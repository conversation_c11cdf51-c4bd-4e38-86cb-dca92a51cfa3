package main

import (
	"flag"
	"os"
	"os/signal"
	"syscall"

	aohlog "github.com/mssfoobar/aoh-golib/logger"
	"github.com/mssfoobar/app/wfe/internal/config"
	_ "github.com/mssfoobar/app/wfe/internal/model/sqlplugin/postgresql" // needed to load postgresql plugin
	"github.com/mssfoobar/app/wfe/internal/wfe"
	"go.uber.org/zap"
)

func main() {
	var envFile string
	flag.StringVar(&envFile, "e", "./.env", "environment file")
	flag.Parse()

	if envFile == "" {
		flag.PrintDefaults()
		return
	}

	if err := config.Init(envFile); err != nil {
		aohlog.Fatal("unable to initialize config", zap.Error(err))
	}

	cfg := config.AppConfig
	if cfg.LogLevel == "debug" {
		aohlog.SetDevelopment()
	}

	w := wfe.New()
	w.Start(*cfg)

	defer w.Stop()

	// Press Ctrl+C to exit the process
	quitCh := make(chan os.Signal, 1)
	signal.Notify(quitCh, os.Interrupt, syscall.SIGTERM)
	<-quitCh
}
