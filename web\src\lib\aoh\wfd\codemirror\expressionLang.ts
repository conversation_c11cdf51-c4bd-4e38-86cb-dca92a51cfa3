// This file was generated by lezer-generator. You probably shouldn't edit it.
import { LRParser } from "@lezer/lr";
const spec_Identifier: Record<string, number | null> = {
	__proto__: null,
	true: 12,
	false: 12,
	nil: 14,
	matches: 16,
	not: 16,
	and: 16,
	or: 16,
	in: 16,
	contains: 18,
	startsWith: 18,
	endWith: 18,
};
export const parser = LRParser.deserialize({
	version: 14,
	states: "%WQVQPOOOwQPO'#CpO!xQPO'#ChO#SQPO'#CiOOQO'#Cp'#CpOOQO'#Cj'#CjQVQPOOO#XQQO,59QO#aQPO'#CkO#kQPO'#CtO#kQPO'#CtO#sQPO,59SO#xQPO'#CwO#}QPO,59TOOQO-E6h-E6hOOQO1G.l1G.lOVQPO1G.lOOQO,59V,59VOOQO-E6i-E6iO$SQPO,59`OOQO1G.n1G.nOVQPO,59cOOQO1G.o1G.oO$[QPO7+$WO$aQPO1G.}OOQO<<Gr<<GrO$iQPO'#ClO$nQPO7+$iO$vQPO,59WOOQO-E6j-E6jOVQPO1G.rOOQO7+$^7+$^",
	stateData:
		"${~OcOS~OQSORSOSSOTPOUSOVSOWSOXSOfQOjRO~OeVOQdXRdXSdXTdXUdXVdXWdXXdXadXfdXjdXgdXidXmdX~OiWOghP~PVOT[O~OZ_Of`O~Og_Xi_X~PVOiWOghX~OgdO~OleO~OmfO~OiWOgha~OgiO~OijOmki~OTlO~OijOmkq~OlnO~O",
	goto: "!wlPPPPPPPPPPmPmmv|!WPPP!^PPP!qPP!t_SOQUW`enQUOR^UQXQSbXcRcYQkhRmkSTOUQYQQaWQg`QheRonRZQR]R",
	nodeNames:
		"⚠ Expression Number String Operator Identifier BooleanLiteral Nil Keyword StringOperator MemberExpression PropertyName ArrayExpression ObjectPattern",
	maxTerm: 29,
	skippedNodes: [0],
	repeatNodeCount: 3,
	tokenData:
		"+Z~RsXY#`Z[#`pq#`qr#qrs$Otu%vuv#yvw&mwx&sz{(f{|#y|}(n}!O#y!O!P(s!P!Q#y!Q!R({!R![)i![!]*`!^!_#q!_!`#q!`!a#q!a!b*e!c!}%v!}#O*m#P#Q*r#Q#R#y#R#S%v#T#o%v#o#p*w#p#q*|#q#r+U$f$g#`$g;'S%v;'S;=`&g<%lO%v~#eSc~XY#`Z[#`pq#`$f$g#`~#vPS~!_!`#y~$OOS~~$TWR~OY$OZr$Ors$ms#O$O#O#P$r#P;'S$O;'S;=`%p<%lO$O~$rOR~~$uRO;'S$O;'S;=`%O;=`O$O~%TXR~OY$OZr$Ors$ms#O$O#O#P$r#P;'S$O;'S;=`%p;=`<%l$O<%lO$O~%sP;=`<%l$OR%}WTPZQtu%v!Q![%v!c!}%v#R#S%v#T#o%v$g;'S%v;'S;=`&g<%lO%vR&jP;=`<%l%v~&pPvw#y~&xWR~OY&sZw&swx$mx#O&s#O#P'b#P;'S&s;'S;=`(`<%lO&s~'eRO;'S&s;'S;=`'n;=`O&s~'sXR~OY&sZw&swx$mx#O&s#O#P'b#P;'S&s;'S;=`(`;=`<%l&s<%lO&s~(cP;=`<%l&s~(kPS~z{#y~(sOi~~(xPe~!O!P#y~)QRQ~!O!P)Z!Q![)i#l#m)t~)^P!Q![)a~)fPQ~!Q![)a~)nQQ~!O!P)Z!Q![)i~)wR!Q![*Q!c!i*Q#T#Z*Q~*VRQ~!Q![*Q!c!i*Q#T#Z*Q~*eOl~~*jPS~!a!b#y~*rOf~~*wOg~~*|Oj~~+RPS~#p#q#y~+ZOm~",
	tokenizers: [0, 1],
	topRules: { Expression: [0, 1] },
	specialized: [{ term: 5, get: (value: string) => spec_Identifier[value] || -1 }],
	tokenPrec: 0,
});
