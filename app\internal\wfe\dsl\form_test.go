package dsl

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func Test_bindData(t *testing.T) {
	form := FormSchema{
		Components: []Component{
			{
				Id:   "table",
				Type: FormTable,
			},
			{
				Type: FormCheckList,
				Key:  "checklist",
			},
			{
				Type: FormRadio,
				Key:  "radio",
			},
			{
				Type: FormSelect,
				Key:  "select",
			},
			{
				Type: FormTagList,
				Key:  "taglist",
			},
		},
	}
	input := map[string]any{
		"table":     struct{}{},
		"checklist": struct{}{},
		"radio":     struct{}{},
		"select":    struct{}{},
		"taglist":   struct{}{},
	}
	form.bindData(input)
	require.Equal(t, "="+form.Components[0].Id, form.Components[0].DataSource)
	require.Equal(t, "="+form.Components[1].Key, form.Components[1].ValuesKey)
	require.Equal(t, "="+form.Components[2].Key, form.Components[2].ValuesKey)
	require.Equal(t, "="+form.Components[3].Key, form.Components[3].ValuesKey)
	require.Equal(t, "="+form.Components[4].Key, form.Components[4].ValuesKey)
}

func Test_createFormInput(t *testing.T) {
	input := createFormInput(
		"test",
		[]any{"a", []any{"b", "c"}, []any{1, 2}},
		[]string{"test_1", "test_2", "test_3"},
	)
	require.Equal(t, map[string]any{
		"1": "a",
		"2": []Value{{
			Label: "b",
			Value: "b",
		}, {
			Label: "c",
			Value: "c",
		}},
		"3": []any{1, 2},
	}, input)
}
