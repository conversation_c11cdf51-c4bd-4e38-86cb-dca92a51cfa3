import type { dia } from "rappid/rappid";

export type DialogError = {
	message: string;
	errorCode: string;
};

enum ErrorType {
	Cell,
	MsgOnly,
}

export class GraphError extends Error {
	private type: ErrorType;
	private element?: dia.Element;

	private constructor(msg: string, type: ErrorType, el?: dia.Element) {
		super(msg);

		Object.setPrototypeOf(this, GraphError.prototype);

		this.type = type;
		this.element = el;
	}

	static createCellError(msg: string, cell: dia.Element): GraphError {
		return new GraphError(msg, ErrorType.Cell, cell);
	}

	static createMsgError(msg: string): GraphError {
		return new GraphError(msg, ErrorType.MsgOnly);
	}

	/**
	 * add cell information and turn error into cell type
	 * @param cell - cell
	 */
	addCell(cell: dia.Element) {
		this.element = cell;
		this.type = ErrorType.Cell;
	}

	/**
	 * Check if message is a message only error (error without a cell or state embedded in)
	 * @returns boolean indicates error type is msg only or not
	 */
	IsMsgOnly(): boolean {
		return this.type === ErrorType.MsgOnly;
	}

	/**
	 * @returns error message with cell name if a cell is provided and has a cell name
	 */
	getMessage(): string {
		let result = this.message;
		if (!this.IsMsgOnly()) {
			result = this.element?.attr("label/text") + ": " + result;
		}
		return result;
	}
}

export class InputValidatorError extends Error {
	constructor(msg: string) {
		super(msg);
		Object.setPrototypeOf(this, InputValidatorError.prototype);
	}
}
