import type { ConditionalBasicOperator } from "$lib/aoh/wfd/bpmn/dsl/schema";

// Generate download link
export const downloadFile = (blob: Blob, fileName: string) => {
	const url = URL.createObjectURL(blob);

	const link = document.createElement("a");
	link.href = url;
	link.download = fileName;
	document.body.appendChild(link);

	link.click();

	URL.revokeObjectURL(url);
	link.remove();
};

export const condOperator: Record<ConditionalBasicOperator, string> = {
	EQ: "==",
	NEQ: "!=",
	MT: ">",
	LT: "<",
	LTE: "<=",
	MTE: ">=",
} as const;
