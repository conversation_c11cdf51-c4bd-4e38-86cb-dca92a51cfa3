package model

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/google/uuid"
	"github.com/mssfoobar/app/wfe/internal/model/sqlplugin"
)

const (
	errDuplicateWorkflowTemplate       = "workflow template with name '%s' already exists"
	errWorkflowTemplateNotFound        = "workflow template id '%s' not found"
	errWorkflowTemplateOccLockMismatch = "workflow template id '%s' occ lock mismatch"
	errWorkflowTemplateIsPublished     = "workflow template id '%s' is already published"
)

type sqlWorkflowTemplate struct {
	Store
}

func newSqlWorkflowTemplate(db sqlplugin.DB) *sqlWorkflowTemplate {
	return &sqlWorkflowTemplate{Store: NewStore(db)}
}

func (s sqlWorkflowTemplate) SaveWorkflowTemplate(
	ctx context.Context,
	request *WorkflowTemplateRequest,
) (*WorkflowTemplateResponse, error) {
	var resp *WorkflowTemplateResponse
	err := s.txExecute(ctx, "SaveWorkflowTemplate", func(tx sqlplugin.Tx) error {
		currRow, err := tx.SelectFromWorkflowTemplate(ctx, sqlplugin.WorkflowTemplateFilter{
			Name:     &request.Name,
			TenantId: request.TenantId,
		})
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return err
		}

		var id uuid.UUID
		if currRow == nil {
			id = uuid.New()
			_, err = tx.InsertIntoWorkflowTemplate(ctx, &sqlplugin.WorkflowTemplateRow{
				Id:           id,
				Name:         request.Name,
				WorkflowJson: request.WorkflowJson,
				DesignerJson: request.DesignerJson,
				Editable:     true,
				CreatedBy:    request.Requester,
				UpdatedBy:    request.Requester,
				TenantId:     request.TenantId,
			})
		} else if currRow.Editable {
			id = currRow.Id
			_, err = tx.UpdateWorkflowTemplate(ctx, &sqlplugin.WorkflowTemplateRow{
				Id:           currRow.Id,
				Name:         request.Name,
				WorkflowJson: request.WorkflowJson,
				DesignerJson: request.DesignerJson,
				Editable:     true,
				UpdatedBy:    request.Requester,
				TenantId:     request.TenantId,
				OccLock:      request.OccLock,
			})
		} else {
			return NewStoreError(fmt.Sprintf(errWorkflowTemplateIsPublished, currRow.Id))
		}

		if err != nil {
			switch {
			case s.Db.IsDupEntryError(err):
				return NewStoreError(fmt.Sprintf(errDuplicateWorkflowTemplate, request.Name))
			case s.Db.IsExceptionError(err):
				return NewStoreError(fmt.Sprintf(errWorkflowTemplateOccLockMismatch, currRow.Id))
			default:
				return err
			}
		}

		resp, err = tx.SelectFromWorkflowTemplate(ctx, sqlplugin.WorkflowTemplateFilter{
			Id:       &id,
			TenantId: request.TenantId,
		})

		return err
	})

	return resp, err
}

func (s sqlWorkflowTemplate) PublishWorkflowTemplate(
	ctx context.Context,
	request *WorkflowTemplateRequest,
) (*WorkflowTemplateResponse, error) {
	var resp *WorkflowTemplateResponse
	err := s.txExecute(ctx, "PublishWorkflowTemplate", func(tx sqlplugin.Tx) error {
		currRow, err := tx.SelectFromWorkflowTemplate(ctx, sqlplugin.WorkflowTemplateFilter{
			Name:     &request.Name,
			TenantId: request.TenantId,
		})
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return err
		}

		var id uuid.UUID
		if currRow == nil {
			id = uuid.New()
			_, err = tx.InsertIntoWorkflowTemplate(ctx, &sqlplugin.WorkflowTemplateRow{
				Id:           id,
				Name:         request.Name,
				WorkflowJson: request.WorkflowJson,
				DesignerJson: request.DesignerJson,
				Editable:     false,
				CreatedBy:    request.Requester,
				TenantId:     request.TenantId,
			})
		} else if currRow.Editable {
			id = currRow.Id
			_, err = tx.UpdateWorkflowTemplate(ctx, &sqlplugin.WorkflowTemplateRow{
				Id:           currRow.Id,
				Name:         request.Name,
				WorkflowJson: request.WorkflowJson,
				DesignerJson: request.DesignerJson,
				Editable:     false,
				UpdatedBy:    request.Requester,
				TenantId:     request.TenantId,
				OccLock:      request.OccLock,
			})
		} else {
			return NewStoreError(fmt.Sprintf(errWorkflowTemplateIsPublished, currRow.Id))
		}

		if err != nil {
			switch {
			case s.Db.IsDupEntryError(err):
				return NewStoreError(fmt.Sprintf(errDuplicateWorkflowTemplate, request.Name))
			case s.Db.IsExceptionError(err):
				return NewStoreError(fmt.Sprintf(errWorkflowTemplateOccLockMismatch, currRow.Id))
			default:
				return err
			}
		}

		resp, err = tx.SelectFromWorkflowTemplate(ctx, sqlplugin.WorkflowTemplateFilter{
			Id:       &id,
			TenantId: request.TenantId,
		})

		return err
	})

	return resp, err
}

func (s sqlWorkflowTemplate) GetWorkflowTemplate(
	ctx context.Context,
	request *GetWorkflowTemplateRequest,
) (*WorkflowTemplateResponse, error) {
	row, err := s.Db.SelectFromWorkflowTemplate(ctx, sqlplugin.WorkflowTemplateFilter{
		Id:       &request.Id,
		TenantId: request.TenantId,
	})
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, NewStoreError(fmt.Sprintf(errWorkflowTemplateNotFound, request.Id))
		}
		return nil, err
	}

	return row, nil
}

func (s sqlWorkflowTemplate) ListWorkflowTemplate(
	ctx context.Context,
	request *ListWorkflowTemplateRequest,
) (*ListWorkflowTemplateResponse, error) {
	var rows []sqlplugin.WorkflowTemplateRow
	var total int
	var err error
	err = s.txExecute(ctx, "ListWorkflowTemplate", func(tx sqlplugin.Tx) error {
		rows, err = s.Db.ListFromWorkflowTemplate(ctx, sqlplugin.WorkflowTemplatePaginateFilter{
			TenantId: request.TenantId,
			Limit:    request.Page.Size,
			Offset:   request.Page.Size * (request.Page.Number - 1),
			OrderBy:  request.Page.Sorts.String(),
		})
		if err != nil {
			if s.Db.IsColumnNotExistError(err) {
				return NewStoreError("invalid query parameter; " + err.Error())
			}
			return err
		}

		total, err = s.Db.CountFromWorkflowTemplate(ctx, sqlplugin.WorkflowTemplateCountFilter{
			TenantId: request.TenantId,
		})

		return err
	})

	return &ListWorkflowTemplateResponse{
		TotalCount:        total,
		WorkflowTemplates: rows,
	}, err
}

func (s sqlWorkflowTemplate) DeleteWorkflowTemplate(
	ctx context.Context,
	request *DeleteWorkflowTemplateRequest,
) error {
	result, err := s.Db.DeleteFromWorkflowTemplate(ctx, sqlplugin.WorkflowTemplateFilter{
		Id:       &request.Id,
		TenantId: request.TenantId,
	})
	if err != nil {
		return err
	}

	affectedRows, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if affectedRows == 0 {
		return NewStoreError(fmt.Sprintf(errWorkflowTemplateNotFound, request.Id))
	}

	return nil
}
