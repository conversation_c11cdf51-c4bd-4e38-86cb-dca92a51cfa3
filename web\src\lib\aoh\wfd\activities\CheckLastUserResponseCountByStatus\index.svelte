<script lang="ts">
	import { type Activity } from "$lib/aoh/wfd/activitySDK/api";
	import { dia } from "rappid/rappid";
	import Input from "../../components/ui/input/input.svelte";
	import Label from "../../components/ui/label/label.svelte";
	import SelectSearch from "../../components/ui/select/select-search.svelte";
	import Textarea from "../../components/ui/textarea/textarea.svelte";
	import Toggle from "../../components/ui/toggle/toggle.svelte";

	let { activity, graph }: { activity: Activity; graph?: dia.Graph } = $props();

	// Get ALL current elements from the live graph (including unsaved ones)
	let liveGraphElements = $derived(graph ? graph.getElements() : []);
	let liveStateNames = $derived(
		liveGraphElements
			.filter(
				(el) =>
					el.get("type") !== "wf.Start" && el.get("type") !== "wf.End" && el.get("type") !== "wf.Terminate"
			)
			.map((el) => el.attr("label/text") || `Unnamed_${el.id}`)
	);
	const operators = [
		{ value: "==" },
		{ value: "!=" },
		{ value: ">" },
		{ value: ">=" },
		{ value: "<" },
		{ value: "<=" },
	];

	function getValue(key: string): string {
		const val = activity.getParameter(key);
		return typeof val === "string" ? val : "";
	}
</script>

<div class="flex flex-col gap-2">
	<div class="flex flex-col">
		<Label>Select Target Activity</Label>
		<SelectSearch
			listData={liveStateNames || ["No states available"]}
			selectedItem={activity.getParameter("ActivityName") as string}
			onSelect={(value) => {
				setTimeout(() => {
					activity.setParameter("ActivityName", value);
				}, 200);
			}}
		/>
	</div>
	<div class="flex flex-col">
		<div class="flex items-center justify-between">
			<Label>Target Action</Label>
			<Toggle variant="outline" class="italic size-6">Expr</Toggle>
		</div>
		<Textarea
			class="min-w-full min-h-10"
			spellcheck="false"
			value={getValue("TargetAction")}
			onblur={(e) => {
				activity.setParameter("TargetAction", e.currentTarget.value);
			}}
		/>
	</div>
	<div class="flex flex-col">
		<div class="flex items-center justify-between">
			<Label>Operator</Label>
		</div>
		<SelectSearch
			listData={operators.map((op) => op.value)}
			selectedItem={activity.getParameter("Operator") as string}
			onSelect={(value) => {
				activity.setParameter("Operator", value);
			}}
		/>
	</div>
	<div class="flex flex-col">
		<div class="flex items-center justify-between">
			<Label>Target Count</Label>
			<Toggle variant="outline" class="italic size-6">Expr</Toggle>
		</div>
		<Input
			type="number"
			value={getValue("TargetCount")}
			onblur={(e) => {
				activity.setParameter("TargetCount", e.currentTarget.value);
			}}
		/>
	</div>
</div>
