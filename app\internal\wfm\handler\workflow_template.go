package handler

import (
	"encoding/json"
	"errors"
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
	"github.com/google/uuid"
	aohhttp "github.com/mssfoobar/aoh-golib/http"
	aohlog "github.com/mssfoobar/aoh-golib/logger"
	"github.com/mssfoobar/app/wfe/internal/model"
	"github.com/mssfoobar/app/wfe/internal/wfm/handler/util"
	"go.uber.org/zap"
)

type WorkflowTemplate struct {
	store model.WorkflowTemplateStore
}

type (
	workflowTemplateUriParam struct {
		TemplateId string `json:"template_id" validate:"required,uuid"`
	}

	workflowTemplateReqBody struct {
		Name         string          `json:"name"`
		WorkflowJson json.RawMessage `json:"workflow_json"`
		DesignerJson json.RawMessage `json:"designer_json"`
		OccLock      int             `json:"occ_lock,omitempty"`
	}
)

func (r *workflowTemplateReqBody) Bind(_ *http.Request) error {
	if r.Name == "" || r.WorkflowJson == nil || r.DesignerJson == nil {
		return errors.New("missing required fields")
	}
	return nil
}

func RegisterWorkflowTemplate(factory *model.Factory, keycloakUrl string) http.Handler {
	r := chi.NewRouter()
	util.SetMiddleware(r, keycloakUrl)

	w := WorkflowTemplate{
		store: factory.NewWorkflowTemplateStore(),
	}

	r.Route("/", func(r chi.Router) {
		r.Get("/", w.List)
		r.Put("/save", w.Save)
		r.Put("/publish", w.Publish)
		r.Get("/{template_id}", w.Get)
		r.Delete("/{template_id}", w.Delete)
	})

	return r
}

// Save is a http handler to create new workflow template
func (wf *WorkflowTemplate) Save(w http.ResponseWriter, r *http.Request) {
	var (
		req *workflowTemplateReqBody
		jwt *aohhttp.JwtClaim
		err error
	)

	if req, err = getWorkflowTemplateReqBody(w, r); err != nil {
		return
	}

	if jwt, err = util.GetJwt(w, r); err != nil {
		return
	}

	resp, err := wf.store.SaveWorkflowTemplate(r.Context(), &model.WorkflowTemplateRequest{
		Name:         req.Name,
		WorkflowJson: req.WorkflowJson,
		DesignerJson: req.DesignerJson,
		Requester:    jwt.Id,
		TenantId:     jwt.ActiveTenant.Id,
		OccLock:      req.OccLock,
	})
	if err != nil {
		aohlog.Error("[WorkflowTemplate] save workflow template failed", zap.Error(err))
		code, err := util.CheckStoreError(err)
		_ = render.Render(w, r, aohhttp.ErrResponse(code, "", []error{err}))
		return
	}
	aohlog.Info("[WorkflowTemplate] saved workflow template successfully", zap.String("template_id", resp.Id.String()))

	_ = render.Render(w, r, aohhttp.Response(http.StatusOK, "", resp))
}

// Publish is a http handler to create/update workflow template
func (wf *WorkflowTemplate) Publish(w http.ResponseWriter, r *http.Request) {
	var (
		req *workflowTemplateReqBody
		jwt *aohhttp.JwtClaim
		err error
	)

	if req, err = getWorkflowTemplateReqBody(w, r); err != nil {
		return
	}

	if jwt, err = util.GetJwt(w, r); err != nil {
		return
	}

	resp, err := wf.store.PublishWorkflowTemplate(r.Context(), &model.WorkflowTemplateRequest{
		Name:         req.Name,
		WorkflowJson: req.WorkflowJson,
		DesignerJson: req.DesignerJson,
		Requester:    jwt.Id,
		TenantId:     jwt.ActiveTenant.Id,
		OccLock:      req.OccLock,
	})
	if err != nil {
		aohlog.Error("[WorkflowTemplate] publish workflow template failed", zap.Error(err))
		code, err := util.CheckStoreError(err)
		_ = render.Render(w, r, aohhttp.ErrResponse(code, "", []error{err}))
		return
	}
	aohlog.Info("[WorkflowTemplate] published workflow template successfully", zap.String("template_id", resp.Id.String()))

	_ = render.Render(w, r, aohhttp.Response(http.StatusOK, "", resp))
}

// List is a http handler to return paginated workflow template list
func (wf *WorkflowTemplate) List(w http.ResponseWriter, r *http.Request) {
	var (
		page *aohhttp.PageRequest
		jwt  *aohhttp.JwtClaim
		resp *model.ListWorkflowTemplateResponse
		err  error
	)

	page, err = util.GetQueryPagination(w, r)
	if err != nil {
		return
	}

	jwt, err = util.GetJwt(w, r)
	if err != nil {
		return
	}

	resp, err = wf.store.ListWorkflowTemplate(r.Context(), &model.ListWorkflowTemplateRequest{
		Page:     *page,
		TenantId: jwt.ActiveTenant.Id,
	})
	if err != nil {
		aohlog.Error("[WorkflowTemplate] list workflow template failed",
			zap.String("tenant_id", jwt.ActiveTenant.Id),
			zap.Error(err))
		code, err := util.CheckStoreError(err)
		_ = render.Render(w, r, aohhttp.ErrResponse(code, "", []error{err}))
		return
	}

	respData := append(make([]model.WorkflowTemplateResponse, 0), resp.WorkflowTemplates...)
	respPage := aohhttp.PageResponse{
		Number:       page.Number,
		Size:         page.Size,
		TotalRecords: resp.TotalCount,
		Count:        len(respData),
		Sort:         []string{page.Sorts.String()},
	}

	aohlog.Info("[WorkflowTemplate] list workflow template successfully", zap.Int("count", len(respData)))
	_ = render.Render(w, r, aohhttp.PaginationResponse(http.StatusOK, "", respPage, respData))
}

// Get is a http handler to return workflow template
func (wf *WorkflowTemplate) Get(w http.ResponseWriter, r *http.Request) {
	var (
		uri  workflowTemplateUriParam
		jwt  *aohhttp.JwtClaim
		resp *model.WorkflowTemplateResponse
		err  error
	)

	if err = util.ValidateUriParams(w, r, &uri); err != nil {
		return
	}

	if jwt, err = util.GetJwt(w, r); err != nil {
		return
	}

	resp, err = wf.store.GetWorkflowTemplate(r.Context(), &model.GetWorkflowTemplateRequest{
		Id:       uuid.MustParse(uri.TemplateId),
		TenantId: jwt.ActiveTenant.Id,
	})
	if err != nil {
		aohlog.Error("[WorkflowTemplate] get workflow template failed",
			zap.String("template_id", uri.TemplateId),
			zap.String("tenant_id", jwt.ActiveTenant.Id),
			zap.Error(err))
		code, err := util.CheckStoreError(err)
		_ = render.Render(w, r, aohhttp.ErrResponse(code, "", []error{err}))
		return
	}

	aohlog.Info("[WorkflowTemplate] get workflow template successfully", zap.String("template_id", uri.TemplateId))
	_ = render.Render(w, r, aohhttp.Response(http.StatusOK, "", resp))
}

// Delete is a http handler to delete workflow template
func (wf *WorkflowTemplate) Delete(w http.ResponseWriter, r *http.Request) {
	var (
		uri workflowTemplateUriParam
		jwt *aohhttp.JwtClaim
		err error
	)

	if err = util.ValidateUriParams(w, r, &uri); err != nil {
		return
	}

	if jwt, err = util.GetJwt(w, r); err != nil {
		return
	}

	err = wf.store.DeleteWorkflowTemplate(r.Context(), &model.DeleteWorkflowTemplateRequest{
		Id:       uuid.MustParse(uri.TemplateId),
		TenantId: jwt.ActiveTenant.Id,
	})
	if err != nil {
		aohlog.Error("[WorkflowTemplate] delete workflow template failed",
			zap.String("template_id", uri.TemplateId),
			zap.String("tenant_id", jwt.ActiveTenant.Id),
			zap.Error(err))
		code, err := util.CheckStoreError(err)
		_ = render.Render(w, r, aohhttp.ErrResponse(code, "", []error{err}))
		return
	}

	aohlog.Info("[WorkflowTemplate] deleted workflow template successfully", zap.String("template_id", uri.TemplateId))
	_ = render.Render(w, r, aohhttp.Response(http.StatusOK, "", nil))
}

func getWorkflowTemplateReqBody(w http.ResponseWriter, r *http.Request) (*workflowTemplateReqBody, error) {
	var req workflowTemplateReqBody
	if err := render.Bind(r, &req); err != nil {
		code, err := util.RequestBodyError(err)
		_ = render.Render(w, r, aohhttp.ErrResponse(code, "", []error{err}))
		return nil, err
	}
	return &req, nil
}
