package model

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/google/uuid"
	"github.com/mssfoobar/app/wfe/internal/model/sqlplugin"
)

const (
	errDuplicateFormTemplate       = "form template with name '%s' already exists"
	errFormTemplateNotFound        = "form template id '%s' not found"
	errFormTemplateOccLockMismatch = "form template id '%s' occ lock mismatch"
)

type sqlFormTemplate struct {
	Store
}

func newSqlFormTemplate(db sqlplugin.DB) *sqlFormTemplate {
	return &sqlFormTemplate{Store: NewStore(db)}
}

func (s sqlFormTemplate) SaveFormTemplate(
	ctx context.Context,
	request *FormTemplateRequest,
) (*FormTemplateResponse, error) {
	var resp *FormTemplateResponse
	err := s.txExecute(ctx, "SaveFormTemplate", func(tx sqlplugin.Tx) error {
		currRow, err := tx.SelectFromFormTemplate(ctx, sqlplugin.FormTemplateFilter{
			Name:     &request.Name,
			TenantId: request.TenantId,
		})
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return err
		}

		var id uuid.UUID
		if currRow == nil {
			id = uuid.New()
			_, err = tx.InsertIntoFormTemplate(ctx, &sqlplugin.FormTemplateRow{
				Id:            id,
				Name:          request.Name,
				FormJson:      request.FormJson,
				ComponentKeys: request.ComponentKeys,
				CreatedBy:     request.Requester,
				UpdatedBy:     request.Requester,
				TenantId:      request.TenantId,
			})
		} else {
			id = currRow.Id
			_, err = tx.UpdateFormTemplate(ctx, &sqlplugin.FormTemplateRow{
				Id:            currRow.Id,
				Name:          request.Name,
				FormJson:      request.FormJson,
				ComponentKeys: request.ComponentKeys,
				UpdatedBy:     request.Requester,
				TenantId:      request.TenantId,
				OccLock:       request.OccLock,
			})
		}

		if err != nil {
			switch {
			case s.Db.IsDupEntryError(err):
				return NewStoreError(fmt.Sprintf(errDuplicateFormTemplate, request.Name))
			case s.Db.IsExceptionError(err):
				return NewStoreError(fmt.Sprintf(errFormTemplateOccLockMismatch, currRow.Id))
			default:
				return err
			}
		}

		resp, err = tx.SelectFromFormTemplate(ctx, sqlplugin.FormTemplateFilter{
			Id:       &id,
			TenantId: request.TenantId,
		})

		return err
	})

	return resp, err
}

func (s sqlFormTemplate) GetFormTemplate(
	ctx context.Context,
	request *GetFormTemplateRequest,
) (*FormTemplateResponse, error) {
	row, err := s.Db.SelectFromFormTemplate(ctx, sqlplugin.FormTemplateFilter{
		Id:       &request.Id,
		TenantId: request.TenantId,
	})
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, NewStoreError(fmt.Sprintf(errFormTemplateNotFound, request.Id))
		}
		return nil, err
	}

	return row, nil
}

func (s sqlFormTemplate) ListFormTemplate(
	ctx context.Context,
	request *ListFormTemplateRequest,
) (*ListFormTemplateResponse, error) {
	var rows []sqlplugin.FormTemplateRow
	var total int
	var err error
	err = s.txExecute(ctx, "ListForm", func(tx sqlplugin.Tx) error {
		rows, err = s.Db.ListFromFormTemplate(ctx, sqlplugin.FormTemplatePaginateFilter{
			TenantId: request.TenantId,
			Limit:    request.Page.Size,
			Offset:   request.Page.Size * (request.Page.Number - 1),
			OrderBy:  request.Page.Sorts.String(),
		})
		if err != nil {
			if s.Db.IsColumnNotExistError(err) {
				return NewStoreError("invalid query parameter; " + err.Error())
			}
			return err
		}

		total, err = s.Db.CountFromFormTemplate(ctx, sqlplugin.FormTemplateCountFilter{
			TenantId: request.TenantId,
		})

		return err
	})

	return &ListFormTemplateResponse{
		TotalCount:    total,
		FormTemplates: rows,
	}, err
}

func (s sqlFormTemplate) DeleteFormTemplate(
	ctx context.Context,
	request *DeleteFormTemplateRequest,
) error {
	result, err := s.Db.DeleteFromFormTemplate(ctx, sqlplugin.FormTemplateFilter{
		Id:       &request.Id,
		TenantId: request.TenantId,
	})
	if err != nil {
		return err
	}

	affectedRows, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if affectedRows == 0 {
		return NewStoreError(fmt.Sprintf(errFormTemplateNotFound, request.Id))
	}

	return nil
}
