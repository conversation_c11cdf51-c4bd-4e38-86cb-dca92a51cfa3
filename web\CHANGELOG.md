# @mssfoobar/wfe-web

## 2.0.0

### Major Changes

- f0ebf04: Migrate workflow designer to AOH web-base framework

    - Update UI component to use Shadcn-Svelte component library.
    - Replace backend Hasura API integration with new WFE-WFM RESTful APIs.
    - Follow new AOH design system (theming, styling, icon, etc.).

- f0ebf04: Customizable activity UI

    - This update allow frontend developer to customize activity UI in workflow designer property panel. For more details
      on how to use this feature, refer to [documentation](https://mssfoobar.github.io/docs/docs/modules/wfe/development/Activity%20Developer%20Guide/custom_activity_ui).
