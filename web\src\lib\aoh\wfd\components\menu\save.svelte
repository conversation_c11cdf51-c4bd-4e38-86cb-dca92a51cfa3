<script lang="ts" module>
	import { z } from "zod";
	import { dia } from "rappid/rappid";
	import { workflowStore } from "$lib/aoh/wfd/stores/workflows";
	import { workflowData } from "$lib/aoh/wfd/stores/workflowData";

	interface Props {
		graph: dia.Graph;
		publish: boolean;
	}

	export const save_workflow_schema = z.object({
		id: z.string().optional(),
		name: z.string(),
		workflow_json: z.unknown(),
		designer_json: z.unknown(),
		occ_lock: z.number().optional(),
	});

	export type SaveWorkflowFromSchema = z.infer<typeof save_workflow_schema>;
</script>

<script lang="ts">
	import { toast } from "svelte-sonner";
	import { superForm } from "sveltekit-superforms";
	import { zodClient } from "sveltekit-superforms/adapters";
	import * as Dialog from "$lib/aoh/wfd/components/ui/dialog/index.js";
	import * as Form from "$lib/aoh/wfd/components/ui/form/index.js";
	import { Input } from "$lib/aoh/wfd/components/ui/input/index.js";
	import { buttonVariants } from "$lib/aoh/wfd/components/ui/button/index.js";
	import ExportFileBuilder from "$lib/aoh/wfd/exportFileBuilder";
	import { GraphError } from "$lib/aoh/wfd/bpmn/utils/error";
	import type { Workflow } from "$lib/aoh/wfd/types";

	let { graph, publish }: Props = $props();
	let open = $state(false);

	const label = publish ? "Publish" : "Save";
	const action = publish ? "?/publish_workflow" : "?/save_workflow";
	const description = publish
		? "This will publish the current BPMN diagram. Published workflow templates cannot be edited."
		: "This will save the current BPMN diagram.";
	const successMsg = publish ? "Published workflow template" : "Saved workflow template";
	const errorMsg = publish ? "Failed to publish workflow template" : "Failed to save workflow template";

	const super_save_workflow_form = superForm<SaveWorkflowFromSchema>(
		{
			id: "",
			name: "",
			workflow_json: {},
			designer_json: {},
			occ_lock: 0,
		},
		{
			dataType: "json",
			validators: zodClient(save_workflow_schema),
			onSubmit: ({ cancel }) => {
				$save_workflow_form = {
					id: $workflowData.id,
					name: $workflowData.name,
					occ_lock: $workflowData.occ_lock,
					designer_json: graph.toJSON(),
				};
				try {
					const builder = new ExportFileBuilder(graph, $workflowData);
					$save_workflow_form.workflow_json = builder
						.addSchema("workflow")
						.processFiles()
						.getExportData().workflow_json;
				} catch (error) {
					if (error instanceof GraphError) {
						toast.error(error.getMessage());
					} else {
						toast.error("Failed to save workflow");
					}
					cancel();
				}
			},
			onResult: ({ result }) => {
				if (result.type === "success" && result.data) {
					const workflow = result.data.workflow as Workflow;
					const index = $workflowStore.findIndex((w) => w.id === workflow.id);
					if (index !== -1) {
						$workflowStore[index] = workflow;
					} else {
						$workflowStore.push(workflow);
					}
					$workflowData = {
						id: workflow.id,
						name: workflow.name,
						occ_lock: workflow.occ_lock,
					};
					toast.success(successMsg);
				} else {
					toast.error(errorMsg);
				}
				open = false;
			},
		}
	);

	const { form: save_workflow_form, enhance: save_workflow_enhance } = super_save_workflow_form;
</script>

<Dialog.Root bind:open>
	<Dialog.Trigger class={buttonVariants({ variant: "secondary" })}>
		{label}
	</Dialog.Trigger>
	<Dialog.Content class="sm:max-w-[475px]">
		<form method="POST" {action} use:save_workflow_enhance>
			<Dialog.Header>
				<Dialog.Title>{label}?</Dialog.Title>
				<Dialog.Description>
					{description}
				</Dialog.Description>
			</Dialog.Header>
			<Form.Field form={super_save_workflow_form} name="name" class="py-4">
				<Form.Control>
					<Form.Label>Name</Form.Label>
					<Input bind:value={$workflowData.name} type="text" spellcheck="false" required />
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
			<Dialog.Footer>
				<Form.Button>{label}</Form.Button>
			</Dialog.Footer>
		</form>
	</Dialog.Content>
</Dialog.Root>
