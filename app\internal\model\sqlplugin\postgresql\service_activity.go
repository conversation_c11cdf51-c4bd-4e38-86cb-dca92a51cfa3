package postgresql

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/mssfoobar/app/wfe/internal/model/sqlplugin"
)

const (
	createServiceActivityQuery = `INSERT INTO service_activity (id, service_name, activity_type, activity_icon, 
								activity_param, activity_result, timeout_in_second) 
								VALUES ($1, $2, $3, $4, $5, $6, $7)`

	updateServiceActivityQuery = `UPDATE service_activity SET service_name = $1, activity_type = $2, activity_icon = $3,
								activity_param = $4, activity_result = $5, timeout_in_second = $6 WHERE id = $7`

	getServiceActivityQuery = `SELECT * FROM service_activity WHERE id = $1`

	deleteServiceActivityByIdQuery = `DELETE FROM service_activity WHERE id = $1`

	listServiceActivity        = `SELECT * FROM service_activity LIMIT $1 OFFSET $2`
	listServiceActivityOrderBy = `SELECT * FROM service_activity ORDER BY %s LIMIT $1 OFFSET $2`

	getTotalCountServiceActivity = `SELECT COUNT(*) FROM service_activity`
)

func (pdb *db) InsertIntoServiceActivity(
	ctx context.Context,
	rows *sqlplugin.ServiceActivityRow,
) (sql.Result, error) {
	return pdb.conn.ExecContext(
		ctx,
		createServiceActivityQuery,
		rows.Id,
		rows.ServiceName,
		rows.ActivityType,
		rows.ActivityIcon,
		rows.ActivityParam,
		rows.ActivityResult,
		rows.TimeoutInSecond,
	)
}

func (pdb *db) UpdateServiceActivity(
	ctx context.Context,
	rows *sqlplugin.ServiceActivityRow,
) (sql.Result, error) {
	return pdb.conn.ExecContext(
		ctx,
		updateServiceActivityQuery,
		rows.ServiceName,
		rows.ActivityType,
		rows.ActivityIcon,
		rows.ActivityParam,
		rows.ActivityResult,
		rows.TimeoutInSecond,
		rows.Id,
	)
}

func (pdb *db) SelectFromServiceActivity(
	ctx context.Context,
	filter sqlplugin.ServiceActivityFilter,
) (*sqlplugin.ServiceActivityRow, error) {
	var row sqlplugin.ServiceActivityRow
	err := pdb.conn.GetContext(ctx, &row, getServiceActivityQuery, filter.Id)
	return &row, err
}

func (pdb *db) ListFromServiceActivity(
	ctx context.Context,
	filter sqlplugin.ServiceActivityPaginateFilter,
) ([]sqlplugin.ServiceActivityRow, error) {
	switch {
	case filter.OrderBy != "":
		return pdb.listFromServiceActivityOrderBy(ctx, filter)
	default:
		return pdb.listFromServiceActivity(ctx, filter)
	}
}

func (pdb *db) listFromServiceActivity(
	ctx context.Context,
	filter sqlplugin.ServiceActivityPaginateFilter,
) ([]sqlplugin.ServiceActivityRow, error) {
	var rows []sqlplugin.ServiceActivityRow
	err := pdb.conn.SelectContext(ctx,
		&rows,
		listServiceActivity,
		filter.Limit,
		filter.Offset,
	)
	return rows, err
}

func (pdb *db) listFromServiceActivityOrderBy(
	ctx context.Context,
	filter sqlplugin.ServiceActivityPaginateFilter,
) ([]sqlplugin.ServiceActivityRow, error) {
	var rows []sqlplugin.ServiceActivityRow
	err := pdb.conn.SelectContext(ctx,
		&rows,
		fmt.Sprintf(listServiceActivityOrderBy, filter.OrderBy),
		filter.Limit,
		filter.Offset,
	)
	return rows, err
}

func (pdb *db) DeleteFromServiceActivity(
	ctx context.Context,
	filter sqlplugin.ServiceActivityFilter,
) (sql.Result, error) {
	return pdb.conn.ExecContext(ctx, deleteServiceActivityByIdQuery, filter.Id)
}

func (pdb *db) CountFromServiceActivity(
	ctx context.Context,
) (int, error) {
	var total int
	err := pdb.conn.GetContext(ctx, &total, getTotalCountServiceActivity)
	return total, err
}
