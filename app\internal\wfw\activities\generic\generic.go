package generic

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/smtp"
	"strings"
	"time"

	"github.com/mssfoobar/app/wfe/internal/config"
	durationParser "github.com/sosodev/duration"
	"go.temporal.io/sdk/activity"
)

const IanNotificationEndpoint = `%s/v1/messages`

type (
	Activities struct {
		smtp    SMTP
		aiModel AIModel
		ian     In<PERSON>pp
	}

	Option struct {
		SMTP config.Smtp
		GPT  config.ChatGpt
		IAN  config.Ian
	}
)

// SMTP interface and implementation
type (
	SMTP interface {
		SendMail(to []string, msg []byte) error
	}

	SMTPImpl struct {
		config.Smtp
	}
)

// ChatGPT types
type (
	AIModel interface {
		Chat(ctx context.Context, prompt string) (string, error)
	}

	ChatGPT struct {
		config.ChatGpt
	}

	ChatGPTResponse struct {
		Choices []struct {
			Message struct {
				Content string `json:"content,omitempty"`
			}
		}
	}
)

// InAppNotification types
type (
	InApp interface {
		Send(ctx context.Context, request InAppRequest) (any, error)
	}

	InAppImpl struct {
		endpoint string
	}

	InAppRequest struct {
		Title       string   `json:"title"`
		Body        string   `json:"body"`
		SenderId    string   `json:"sender_id"`
		ReceiverIds []string `json:"receiver_ids"`
		TenantId    string   `json:"tenant_id"`
	}
)

func New(opt Option) *Activities {
	return &Activities{
		smtp:    &SMTPImpl{opt.SMTP},
		aiModel: &ChatGPT{opt.GPT},
		ian:     &InAppImpl{endpoint: fmt.Sprintf(IanNotificationEndpoint, opt.IAN.Url)},
	}
}

// HttpCall take in 'method', 'endpoint' and 'payload' as input
// Successful execution return http response
func (a *Activities) HttpCall(ctx context.Context, endpoint, method, payload string) (interface{}, error) {
	client := &http.Client{}
	req, err := http.NewRequest(method, endpoint, bytes.NewReader([]byte(payload)))
	if err != nil {
		return nil, err
	}

	// heartbeat report before long-running process
	activity.RecordHeartbeat(ctx)

	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}

	activity.RecordHeartbeat(ctx)

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}
	defer func() {
		_ = res.Body.Close()
	}()

	var result interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, err
	}

	// catch cancellation from workflow by checking ctx.Done
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	return result, nil
}

// Delay take parameter 'second' in time duration in second
func (a *Activities) Delay(ctx context.Context, second float64) error {
	delay := time.Duration(second) * time.Second
	elapsedDuration := time.Duration(0)
	for elapsedDuration < delay {
		time.Sleep(time.Second)
		elapsedDuration += time.Second
		activity.RecordHeartbeat(ctx)
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}
	}
	return nil
}

// Timer take parameter 'duration' in ISO8601 format
func (a *Activities) Timer(ctx context.Context, duration string) error {
	p, err := durationParser.Parse(duration)
	if err != nil {
		return errors.New("invalid ISO8601 format")
	}
	elapsedDuration := time.Nanosecond
	for elapsedDuration < p.ToTimeDuration() {
		time.Sleep(time.Second)
		elapsedDuration += time.Second
		activity.RecordHeartbeat(ctx)
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}
	}
	return nil
}

// SendEmail use gmail smtp to send email. it has three parameters SendTo, Subject and Message.
// To send multiple email address, set value of SendTo with comma-delimited email addresses.
func (a *Activities) SendEmail(_ context.Context, to, subject, body string) error {
	toEmails := strings.Split(to, ",")
	message := []byte(
		fmt.Sprintf(
			"To: %s\r\n",
			to,
		) + fmt.Sprintf(
			"Subject: %s\r\n\r\n",
			subject,
		) + body,
	)

	return a.smtp.SendMail(toEmails, message)
}

func (s *SMTPImpl) SendMail(to []string, msg []byte) error {
	auth := smtp.PlainAuth("", s.Username, s.Password, s.Host)
	return smtp.SendMail(s.Host+":"+s.Port, auth, s.From, to, msg)
}

func (a *Activities) AskChatGPT(ctx context.Context, prompt string) (string, error) {
	return a.aiModel.Chat(ctx, prompt)
}

func (g *ChatGPT) Chat(ctx context.Context, prompt string) (string, error) {
	request := map[string]interface{}{
		"model": "gpt-3.5-turbo", "messages": []interface{}{
			map[string]interface{}{
				"role":    "system",
				"content": prompt,
			},
		},
	}

	b, err := json.Marshal(request)
	if err != nil {
		return "", err
	}

	req, err := http.NewRequest(http.MethodPost, g.Url, bytes.NewReader(b))
	if err != nil {
		return "", err
	}
	req.Header.Add("Authorization", "Bearer "+g.ApiKey)
	req.Header.Add("Content-Type", "application/json")

	// heartbeat report before long-running process
	activity.RecordHeartbeat(ctx)

	client := http.Client{}
	res, err := client.Do(req)
	if err != nil {
		return "", err
	}

	activity.RecordHeartbeat(ctx)

	response, err := io.ReadAll(res.Body)
	if err != nil {
		return "", err
	}
	defer func() {
		_ = res.Body.Close()
	}()

	var data ChatGPTResponse
	err = json.Unmarshal(response, &data)
	if err != nil {
		return "", err
	}

	if len(data.Choices) == 0 {
		return "", errors.New("no chatGPT response data")
	}

	// catch cancellation from workflow by checking ctx.Done
	select {
	case <-ctx.Done():
		return "", ctx.Err()
	default:
	}

	return data.Choices[0].Message.Content, nil
}

// InAppNotification call IAN endpoint to send in-app notification
func (a *Activities) InAppNotification(
	ctx context.Context,
	senderId, receiverId, tenantId, title, body string,
) (any, error) {
	return a.ian.Send(ctx, InAppRequest{
		SenderId:    senderId,
		ReceiverIds: []string{receiverId},
		TenantId:    tenantId,
		Title:       title,
		Body:        body,
	})
}

func (i *InAppImpl) Send(ctx context.Context, request InAppRequest) (any, error) {
	client := &http.Client{}

	payload, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest(http.MethodPost, i.endpoint, bytes.NewReader(payload))
	if err != nil {
		return nil, err
	}

	// heartbeat report before long-running process
	activity.RecordHeartbeat(ctx)

	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}

	activity.RecordHeartbeat(ctx)

	resBody, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}
	defer func() {
		_ = res.Body.Close()
	}()

	var result any
	if err := json.Unmarshal(resBody, &result); err != nil {
		return nil, err
	}

	// catch cancellation from workflow by checking ctx.Done
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	return result, nil
}
