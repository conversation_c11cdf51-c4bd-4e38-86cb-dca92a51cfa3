import type { RequestHandler } from "./$types";

export type HealthData = {
	uptime: number;
	version: string;
	memoryUsage: NodeJS.MemoryUsage;
	message: string;
};

export const GET: RequestHandler = async () => {
	const responseBody: HTTPResponseBody<HealthData> = {
		data: {
			uptime: process.uptime(),
			version: process.version,
			memoryUsage: process.memoryUsage(),
			message: "NodeJS Process Details",
		},
		message: "Success",
		sent_at: new Date().toISOString(),
	};

	return new Response(JSON.stringify(responseBody));
};
