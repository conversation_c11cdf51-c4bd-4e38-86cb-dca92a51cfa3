import type { Actions, ServerLoad } from "@sveltejs/kit";
import { env } from "$env/dynamic/private";
import { fail, superValidate } from "sveltekit-superforms";
import { zod } from "sveltekit-superforms/adapters";
import { StatusCodes } from "http-status-codes";
import { save_workflow_schema } from "$lib/aoh/wfd/components/menu/save.svelte";
// import { log as logger } from "$lib/aoh/core/logger/Logger";
import type { Workflow, Activity, Form } from "./types";
import { validate_condition_schema } from "./components/inspector/conditional/ConditionField.svelte";

// const log = logger.child({ src: new URL(import.meta.url).pathname });

export const load: ServerLoad = async ({ locals }) => {
	if (!locals.authResult.success) {
		return new Response(
			JSON.stringify({
				message: "Unauthorized",
			}),
			{
				status: StatusCodes.UNAUTHORIZED,
			}
		);
	}

	const bearerAuthorizationString = `Bearer ${locals.authResult.access_token}`;

	const headers = {
		"Content-Type": "application/json",
		Authorization: bearerAuthorizationString,
	};

	const workflows_promise = fetch(env.WFM_URL + "/v1/workflow_template?size=999", {
		method: "GET",
		headers,
	});

	const activities_promise = fetch(env.WFM_URL + "/v1/service_activity?size=999", {
		method: "GET",
		headers,
	});

	const events_promise = fetch(env.WFM_URL + "/v1/service_event?size=999", {
		method: "GET",
		headers,
	});

	const forms_promise = fetch(env.WFM_URL + "/v1/form_template?size=999", {
		method: "GET",
		headers,
	});

	let workflows: Array<Workflow> = [];
	let activities: Array<Activity> = [];
	let events: Array<Event> = [];
	let forms: Array<Form> = [];

	try {
		const [workflows_response, activities_response, events_response, forms_response] = await Promise.all([
			workflows_promise,
			activities_promise,
			events_promise,
			forms_promise,
		]);

		if (!workflows_response.ok) {
			// log.error(`Error - unable to get workflows: ${workflows_response.statusText}`);
		} else {
			const workflows_result = await workflows_response.json();
			workflows = workflows_result.data as Array<Workflow>;
		}

		if (!activities_response.ok) {
			// log.error(`Error - unable to get activities: ${activities_response.statusText}`);
		} else {
			const activities_result = await activities_response.json();
			activities = activities_result.data as Array<Activity>;
		}

		if (!events_response.ok) {
			// log.error(`Error - unable to get events: ${events_response.statusText}`);
		} else {
			const events_result = await events_response.json();
			events = events_result.data as Array<Event>;
		}

		if (!forms_response.ok) {
			// log.error(`Error - unable to get forms: ${forms_response.statusText}`);
		} else {
			const forms_result = await forms_response.json();
			forms = forms_result.data as Array<Form>;
		}
	} catch (error) {
		// log.error({ error }, "Error fetching workflows, activities and forms");
	}

	return {
		workflows,
		activities,
		events,
		forms,
	};
};

export const actions: Actions = {
	save_workflow: async ({ request, fetch, locals }) => {
		if (!locals.authResult.success) {
			return new Response(
				JSON.stringify({
					message: "Unauthorized",
				}),
				{
					status: StatusCodes.UNAUTHORIZED,
				}
			);
		}

		const bearerAuthorizationString = `Bearer ${locals.authResult.access_token}`;

		const headers = {
			"Content-Type": "application/json",
			Authorization: bearerAuthorizationString,
		};

		const formData = await request.formData();

		const form = await superValidate(formData, zod(save_workflow_schema));

		if (!form.valid) return fail(400, form);

		const response = await fetch(`${env.WFM_URL}/v1/workflow_template/save`, {
			method: "PUT",
			body: JSON.stringify(form.data),
			headers,
		});

		if (!response.ok) {
			return fail(400, { form });
		}

		const result = await response.json();
		const workflow = result.data;
		return { form, workflow };
	},
	publish_workflow: async ({ request, fetch, locals }) => {
		if (!locals.authResult.success) {
			return new Response(
				JSON.stringify({
					message: "Unauthorized",
				}),
				{
					status: StatusCodes.UNAUTHORIZED,
				}
			);
		}

		const bearerAuthorizationString = `Bearer ${locals.authResult.access_token}`;

		const headers = {
			"Content-Type": "application/json",
			Authorization: bearerAuthorizationString,
		};

		const formData = await request.formData();

		const form = await superValidate(formData, zod(save_workflow_schema));

		if (!form.valid) return fail(400, form);

		const response = await fetch(`${env.WFM_URL}/v1/workflow_template/publish`, {
			method: "PUT",
			body: JSON.stringify(form.data),
			headers,
		});

		if (!response.ok) {
			return fail(400, { form });
		}

		const result = await response.json();
		const workflow = result.data;
		return { form, workflow };
	},
	validate_condition: async ({ request, fetch, locals }) => {
		if (!locals.authResult.success) {
			return new Response(
				JSON.stringify({
					message: "Unauthorized",
				}),
				{
					status: StatusCodes.UNAUTHORIZED,
				}
			);
		}

		const bearerAuthorizationString = `Bearer ${locals.authResult.access_token}`;

		const headers = {
			"Content-Type": "application/json",
			Authorization: bearerAuthorizationString,
		};

		const formData = await request.formData();

		const form = await superValidate(formData, zod(validate_condition_schema));

		if (!form.valid) return fail(400, form);

		const response = await fetch(`${env.WFM_URL}/v1/validate/condition`, {
			method: "POST",
			body: JSON.stringify(form.data),
			headers,
		});

		const validate = await response.json();

		return { form, validate };
	},
	validate_expression: async ({ request, fetch, locals }) => {
		if (!locals.authResult.success) {
			return new Response(
				JSON.stringify({
					message: "Unauthorized",
				}),
				{
					status: StatusCodes.UNAUTHORIZED,
				}
			);
		}

		const bearerAuthorizationString = `Bearer ${locals.authResult.access_token}`;

		const headers = {
			"Content-Type": "application/json",
			Authorization: bearerAuthorizationString,
		};

		const formData = await request.formData();

		const form = await superValidate(formData, zod(validate_condition_schema));

		if (!form.valid) return fail(400, form);

		const response = await fetch(`${env.WFM_URL}/v1/validate/expression`, {
			method: "POST",
			body: JSON.stringify(form.data),
			headers,
		});

		const validate = await response.json();

		return { form, validate };
	},
};
