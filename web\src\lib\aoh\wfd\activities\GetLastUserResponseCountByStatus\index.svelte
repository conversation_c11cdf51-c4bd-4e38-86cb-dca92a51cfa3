<script lang="ts">
	import { type Activity } from "$lib/aoh/wfd/activitySDK/api";
	import { dia } from "rappid/rappid";
	import Label from "../../components/ui/label/label.svelte";
	import SelectSearch from "../../components/ui/select/select-search.svelte";
	import Textarea from "../../components/ui/textarea/textarea.svelte";
	import Toggle from "../../components/ui/toggle/toggle.svelte";
	import Tooltip from "$lib/components/ui/tooltip/tooltip.svelte";

	let { activity, graph }: { activity: Activity; graph?: dia.Graph } = $props();

	// Get ALL current elements from the live graph (including unsaved ones)
	let liveGraphElements = $derived(graph ? graph.getElements() : []);
	let liveStateNames = $derived(
		liveGraphElements
			.filter(
				(el) =>
					el.attr("type/text") === "SendTeamNotiMOHIncidentReport" ||
					el.attr("type/text") === "SendTeamChannelNotiMOHIncidentReport"
			)
			.map((el) => el.attr("label/text") || `Unnamed_${el.id}`)
	);

	const saveParam = (key: string, value: number) => {
		if (value < 0 || !Number.isInteger(value)) {
			value = Math.max(0, Math.floor(value));
		}
		activity.setParameter(key, value);
	};
	function getTargetAction(): string {
		const val = activity.getParameter("TargetAction");
		return typeof val === "string" ? val : "";
	}
</script>

<div class="flex flex-col gap-2">
	<div class="flex flex-col">
		<Label>Select Target Activity</Label>
		<div class="flex">
			<SelectSearch
				listData={liveStateNames || ["No states available"]}
				selectedItem={activity.getParameter("ActivityName") as string}
				onSelect={(value) => {
					setTimeout(() => {
						activity.setParameter("ActivityName", value);
					}, 200);
				}}
			/>
			<Tooltip message="The target activity is the activity that you want to check the last responses." />
		</div>
	</div>
	<div class="flex flex-col">
		<div class="flex items-center justify-between">
			<Label>Target Action</Label>
			<Toggle variant="outline" class="italic size-6">Expr</Toggle>
		</div>
		<Textarea
			class="min-w-full min-h-10"
			spellcheck="false"
			value={getTargetAction()}
			onblur={(e) => {
				activity.setParameter("TargetAction", e.currentTarget.value);
			}}
		/>
	</div>
</div>
