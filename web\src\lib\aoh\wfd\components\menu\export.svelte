<script lang="ts">
	import { toast } from "svelte-sonner";
	import { dia } from "rappid/rappid";
	import * as Dialog from "$lib/aoh/wfd/components/ui/dialog/index.js";
	import { But<PERSON>, buttonVariants } from "$lib/aoh/wfd/components/ui/button/index.js";
	import ExportFileBuilder from "$lib/aoh/wfd/exportFileBuilder";
	import { downloadFile } from "$lib/aoh/wfd/bpmn/utils/misc";
	import { GraphError } from "$lib/aoh/wfd/bpmn/utils/error";
	import { Input } from "$lib/aoh/wfd/components/ui/input";
	import { Label } from "$lib/aoh/wfd/components/ui/label/index.js";
	import { workflowData } from "$lib/aoh/wfd/stores/workflowData";

	let { graph }: { graph: dia.Graph } = $props();
	let open = $state(false);

	const exportFile = async () => {
		try {
			const builder = new ExportFileBuilder(graph, $workflowData);
			const file = await builder.addSchema("hybrid").processFiles().generateBlob();
			if (!file) {
				return;
			}
			downloadFile(file.blob, file.fileName);
		} catch (error) {
			if (error instanceof GraphError) {
				toast.error(error.getMessage());
			} else {
				toast.error("Failed to export workflow");
			}
		}
	};
</script>

<Dialog.Root bind:open>
	<Dialog.Trigger class={buttonVariants({ variant: "secondary" })}>Export</Dialog.Trigger>
	<Dialog.Content>
		<Dialog.Header>
			<Dialog.Title>Export?</Dialog.Title>
			<Dialog.Description>Export your workflow as a JSON file</Dialog.Description>
		</Dialog.Header>
		<Label>Name</Label>
		<Input bind:value={$workflowData.name} type="text" spellcheck="false" required />
		<Dialog.Footer>
			<Button
				onclick={() => {
					exportFile();
					open = false;
				}}>Export</Button
			>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
