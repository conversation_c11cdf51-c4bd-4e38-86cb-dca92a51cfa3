package session

import (
	"fmt"
	"net/url"

	"github.com/jmoiron/sqlx"
	"github.com/mssfoobar/app/wfe/internal/config"
)

const (
	dsnFmt     = "postgres://%v:%v@%v:%v/%v?sslmode=%v&search_path=%v"
	driverName = "postgres"
)

type Session struct {
	*sqlx.DB
}

func NewSession(cfg *config.SQL) (*Session, error) {
	db, err := createConnection(cfg)
	if err != nil {
		return nil, err
	}
	return &Session{DB: db}, nil
}

func (s *Session) Close() {
	if s.DB != nil {
		_ = s.DB.Close()
	}
}

func createConnection(cfg *config.SQL) (*sqlx.DB, error) {
	db, err := sqlx.Connect(driverName, buildDSN(cfg))
	if err != nil {
		return nil, err
	}
	if cfg.MaxConns > 0 {
		db.SetMaxOpenConns(cfg.MaxConns)
	}
	if cfg.MaxIdleConns > 0 {
		db.SetMaxIdleConns(cfg.MaxIdleConns)
	}
	if cfg.MaxConnLifetime > 0 {
		db.SetConnMaxLifetime(cfg.MaxConnLifetime)
	}

	// Maps struct names in CamelCase to snake without need for db struct tags.
	//db.MapperFunc(strcase.ToSnake)
	return db, nil
}

func buildDSN(cfg *config.SQL) string {
	dsn := fmt.Sprintf(
		dsnFmt,
		cfg.User,
		url.QueryEscape(cfg.Password),
		cfg.Host,
		cfg.Port,
		cfg.DatabaseName,
		cfg.SslMode,
		cfg.SchemaName,
	)
	return dsn
}
