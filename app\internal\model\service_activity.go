package model

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/google/uuid"
	"github.com/mssfoobar/app/wfe/internal/model/sqlplugin"
)

const (
	errDuplicateServiceActivity = "service activity with name '%s' already exists"
	errServiceActivityNotFound  = "service activity id '%s' not found"
)

type sqlServiceActivity struct {
	Store
}

func newSqlServiceActivity(db sqlplugin.DB) *sqlServiceActivity {
	return &sqlServiceActivity{Store: NewStore(db)}
}

func (s sqlServiceActivity) CreateServiceActivity(
	ctx context.Context,
	request *CreateServiceActivityRequest,
) (*ServiceActivityResponse, error) {
	var resp *ServiceActivityResponse
	err := s.txExecute(ctx, "CreateServiceActivity", func(tx sqlplugin.Tx) error {
		_, err := tx.InsertIntoServiceActivity(ctx, &sqlplugin.ServiceActivityRow{
			Id:              uuid.New(),
			ServiceName:     request.ServiceName,
			ActivityType:    request.ActivityType,
			ActivityIcon:    request.ActivityIcon,
			ActivityParam:   request.ActivityParam,
			ActivityResult:  request.ActivityResult,
			TimeoutInSecond: 0,
		})
		if err != nil {
			if s.Db.IsDupEntryError(err) {
				return NewStoreError(fmt.Sprintf(errDuplicateServiceActivity, request.ServiceName))
			}
			return err
		}
		return err
	})

	return resp, err
}

func (s sqlServiceActivity) UpdateServiceActivity(
	ctx context.Context,
	request *UpdateServiceActivityRequest,
) (*ServiceActivityResponse, error) {
	var resp *ServiceActivityResponse
	err := s.txExecute(ctx, "UpdateServiceActivity", func(tx sqlplugin.Tx) error {
		result, err := s.Db.UpdateServiceActivity(ctx, &sqlplugin.ServiceActivityRow{
			Id:              request.Id,
			ServiceName:     request.ServiceName,
			ActivityType:    request.ActivityType,
			ActivityIcon:    request.ActivityIcon,
			ActivityParam:   request.ActivityParam,
			ActivityResult:  request.ActivityResult,
			TimeoutInSecond: request.TimeoutInSecond,
		})
		if err != nil {
			return err
		}

		affectedRows, err := result.RowsAffected()
		if err != nil {
			return err
		}
		if affectedRows == 0 {
			return NewStoreError(fmt.Sprintf(errServiceActivityNotFound, request.Id))
		}

		resp, err = tx.SelectFromServiceActivity(ctx, sqlplugin.ServiceActivityFilter{
			Id: request.Id,
		})
		return err
	})

	return resp, err
}

func (s sqlServiceActivity) GetServiceActivity(
	ctx context.Context,
	request *GetServiceActivityRequest,
) (*ServiceActivityResponse, error) {
	row, err := s.Db.SelectFromServiceActivity(ctx, sqlplugin.ServiceActivityFilter{
		Id: request.Id,
	})
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, NewStoreError(fmt.Sprintf(errServiceActivityNotFound, request.Id))
		}
		return nil, err
	}

	return row, nil
}

func (s sqlServiceActivity) ListServiceActivity(
	ctx context.Context,
	request *ListServiceActivityRequest,
) (*ListServiceActivityResponse, error) {
	var rows []sqlplugin.ServiceActivityRow
	var total int
	var err error
	err = s.txExecute(ctx, "ListServiceActivity", func(tx sqlplugin.Tx) error {
		rows, err = s.Db.ListFromServiceActivity(ctx, sqlplugin.ServiceActivityPaginateFilter{
			Limit:   request.Page.Size,
			Offset:  request.Page.Size * (request.Page.Number - 1),
			OrderBy: request.Page.Sorts.String(),
		})
		if err != nil {
			if s.Db.IsColumnNotExistError(err) {
				return NewStoreError("invalid query parameter; " + err.Error())
			}
			return err
		}

		total, err = s.Db.CountFromServiceActivity(ctx)

		return err
	})

	return &ListServiceActivityResponse{
		TotalCount:        total,
		ServiceActivities: rows,
	}, err
}

func (s sqlServiceActivity) DeleteServiceActivity(
	ctx context.Context,
	request *DeleteServiceActivityRequest,
) error {
	result, err := s.Db.DeleteFromServiceActivity(ctx, sqlplugin.ServiceActivityFilter{
		Id: request.Id,
	})
	if err != nil {
		return err
	}

	affectedRows, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if affectedRows == 0 {
		return NewStoreError(fmt.Sprintf(errServiceActivityNotFound, request.Id))
	}

	return nil
}
