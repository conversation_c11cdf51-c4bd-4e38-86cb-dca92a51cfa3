import { env } from "$env/dynamic/private";
import { json } from "@sveltejs/kit";
import { StatusCodes } from "http-status-codes";
import type { RequestHandler } from "./$types";
import { type Configuration, type TokenEndpointResponse, refreshTokenGrant, buildEndSessionUrl } from "openid-client";
import { COOKIES_TYPE_ENUM, expiryCookieOpts, LOGIN_API } from "$lib/aoh/core/provider/auth/auth";

/** Log the user out by deleting all token cookies */
export const GET: RequestHandler = async ({ cookies, locals, setHeaders }) => {
	if (!locals.clients?.oidc_config) {
		throw new Error(
			"oidc_config missing from locals - required for authentication, please check your configuration for authentication"
		);
	}

	const origin = env.ORIGIN;
	const oidc_config: Configuration = locals.clients?.oidc_config;
	const refresh_token: string | undefined = cookies.get(COOKIES_TYPE_ENUM.REFRESH_TOKEN);

	cookies.delete(COOKIES_TYPE_ENUM.REFRESH_TOKEN, expiryCookieOpts(0));
	cookies.delete(COOKIES_TYPE_ENUM.ACCESS_TOKEN, expiryCookieOpts(0));
	const LOGIN_PAGE: string = env.LOGIN_PAGE ? env.LOGIN_PAGE : LOGIN_API;

	if (refresh_token && oidc_config) {
		const tokens: TokenEndpointResponse = await refreshTokenGrant(oidc_config, refresh_token);

		if (!tokens.id_token) {
			return json(null, {
				status: StatusCodes.UNAUTHORIZED,
			});
		}

		const endSessionUrl: URL = buildEndSessionUrl(oidc_config, {
			post_logout_redirect_uri: origin + ("/" + LOGIN_PAGE).replace("//", "/"),
			id_token_hint: tokens.id_token,
		});

		setHeaders({
			Location: endSessionUrl.toString(),
		});

		return json(null, {
			status: StatusCodes.TEMPORARY_REDIRECT,
		});
	}

	setHeaders({
		Location: origin + ("/" + LOGIN_PAGE).replace("//", "/"),
	});

	return json(null, {
		status: StatusCodes.TEMPORARY_REDIRECT,
	});
};
