{"name": "@mssfoobar/wfe", "description": "Workflow Engine - A full fledged eSOP system", "main": "index.js", "author": "<PERSON><PERSON>", "license": "ISC", "workspaces": ["app", "web"], "devDependencies": {"@changesets/cli": "^2.27.11", "@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "husky": "^9.1.6"}, "scripts": {"prepare": "husky"}, "dependencies": {"@mssfoobar/wfe": "file:"}}