import { LanguageSupport, LRLanguage } from "@codemirror/language";
import { styleTags, tags as t } from "@lezer/highlight";
import { parser } from "$lib/aoh/wfd/codemirror/expressionLang";

export const ExpressionLanguage = LRLanguage.define({
	parser: parser.configure({
		props: [
			styleTags({
				Identifier: t.meta,
				PropertyName: t.propertyName,
				Number: t.number,
				String: t.string,
				Nil: t.null,
				Keyword: t.keyword,
				BooleanLiteral: t.bool,
				Operator: t.operator,
				StringOperator: t.operator,
				"( )": t.paren,
				"[ ]": t.squareBracket,
				"{ }": t.brace,
			}),
		],
	}),
});

export function expression() {
	return new LanguageSupport(ExpressionLanguage);
}
