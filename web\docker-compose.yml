services:
  workflow-designer:
    container_name: workflow-designer
    hostname: workflow-designer
    build:
      context: .
      dockerfile: Dockerfile
    restart: always
    environment:
      - IAM_URL=${IAM_URL}
      - IAM_CLIENT_ID=${IAM_CLIENT_ID}
      - PUBLIC_DOMAIN=${PU<PERSON><PERSON>_DOMAIN}
      - PUBLIC_COOKIE_PREFIX=${PUBLIC_COOKIE_PREFIX}
      - OIDC_ALLOW_INSECURE_REQUESTS=${OIDC_ALLOW_INSECURE_REQUESTS}
      - LOGIN_DESTINATION=${LOGIN_DESTINATION}
      - LOGIN_PAGE${LOGIN_PAGE}
      - PUBLIC_STATIC_BUILD_VERSION=${PUBLIC_STATIC_BUILD_VERSION}
      - WFM_URL=${WFM_URL}
      - ACTIVITY_AAS_URL=${ACTIVITY_AAS_URL}
      - DATA_AGG_URL=${DATA_AGG_URL}
      - PORT=${PORT}
      - ORIGIN=${ORIGIN}
    ports:
      - 4500:8080
    deploy:
      resources:
        reservations:
          cpus: '0.250'
          memory: 64M
        limits:
          cpus: '0.500'
          memory: 128M
    logging:
      driver: "json-file"
      options:
          max-size: "10m"
          max-file: "2"
    networks:
      - wfe-network
    command: >
        build

networks:
  wfe-network:
    driver: bridge
    name: aoh-network
    external: true