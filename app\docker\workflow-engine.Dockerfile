FROM golang:1.23.4 AS builder

WORKDIR /app

COPY go.mod go.mod
COPY go.sum go.sum

COPY internal/ internal/
COPY schema/ schema/
COPY cmd/ cmd/

# Github Personal Access Token to access private go modules
ARG GITHUB_PAT

RUN git --version && \
    git config --global url.https://"$GITHUB_PAT"@github.com/.insteadOf https://github.com/ && \
    CGO_ENABLED=0 GOOS=linux GOARCH=amd64 GO111MODULE=on go build -a -o /build/workflow-engine ./cmd/workflow-engine

FROM alpine:3.21.0

ARG REVISION

LABEL "org.opencontainers.image.source"="https://github.com/mssfoobar/wfe"
LABEL "org.opencontainers.image.title"="Workflow Engine"
LABEL "org.opencontainers.image.description"="Core backend engine for eSOP system"
LABEL "org.opencontainers.image.authors"="https://github.com/nyan979"
LABEL "org.opencontainers.image.revision"="${REVISION}"

RUN addgroup -g 1000 wfe && \
    adduser -u 1000 -G wfe -D wfe && \
    mkdir /app && \
    chown -R wfe:wfe /app
USER wfe

WORKDIR /app

COPY --from=builder /build/workflow-engine .

CMD ["/app/workflow-engine"]
