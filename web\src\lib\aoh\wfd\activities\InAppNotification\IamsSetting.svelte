<script lang="ts" module>
	type IAMS_USER = {
		id: string;
		username: string;
	};

	type IAMS_TENANT = {
		id: string;
		name: string;
	};
</script>

<script lang="ts">
	import * as Select from "$lib/aoh/wfd/components/ui/select";
	import { Label } from "$lib/aoh/wfd/components/ui/label";
	import { type Activity } from "$lib/aoh/wfd/activitySDK/api";

	let { activity }: { activity: Activity } = $props();
	let iams_users: Array<IAMS_USER> = $state((activity.getUiState("iams_users") as Array<IAMS_USER>) || []);
	let iams_tenants: Array<IAMS_TENANT> = $state((activity.getUiState("iams_tenants") as Array<IAMS_TENANT>) || []);
	let sender_id = $state((activity.getParameter("senderId") as string) || "");
	let receiver_id = $state((activity.getParameter("receiverId") as string) || "");
	let tenant_id = $state((activity.getParameter("tenantId") as string) || "");

	const senderIdTriggerContent = $derived(
		iams_users.find((user) => user.id === sender_id)?.username ?? "Select sender"
	);
	const receiverIdTriggerContent = $derived(
		iams_users.find((user) => user.id === receiver_id)?.username ?? "Select receiver"
	);
	const tenantIdTriggerContent = $derived(
		iams_tenants.find((tenant) => tenant.id === tenant_id)?.name ?? "Select tenant"
	);
</script>

<p class="text-xs text-destructive">Only IAMS system admin can configure this setting</p>
<Label for="senderId">From</Label>
<Select.Root
	type="single"
	allowDeselect={false}
	name="senderId"
	bind:value={sender_id}
	onValueChange={() => activity.setParameter("senderId", sender_id)}
>
	<Select.Trigger class="w-full">
		{senderIdTriggerContent}
	</Select.Trigger>
	<Select.Content>
		<Select.Group>
			{#each iams_users as user}
				<Select.Item value={user.id} label={user.username}>{user.username}</Select.Item>
			{/each}
		</Select.Group>
	</Select.Content>
</Select.Root>
<Label for="receiverId">To</Label>
<Select.Root
	type="single"
	allowDeselect={false}
	name="receiverId"
	bind:value={receiver_id}
	onValueChange={() => activity.setParameter("receiverId", receiver_id)}
>
	<Select.Trigger class="w-full">
		{receiverIdTriggerContent}
	</Select.Trigger>
	<Select.Content>
		<Select.Group>
			{#each iams_users as user}
				<Select.Item value={user.id} label={user.username}>{user.username}</Select.Item>
			{/each}
		</Select.Group>
	</Select.Content>
</Select.Root>
<Label for="tenantId">Tenant</Label>
<Select.Root
	type="single"
	allowDeselect={false}
	name="tenantId"
	bind:value={tenant_id}
	onValueChange={() => activity.setParameter("tenantId", tenant_id)}
>
	<Select.Trigger class="w-full">
		{tenantIdTriggerContent}
	</Select.Trigger>
	<Select.Content>
		<Select.Group>
			{#each iams_tenants as tenant}
				<Select.Item value={tenant.id} label={tenant.name}>{tenant.name}</Select.Item>
			{/each}
		</Select.Group>
	</Select.Content>
</Select.Root>
