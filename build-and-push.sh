#!/bin/bash
 
set -e
 
# Configurable variables
ECR_REGISTRY=131842227326.dkr.ecr.ap-southeast-1.amazonaws.com
APP_NAME=hcc3-wfe-designer-fpt
IMAGE_TAG=latest
 
# AWS login
aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin $ECR_REGISTRY
 
# Ensure repository exists
aws ecr describe-repositories --repository-names $APP_NAME >/dev/null 2>&1 || \
aws ecr create-repository --repository-name $APP_NAME
 
# Build Docker image
cd ./web && docker build . --file Dockerfile -t $ECR_REGISTRY/$APP_NAME:$IMAGE_TAG \
    --build-arg PORT=8080 \
    --build-arg ORIGIN=http://wfd.************.nip.io \
    --build-arg IAM_URL=http://iams-keycloak.************.nip.io/realms/AOH/.well-known/openid-configuration \
    --build-arg IAM_CLIENT_ID=wfm_client \
    --build-arg PUBLIC_DOMAIN=************.nip.io \
    --build-arg PUBLIC_COOKIE_PREFIX=wfd \
    --build-arg OIDC_ALLOW_INSECURE_REQUESTS=1 \
    --build-arg LOGIN_DESTINATION=/aoh/wfd \
    --build-arg WFM_URL=http://workflow-manager:8080 \
    --build-arg ACTIVITY_AAS_URL=http://iams-aas.************.nip.io \
    --build-arg DATA_AGG_URL=http://data-agg:5003
 
# Push Docker image
docker push $ECR_REGISTRY/$APP_NAME:$IMAGE_TAG
 