<script>
	import { Tooltip } from "bits-ui";
	import HelpIcon from "lucide-svelte/icons/circle-help";
	export let message = "";
</script>

<Tooltip.Provider>
	<Tooltip.Root delayDuration={0}>
		<Tooltip.Trigger>
			<HelpIcon class="size-5 min-w-[24px] ml-2" />
		</Tooltip.Trigger>
		<Tooltip.Content sideOffset={8} align="end">
			<div
				class="rounded-input border-dark-10 bg-background shadow-popover outline-hidden z-0 flex items-center justify-center border p-3"
			>
				{message}
			</div>
		</Tooltip.Content>
	</Tooltip.Root>
</Tooltip.Provider>
