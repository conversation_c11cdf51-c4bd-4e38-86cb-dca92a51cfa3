package handler

import (
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/mssfoobar/app/wfe/internal/model"
	"github.com/mssfoobar/app/wfe/internal/wfm/temporal"
)

func NewRouter(factory *model.Factory, temporal temporal.Service, keycloakUrl string) http.Handler {
	r := chi.NewRouter()

	r.Route("/v1", func(r chi.Router) {
		r.Mount("/", registerHealth())
		r.Mount("/workflow_template", RegisterWorkflowTemplate(factory, keycloakUrl))
		r.Mount("/form_template", RegisterFormTemplate(factory, keycloakUrl))
		r.Mount("/workflow", RegisterExecute(factory, temporal, keycloakUrl))
		r.Mount("/validate", RegisterValidate(keycloakUrl))
		r.Mount("/service_activity", RegisterServiceActivity(factory, keycloakUrl))
		r.Mount("/service_event", RegisterServiceEvent(factory, keycloakUrl))
	})
	return r
}
