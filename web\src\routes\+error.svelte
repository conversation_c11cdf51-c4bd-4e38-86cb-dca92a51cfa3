<svelte:options runes={true} />

<script lang="ts">
	import { page } from "$app/stores";
	import { annotate } from "rough-notation";
	import { onMount } from "svelte";

	let annotateElement: HTMLSpanElement;

	onMount(() => {
		annotate(annotateElement, {
			type: "underline",
		}).show();
	});
</script>

<div class="text-foreground bg-background w-screen h-screen flex items-center justify-center flex-col gap-2">
	<div class="text-4xl text-destructive">ERROR</div>
	<div class="text-2xl">Page {$page?.error?.message}</div>
	<div class="text-lg">
		Customize this page at <span class="text-orange-500" bind:this={annotateElement}
			>{new URL(import.meta.url).pathname}</span
		>
	</div>
</div>
