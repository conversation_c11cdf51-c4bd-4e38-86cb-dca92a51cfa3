<script lang="ts" module>
	/**
	 * This component defines the sidebar using shadcn-svelte.
	 * It can serve as a reference for implementing custom sidebars.
	 */
	import ChevronsRight from "lucide-svelte/icons/chevrons-right";
	import * as Sidebar from "$lib/aoh/core/components/ui/sidebar/index.js";
	import { useSidebar } from "$lib/aoh/core/components/ui/sidebar/index.js";
	import type { Tenant } from "$lib/aoh/core/provider/auth/auth";
	import { titleCase } from "change-case-all";
	import { page } from "$app/stores";
	// import { logger } from "@mssfoobar/logger/Logger";

	export type SidebarItem = {
		title: string;
		url: string;
		icon: any;
		isSelected: boolean;
	};

	export type SidebarProps = {
		items: SidebarItem[];
		title: string;
		tenant?: Tenant;
	};
</script>

<script lang="ts">
	let { items, title, tenant }: SidebarProps = $props();

	const sidebar = useSidebar();

	function isSidebarExpanded(): boolean {
		return sidebar.state == "expanded";
	}

	// Basic method to set all item as selected state - more discussion required to decide when something is highlighted
	$effect(() => {
		items.forEach((item) => {
			if ($page.url.pathname.startsWith(item.url)) {
				item.isSelected = true;
			} else {
				item.isSelected = false;
			}
		});
	});
</script>

<Sidebar.Root collapsible="icon">
	<Sidebar.Content class="py-4">
		<Sidebar.Group>
			<Sidebar.Menu class="">
				<Sidebar.MenuItem
					class="flex gap-2 items-start transition-[padding] {isSidebarExpanded() ? 'px-2' : ''}"
				>
					<Sidebar.MenuButton
						class="flex-none bg-bg-primary hover:bg-bg-primary-hover active:bg-bg-primary-active w-8 h-8"
						onclick={() => sidebar.toggle()}
					>
						<ChevronsRight
							class="{isSidebarExpanded()
								? 'rotate-180'
								: ''} text-icon-onColor transition-transform duration-200 ease-in-out"
						/>
					</Sidebar.MenuButton>
					{#if isSidebarExpanded()}
						<div class="grow">
							<p class="font-semibold text-sm text-color-magic leading-none text-nowrap">{title}</p>
							{#if tenant && tenant.tenant_name}
								<span class="text-xs text-center px-2 py-[2px] border rounded-md border-border">
									{titleCase(tenant.tenant_name)}
								</span>
							{/if}
						</div>
					{/if}
				</Sidebar.MenuItem>
			</Sidebar.Menu>
		</Sidebar.Group>
		<Sidebar.Group>
			<Sidebar.GroupLabel>Platform</Sidebar.GroupLabel>
			<Sidebar.GroupContent>
				<Sidebar.Menu>
					{#each items as item (item.title)}
						<Sidebar.MenuItem>
							<Sidebar.MenuButton class={item.isSelected ? "bg-sidebar-accent" : ""}>
								{#snippet child({ props })}
									<a href={item.url} {...props}>
										<item.icon class="text-icon" />
										<span>{titleCase(item.title)}</span>
									</a>
								{/snippet}
							</Sidebar.MenuButton>
						</Sidebar.MenuItem>
					{/each}
				</Sidebar.Menu>
			</Sidebar.GroupContent>
		</Sidebar.Group>
	</Sidebar.Content>
</Sidebar.Root>
