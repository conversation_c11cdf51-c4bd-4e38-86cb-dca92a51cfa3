<script lang="ts">
	import * as Dialog from "$lib/aoh/wfd/components/ui/dialog/index.js";
	import { Button } from "$lib/aoh/wfd/components/ui/button/index.js";
	import SearchIcon from "lucide-svelte/icons/search";
	import SaveIcon from "lucide-svelte/icons/save";
	import ResetIcon from "lucide-svelte/icons/refresh-cw";

	import { createEventDispatcher } from "svelte";
	import { writable } from "svelte/store";

	import {
		Input,
		Checkbox,
		Table,
		TableBody,
		TableBodyCell,
		TableBodyRow,
		TableHead,
		TableHeadCell,
	} from "flowbite-svelte";

	type ColumnDefinition = {
		name: string;
		key: string;
	};

	const dispatch = createEventDispatcher();

	// Control the Modal
	let {
		open = $bindable(),
		modalTitle,
		rows = [],
		columns = [],
		selectedItems = [],
		keyToCompare,
	} = $props<{
		open: boolean;
		modalTitle: string;
		rows: Record<string, any>[];
		columns: ColumnDefinition[];
		selectedItems: any[];
		keyToCompare: string;
	}>();

	selectedItems = selectedItems.filter((item: any) =>
		rows.some((row: Record<string, any>) => row[keyToCompare] === item)
	);

	let caches = [...selectedItems];
	const textFilter = writable("");
	const filteredRows = writable(rows);
	const headCellStyle = "border  border-t-0 capitalize p-4 cursor-pointer";
	const bodyCellStyle = "text-wrap border-x   px-4";

	const handleSearch = () => {
		if ($textFilter !== "") {
			const newFiltered: Record<string, any>[] = [];
			rows.forEach((row: Record<string, any>) => {
				for (const col of columns) {
					if (row[col.key]?.toLowerCase().indexOf($textFilter.toLowerCase()) > -1) {
						newFiltered.push(row);
						break;
					}
				}
			});
			filteredRows.set([...newFiltered]);
		} else {
			filteredRows.set(rows);
		}
	};

	const onCheckAll = (event: Event) => {
		if ((event.currentTarget as HTMLInputElement)?.checked) {
			const cachedItems = [...selectedItems];
			$filteredRows.forEach((e: Record<string, any>) => {
				if (!cachedItems.includes(e[keyToCompare])) cachedItems.push(e[keyToCompare]);
			});
			selectedItems = [...cachedItems];
		} else {
			const filterItems = $filteredRows.map((row: Record<string, any>) => row[keyToCompare]);
			selectedItems = selectedItems?.filter((item: any) => !filterItems.includes(item));
		}
	};

	const onCheck = (value: string, event: Event) => {
		if ((event.currentTarget as HTMLInputElement).checked) {
			if (selectedItems?.length) {
				selectedItems = [...selectedItems, value];
			} else {
				selectedItems = [value];
			}
		} else {
			selectedItems = selectedItems?.filter((e: any) => e !== value);
		}
	};

	const onSave = () => {
		caches = [...selectedItems];
		dispatch("close", selectedItems);
	};

	const onReset = () => {
		selectedItems = [...caches];
	};
</script>

<Dialog.Root bind:open>
	<Dialog.Content class="w-[800px] max-w-full">
		<Dialog.Header>
			<Dialog.Title>{modalTitle}</Dialog.Title>
		</Dialog.Header>
		<!-- Search bar -->
		<div class="relative">
			<Input
				on:input={handleSearch}
				bind:value={$textFilter}
				type="text"
				id="textFilter"
				name="textFilter"
				placeholder="Search"
				class="h-10 pl-10"
			>
				<div class="ml-2" slot="left">
					<SearchIcon />
				</div>
			</Input>
		</div>

		<!-- Table -->
		<div class="relative flex flex-col">
			<div class="mb-3 flex-1 rounded-lg border-y">
				<Table striped={true} border={1} divClass="max-h-[400px] overflow-auto rounded-lg">
					<TableHead
						defaultRow={false}
						theadClass="sticky -top-[0.1px] capitalize bg-gray-50 dark:bg-gray-900 z-10 text-gray-900 dark:text-gray-100"
					>
						<TableHeadCell class={headCellStyle + " w-40"}>
							<div class="flex">
								<span class="flex">
									<Checkbox
										class="mr-1"
										on:change={onCheckAll}
										checked={$filteredRows.length === selectedItems.length}
									/>
									<div class="inline">Select All</div>
								</span>
							</div>
						</TableHeadCell>
						{#each columns as column}
							<TableHeadCell class={headCellStyle}>
								<span class="mr-1">{column.name}</span>
							</TableHeadCell>
						{/each}
					</TableHead>
					<TableBody>
						{#each $filteredRows as row}
							<TableBodyRow class="dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700">
								<TableBodyCell class={bodyCellStyle + " w-40"}>
									<Checkbox
										class=""
										checked={selectedItems?.includes(row[keyToCompare])}
										on:change={(event) => onCheck(row[keyToCompare], event)}
									/>
								</TableBodyCell>
								{#each columns as column}
									<TableBodyCell class={bodyCellStyle}>{row[column.key] || ""}</TableBodyCell>
								{/each}
							</TableBodyRow>
						{/each}
					</TableBody>
				</Table>
			</div>
		</div>

		<Dialog.Footer>
			<Button
				onclick={() => {
					onSave();
					open = false;
				}}><SaveIcon /> Ok</Button
			>
			<Button
				onclick={() => {
					onReset();
				}}><ResetIcon /> Reset</Button
			>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
