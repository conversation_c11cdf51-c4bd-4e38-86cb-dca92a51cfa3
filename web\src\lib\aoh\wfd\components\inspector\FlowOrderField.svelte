<script lang="ts">
	import { onD<PERSON>roy, onMount } from "svelte";
	import type { ui } from "rappid/rappid";
	import { Input } from "$lib/aoh/wfd/components/ui/input";
	import { Label } from "$lib/aoh/wfd/components/ui/label";
	import type { Flow, Switch } from "$lib/aoh/wfd/bpmn/shapes";

	let { inspector }: { inspector: ui.Inspector } = $props();

	const cell = inspector.options.cell as Flow;
	let dict = (cell.getSourceCell() as Switch)?.orderDict;

	let order: number | undefined = $state(dict?.getByVal(cell.id) || dict?.genNextKey(cell.id));
	let errorMsg: string = $state("");

	if (!cell.isConditional()) {
		errorMsg = "";
		dict?.deleteByVal(cell.id);
	} else {
		cell.updateConditionalLabel();
	}

	const onInputChange = () => {
		if (order === undefined) {
			return;
		}

		if (order > 99 || order < 1) {
			errorMsg = "Value out of range (1-99)";
			return;
		}

		if (dict.hasNum(order) && dict.getByNum(order) !== cell.id) {
			errorMsg = "Duplicated order number";
			return;
		}

		errorMsg = "";
		dict.deleteByVal(cell.id);
		dict.set(order, cell.id);
		cell.updateConditionalLabel();
	};

	const onChangeSource = () => {
		dict = (cell.getSourceElement() as Switch)?.orderDict;
		cell.updateConditionalLabel();
	};

	onMount(() => {
		cell.on("change:source", onChangeSource);
	});

	onDestroy(() => {
		cell.off("change:source", onChangeSource);
	});
</script>

<Label for="order">Order</Label>
<Input
	id="order"
	type="number"
	min="1"
	max="99"
	class={errorMsg !== "" ? "error" : ""}
	onblur={onInputChange}
	bind:value={order}
/>
<div class="text-destructive text-xs normal-case">{errorMsg}</div>
