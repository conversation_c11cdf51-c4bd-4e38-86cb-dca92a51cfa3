package dsl

import (
	"context"
	"errors"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"
	"time"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"go.temporal.io/sdk/activity"
	"go.temporal.io/sdk/testsuite"
	"go.temporal.io/sdk/workflow"
)

type Activities struct{}

func (a *Activities) End(ctx context.Context) error {
	return nil
}

func (a *Activities) Delay(ctx context.Context, second float64) error {
	delay := time.Duration(second) * time.Second
	elapsedDuration := time.Nanosecond
	for elapsedDuration < delay {
		time.Sleep(time.Second)
		elapsedDuration += time.Second
		activity.RecordHeartbeat(ctx, "status-report-to-workflow")
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}
	}
	return nil
}

func (a *Activities) SampleEvent1(ctx context.Context, input string) (string, error) {
	return "", nil
}

func (a *Activities) SampleActivity1(ctx context.Context, input string) (string, error) {
	return "", nil
}

func (a *Activities) SampleActivity2(ctx context.Context, input string) (string, error) {
	return "", nil
}

func (a *Activities) SampleActivity3(ctx context.Context, input string) (string, error) {
	return "", nil
}

type UnitTestSuite struct {
	suite.Suite
	testsuite.WorkflowTestSuite
}

func TestUnitTestSuite(t *testing.T) {
	suite.Run(t, new(UnitTestSuite))
}

func (s *UnitTestSuite) TestExecuteWorkflow_Activity() {
	env := s.NewTestWorkflowEnvironment()
	expected1 := "result1"
	expected2 := "result2"
	env.RegisterActivity(&Activities{})
	var a *Activities
	env.OnActivity(a.SampleActivity1, mock.Anything, mock.Anything).Return(expected1, nil)
	env.OnActivity(a.SampleActivity2, mock.Anything, mock.Anything).Return(expected2, nil)
	dsl := Workflow{
		Variables: map[string]interface{}{
			"SampleActivity1_Input": "test",
		},
		Start: "SampleActivity1",
		States: []Statement{
			{
				Name: "SampleActivity1",
				Next: "SampleActivity2",
				Activity: &ActivityInvocation{
					Type: "SampleActivity1",
					ActivityBase: ActivityBase{
						Result:    "SampleActivity1_Result",
						Arguments: []string{"SampleActivity1_Input"},
					},
					Options: &ActivityInvocationOptions{
						TaskQueue:              "test",
						HeartbeatTimeout:       300,
						ScheduleToCloseTimeout: 300,
						ScheduleToStartTimeout: 300,
						StartToCloseTimeout:    300,
						WaitForCancellation:    true,
					},
				},
			},
			{
				Name: "SampleActivity2",
				Next: "End",
				Activity: &ActivityInvocation{
					Type: "SampleActivity2",
					ActivityBase: ActivityBase{
						Result:    "SampleActivity2_Result",
						Arguments: []string{"SampleActivity1_Result"},
					},
				},
			},
			{
				Name: "End",
				End:  &End{Terminate: false},
			},
		},
	}
	env.ExecuteWorkflow(ExecuteWorkflow, dsl)
	s.True(env.IsWorkflowCompleted())
	s.NoError(env.GetWorkflowError())
	var result map[string]any
	s.NoError(env.GetWorkflowResult(&result))
	s.Equal(expected1, result["SampleActivity1_Result"])
	s.Equal(expected2, result["SampleActivity2_Result"])
}

func (s *UnitTestSuite) TestExecuteWorkflow_Activity_Error() {
	env := s.NewTestWorkflowEnvironment()
	expectedError := errors.New("activity error")
	env.RegisterActivity(&Activities{})
	var a *Activities
	env.OnActivity(a.SampleActivity1, mock.Anything, mock.Anything).Return("result", nil)
	env.OnActivity(a.SampleActivity2, mock.Anything, mock.Anything).Return("", expectedError)
	dsl := Workflow{
		Start: "SampleActivity1",
		States: []Statement{
			{
				Name: "SampleActivity1",
				Next: "SampleActivity2",
				Activity: &ActivityInvocation{
					Type: "SampleActivity1",
					ActivityBase: ActivityBase{
						Result:    "SampleActivity1_Result",
						Arguments: []string{"SampleActivity1_Input"},
					},
				},
			},
			{
				Name: "SampleActivity2",
				Next: "End",
				Activity: &ActivityInvocation{
					Type: "SampleActivity2",
					ActivityBase: ActivityBase{
						Result:    "SampleActivity2_Result",
						Arguments: []string{"SampleActivity1_Result"},
					},
				},
			},
			{
				Name: "End",
				End:  &End{Terminate: false},
			},
		},
	}
	env.ExecuteWorkflow(ExecuteWorkflow, dsl)
	s.True(env.IsWorkflowCompleted())
	s.ErrorAs(env.GetWorkflowError(), &expectedError)
}

func (s *UnitTestSuite) TestExecuteWorkflow_CallActivity() {
	env := s.NewTestWorkflowEnvironment()
	expected1 := "result1"
	expected2 := "result2"
	env.RegisterActivity(&Activities{})
	env.RegisterWorkflow(ChildExecuteWorkflow)
	var a *Activities
	env.OnActivity(a.SampleActivity1, mock.Anything, mock.Anything).Return(expected1, nil)
	env.OnActivity(a.SampleActivity2, mock.Anything, mock.Anything).Return(expected2, nil)
	dsl := Workflow{
		Variables: map[string]any{
			"SampleCallActivity_WorkflowJSON": Workflow{
				Variables: map[string]interface{}{
					"SampleActivity1_Input": "test",
				},
				Start: "SampleActivity1",
				States: []Statement{
					{
						Name: "SampleActivity1",
						Next: "SampleActivity2",
						Activity: &ActivityInvocation{
							Type: "SampleActivity1",
							ActivityBase: ActivityBase{
								Result:    "SampleActivity1_Result",
								Arguments: []string{"SampleActivity1_Input"},
							},
							Options: &ActivityInvocationOptions{
								TaskQueue:              "test",
								HeartbeatTimeout:       300,
								ScheduleToCloseTimeout: 300,
								ScheduleToStartTimeout: 300,
								StartToCloseTimeout:    300,
								WaitForCancellation:    true,
							},
						},
					},
					{
						Name: "SampleActivity2",
						Next: "End",
						Activity: &ActivityInvocation{
							Type: "SampleActivity2",
							ActivityBase: ActivityBase{
								Result:    "SampleActivity2_Result",
								Arguments: []string{"SampleActivity1_Result"},
							},
						},
					},
					{
						Name: "End",
						End:  &End{Terminate: false},
					},
				},
			},
		},
		Start: "SampleCallActivity",
		States: []Statement{
			{
				Name: "SampleCallActivity",
				Next: "End",
				CallActivity: &CallActivity{
					ActivityBase: ActivityBase{
						Result:    "SampleCallActivity_Result",
						Arguments: []string{"SampleCallActivity_WorkflowJSON"},
					},
				},
			},
			{
				Name: "End",
				End:  &End{Terminate: false},
			},
		},
	}
	env.ExecuteWorkflow(ExecuteWorkflow, dsl)
	s.True(env.IsWorkflowCompleted())
	s.NoError(env.GetWorkflowError())
	var result map[string]map[string]any
	s.NoError(env.GetWorkflowResult(&result))
	s.Equal(expected1, result["SampleCallActivity_Result"]["SampleActivity1_Result"])
	s.Equal(expected2, result["SampleCallActivity_Result"]["SampleActivity2_Result"])
}

func (s *UnitTestSuite) TestExecuteWorkflow_CallActivity_Error() {
	env := s.NewTestWorkflowEnvironment()
	expectedError := errors.New("activity error")
	env.RegisterActivity(&Activities{})
	env.RegisterWorkflow(ChildExecuteWorkflow)
	var a *Activities
	env.OnActivity(a.SampleActivity1, mock.Anything, mock.Anything).Return("result", nil)
	env.OnActivity(a.SampleActivity2, mock.Anything, mock.Anything).Return("", expectedError)
	dsl := Workflow{
		Variables: map[string]any{
			"SampleCallActivity_WorkflowJSON": Workflow{
				Variables: map[string]interface{}{
					"SampleActivity1_Input": "test",
				},
				Start: "SampleActivity1",
				States: []Statement{
					{
						Name: "SampleActivity1",
						Next: "SampleActivity2",
						Activity: &ActivityInvocation{
							Type: "SampleActivity1",
							ActivityBase: ActivityBase{
								Result:    "SampleActivity1_Result",
								Arguments: []string{"SampleActivity1_Input"},
							},
							Options: &ActivityInvocationOptions{
								TaskQueue:              "test",
								HeartbeatTimeout:       300,
								ScheduleToCloseTimeout: 300,
								ScheduleToStartTimeout: 300,
								StartToCloseTimeout:    300,
								WaitForCancellation:    true,
							},
						},
					},
					{
						Name: "SampleActivity2",
						Next: "End",
						Activity: &ActivityInvocation{
							Type: "SampleActivity2",
							ActivityBase: ActivityBase{
								Result:    "SampleActivity2_Result",
								Arguments: []string{"SampleActivity1_Result"},
							},
						},
					},
					{
						Name: "End",
						End:  &End{Terminate: false},
					},
				},
			},
		},
		Start: "SampleCallActivity",
		States: []Statement{
			{
				Name: "SampleCallActivity",
				Next: "End",
				CallActivity: &CallActivity{
					ActivityBase: ActivityBase{
						Result:    "SampleCallActivity_Result",
						Arguments: []string{"SampleCallActivity_WorkflowJSON"},
					},
				},
			},
			{
				Name: "End",
				End:  &End{Terminate: false},
			},
		},
	}
	env.ExecuteWorkflow(ExecuteWorkflow, dsl)
	s.True(env.IsWorkflowCompleted())
	s.ErrorAs(env.GetWorkflowError(), &expectedError)
}

func (s *UnitTestSuite) TestExecuteWorkflow_Parallel() {
	env := s.NewTestWorkflowEnvironment()
	expected1 := "result1"
	expected2 := "result2"
	env.RegisterActivity(&Activities{})
	var a *Activities
	env.OnActivity(a.SampleActivity1, mock.Anything, mock.Anything).Return(expected1, nil)
	env.OnActivity(a.SampleActivity2, mock.Anything, mock.Anything).Return(expected2, nil)
	dsl := Workflow{
		Start: "Parallel1",
		States: []Statement{
			{
				Name: "SampleActivity1",
				Activity: &ActivityInvocation{
					Type: "SampleActivity1",
					ActivityBase: ActivityBase{
						Result: "SampleActivity1_Result",
					},
				},
			},
			{
				Name: "SampleActivity2",
				Activity: &ActivityInvocation{
					Type: "SampleActivity2",
					ActivityBase: ActivityBase{
						Result: "SampleActivity2_Result",
					},
				},
			},
			{
				Name: "Parallel1",
				Next: "End",
				Parallel: &Parallel{
					Branches: []Branch{
						{
							Next: "SampleActivity1",
						},
						{
							Next: "SampleActivity2",
						},
					},
				},
			},
			{
				Name: "End",
				End:  &End{Terminate: false},
			},
		},
	}
	env.ExecuteWorkflow(ExecuteWorkflow, dsl)
	s.True(env.IsWorkflowCompleted())
	s.NoError(env.GetWorkflowError())
	var result map[string]any
	s.NoError(env.GetWorkflowResult(&result))
	s.Equal(expected1, result["SampleActivity1_Result"])
	s.Equal(expected2, result["SampleActivity2_Result"])
}

func (s *UnitTestSuite) TestExecuteWorkflow_Parallel_Error() {
	env := s.NewTestWorkflowEnvironment()
	expectedError := errors.New("activity error")
	env.RegisterActivity(&Activities{})
	var a *Activities
	env.OnActivity(a.SampleActivity1, mock.Anything, mock.Anything).Return("", expectedError)
	env.OnActivity(a.SampleActivity2, mock.Anything, mock.Anything).Return("", expectedError)
	dsl := Workflow{
		Start: "Parallel1",
		States: []Statement{
			{
				Name: "SampleActivity1",
				Activity: &ActivityInvocation{
					Type: "SampleActivity1",
					ActivityBase: ActivityBase{
						Result: "SampleActivity1_Result",
					},
				},
			},
			{
				Name: "SampleActivity2",
				Activity: &ActivityInvocation{
					Type: "SampleActivity2",
					ActivityBase: ActivityBase{
						Result: "SampleActivity2_Result",
					},
				},
			},
			{
				Name: "Parallel1",
				Next: "End",
				Parallel: &Parallel{
					Branches: []Branch{
						{
							Next: "SampleActivity1",
						},
						{
							Next: "SampleActivity2",
						},
					},
				},
			},
			{
				Name: "End",
				End:  &End{Terminate: false},
			},
		},
	}
	env.ExecuteWorkflow(ExecuteWorkflow, dsl)
	s.True(env.IsWorkflowCompleted())
	s.Error(env.GetWorkflowError())
}

func (s *UnitTestSuite) TestExecuteWorkflow_Switch_Condition() {
	env := s.NewTestWorkflowEnvironment()
	expected1 := "result1"
	expected2 := "result2"
	env.RegisterActivity(&Activities{})
	var a *Activities
	env.OnActivity(a.SampleActivity1, mock.Anything, mock.Anything).Return(expected1, nil)
	env.OnActivity(a.SampleActivity2, mock.Anything, mock.Anything).Return(expected2, nil)
	env.OnActivity(a.SampleActivity3, mock.Anything, mock.Anything).Return("", nil)
	dsl := Workflow{
		Start: "SampleActivity1",
		States: []Statement{
			{
				Name: "SampleActivity1",
				Next: "Switch1",
				Activity: &ActivityInvocation{
					Type: "SampleActivity1",
					ActivityBase: ActivityBase{
						Result: "SampleActivity1_Result",
					},
				},
			},
			{
				Name: "SampleActivity2",
				Next: "End",
				Activity: &ActivityInvocation{
					Type: "SampleActivity2",
					ActivityBase: ActivityBase{
						Result: "SampleActivity2_Result",
					},
				},
			},
			{
				Name: "SampleActivity3",
				Next: "End",
				Activity: &ActivityInvocation{
					Type: "SampleActivity3",
					ActivityBase: ActivityBase{
						Result: "SampleActivity3_Result",
					},
				},
			},
			{
				Name: "Switch1",
				Switch: &Switch{
					Cases: []*Case{
						{
							Name: "SampleActivity1_Result == result1",
							Next: "SampleActivity2",
							Conditional: Expression{
								Basic: &Basic{
									Input:    "SampleActivity1_Result",
									Operator: "EQ",
									Value:    expected1,
								},
							},
						},
					},
					Default: &Default{
						Next: "SampleActivity3",
					},
				},
			},
			{
				Name: "End",
				End:  &End{Terminate: false},
			},
		},
	}
	env.ExecuteWorkflow(ExecuteWorkflow, dsl)
	s.True(env.IsWorkflowCompleted())
	s.NoError(env.GetWorkflowError())
	var result map[string]any
	s.NoError(env.GetWorkflowResult(&result))
	s.Equal(expected1, result["SampleActivity1_Result"])
	s.Equal(expected2, result["SampleActivity2_Result"])
}

func (s *UnitTestSuite) TestExecuteWorkflow_Switch_Condition_Advance() {
	env := s.NewTestWorkflowEnvironment()
	expected1 := "result1"
	expected2 := "result2"
	env.RegisterActivity(&Activities{})
	var a *Activities
	env.OnActivity(a.SampleActivity1, mock.Anything, mock.Anything).Return(expected1, nil)
	env.OnActivity(a.SampleActivity2, mock.Anything, mock.Anything).Return(expected2, nil)
	env.OnActivity(a.SampleActivity3, mock.Anything, mock.Anything).Return("", nil)
	dsl := Workflow{
		Start: "SampleActivity1",
		States: []Statement{
			{
				Name: "SampleActivity1",
				Next: "Switch1",
				Activity: &ActivityInvocation{
					Type: "SampleActivity1",
					ActivityBase: ActivityBase{
						Result: "SampleActivity1_Result",
					},
				},
			},
			{
				Name: "SampleActivity2",
				Next: "End",
				Activity: &ActivityInvocation{
					Type: "SampleActivity2",
					ActivityBase: ActivityBase{
						Result: "SampleActivity2_Result",
					},
				},
			},
			{
				Name: "SampleActivity3",
				Next: "End",
				Activity: &ActivityInvocation{
					Type: "SampleActivity3",
					ActivityBase: ActivityBase{
						Result: "SampleActivity3_Result",
					},
				},
			},
			{
				Name: "Switch1",
				Switch: &Switch{
					Cases: []*Case{
						{
							Name: "SampleActivity1_Result == result1",
							Next: "SampleActivity2",
							Conditional: Expression{
								Advance: &Advance{
									Expression: "SampleActivity1_Result == \"result1\"",
								},
							},
						},
					},
					Default: &Default{
						Next: "SampleActivity3",
					},
				},
			},
			{
				Name: "End",
				End:  &End{Terminate: false},
			},
		},
	}
	env.ExecuteWorkflow(ExecuteWorkflow, dsl)
	s.True(env.IsWorkflowCompleted())
	s.NoError(env.GetWorkflowError())
	var result map[string]any
	s.NoError(env.GetWorkflowResult(&result))
	s.Equal(expected1, result["SampleActivity1_Result"])
	s.Equal(expected2, result["SampleActivity2_Result"])
}

func (s *UnitTestSuite) TestExecuteWorkflow_Switch_Default() {
	env := s.NewTestWorkflowEnvironment()
	expected1 := "result1"
	expected2 := "result2"
	expected3 := "result3"
	env.RegisterActivity(&Activities{})
	var a *Activities
	env.OnActivity(a.SampleActivity1, mock.Anything, mock.Anything).Return(expected1, nil)
	env.OnActivity(a.SampleActivity2, mock.Anything, mock.Anything).Return(expected2, nil)
	env.OnActivity(a.SampleActivity3, mock.Anything, mock.Anything).Return(expected3, nil)
	dsl := Workflow{
		Start: "SampleActivity1",
		States: []Statement{
			{
				Name: "SampleActivity1",
				Next: "Switch1",
				Activity: &ActivityInvocation{
					Type: "SampleActivity1",
					ActivityBase: ActivityBase{
						Result: "SampleActivity1_Result",
					},
				},
			},
			{
				Name: "SampleActivity2",
				Next: "End",
				Activity: &ActivityInvocation{
					Type: "SampleActivity2",
					ActivityBase: ActivityBase{
						Result: "SampleActivity2_Result",
					},
				},
			},
			{
				Name: "SampleActivity3",
				Next: "End",
				Activity: &ActivityInvocation{
					Type: "SampleActivity3",
					ActivityBase: ActivityBase{
						Result: "SampleActivity3_Result",
					},
				},
			},
			{
				Name: "Switch1",
				Switch: &Switch{
					Cases: []*Case{
						{
							Name: "SampleActivity1_Result == result3",
							Next: "SampleActivity2",
							Conditional: Expression{
								Basic: &Basic{
									Input:    "SampleActivity1_Result",
									Operator: "EQ",
									Value:    expected3,
								},
							},
						},
					},
					Default: &Default{
						Next: "SampleActivity3",
					},
				},
			},
			{
				Name: "End",
				End:  &End{Terminate: false},
			},
		},
	}
	env.ExecuteWorkflow(ExecuteWorkflow, dsl)
	s.True(env.IsWorkflowCompleted())
	s.NoError(env.GetWorkflowError())
	var result map[string]any
	s.NoError(env.GetWorkflowResult(&result))
	s.Equal(expected1, result["SampleActivity1_Result"])
	s.Equal(expected3, result["SampleActivity3_Result"])
}

func (s *UnitTestSuite) TestExecuteWorkflow_Switch_Recover() {
	env := s.NewTestWorkflowEnvironment()
	expected1 := "result1"
	expected2 := "result2"
	expected3 := "result3"
	env.RegisterActivity(&Activities{})
	var a *Activities
	env.OnActivity(a.SampleActivity1, mock.Anything, mock.Anything).Return(expected1, nil)
	env.OnActivity(a.SampleActivity2, mock.Anything, mock.Anything).Return(expected2, nil)
	env.OnActivity(a.SampleActivity3, mock.Anything, mock.Anything).Return(expected3, nil)
	env.RegisterWorkflow(RecoverWorkflow)
	env.OnWorkflow("RecoverWorkflow", mock.Anything, mock.Anything, mock.Anything).Return(
		func(ctx workflow.Context, input []string, name string) (string, error) {
			return "SampleActivity3", nil
		})
	dsl := Workflow{
		Start: "SampleActivity1",
		States: []Statement{
			{
				Name: "SampleActivity1",
				Next: "Switch1",
				Activity: &ActivityInvocation{
					Type: "SampleActivity1",
					ActivityBase: ActivityBase{
						Result: "SampleActivity1_Result",
					},
				},
			},
			{
				Name: "SampleActivity2",
				Next: "End",
				Activity: &ActivityInvocation{
					Type: "SampleActivity2",
					ActivityBase: ActivityBase{
						Result: "SampleActivity2_Result",
					},
				},
			},
			{
				Name: "SampleActivity3",
				Next: "End",
				Activity: &ActivityInvocation{
					Type: "SampleActivity3",
					ActivityBase: ActivityBase{
						Result: "SampleActivity3_Result",
					},
				},
			},
			{
				Name: "Switch1",
				Switch: &Switch{
					Cases: []*Case{
						{
							Name: "SampleActivity1_Result == result2",
							Next: "SampleActivity2",
							Conditional: Expression{
								Basic: &Basic{
									Input:    "SampleActivity1_Result",
									Operator: "EQ",
									Value:    expected2,
								},
							},
						},
						{
							Name: "SampleActivity1_Result == result3",
							Next: "SampleActivity3",
							Conditional: Expression{
								Basic: &Basic{
									Input:    "SampleActivity1_Result",
									Operator: "EQ",
									Value:    expected3,
								},
							},
						},
					},
				},
			},
			{
				Name: "End",
				End:  &End{Terminate: false},
			},
		},
	}
	env.ExecuteWorkflow(ExecuteWorkflow, dsl)
	s.True(env.IsWorkflowCompleted())
	s.NoError(env.GetWorkflowError())
	var result map[string]any
	s.NoError(env.GetWorkflowResult(&result))
	s.Equal(expected1, result["SampleActivity1_Result"])
	s.Equal(expected3, result["SampleActivity3_Result"])
}

func (s *UnitTestSuite) TestExecuteWorkflow_Switch_Advance_Condition() {
	env := s.NewTestWorkflowEnvironment()
	expected := "result2"
	env.RegisterActivity(&Activities{})
	var a *Activities
	env.OnActivity(a.SampleActivity1, mock.Anything, mock.Anything).Return("result1", nil)
	env.OnActivity(a.SampleActivity2, mock.Anything, mock.Anything).Return("result2", nil)
	dsl := Workflow{
		Start: "SampleActivity1",
		States: []Statement{
			{
				Name: "SampleActivity1",
				Next: "Switch1",
				Activity: &ActivityInvocation{
					Type: "SampleActivity1",
					ActivityBase: ActivityBase{
						Result: "SampleActivity1_Result",
					},
				},
			},
			{
				Name: "Switch1",
				Switch: &Switch{
					Cases: []*Case{
						{
							Name: "SampleActivity1_Result == result1",
							Next: "SampleActivity2",
							Conditional: Expression{
								Advance: &Advance{
									Expression: "SampleActivity1_Result == \"result1\"",
								},
							},
						},
					},
				},
			},
			{
				Name: "SampleActivity2",
				Next: "End",
				Activity: &ActivityInvocation{
					Type: "SampleActivity2",
					ActivityBase: ActivityBase{
						Result: "SampleActivity2_Result",
					},
				},
			},
			{
				Name: "End",
				End:  &End{Terminate: false},
			},
		},
	}
	env.ExecuteWorkflow(ExecuteWorkflow, dsl)
	s.True(env.IsWorkflowCompleted())
	s.NoError(env.GetWorkflowError())
	var result map[string]any
	s.NoError(env.GetWorkflowResult(&result))
	s.Equal(expected, result["SampleActivity2_Result"])
}

func (s *UnitTestSuite) TestExecuteWorkflow_Form() {
	env := s.NewTestWorkflowEnvironment()
	expected := map[string]any{"submittedForm": map[string]any{"key": "value"}}
	env.RegisterActivity(&Activities{})
	var a *Activities
	env.OnActivity(a.SampleActivity1, mock.Anything, mock.Anything).Return("", nil)
	env.RegisterWorkflow(FormWorkflow)
	env.OnWorkflow("FormWorkflow", mock.Anything, mock.Anything, mock.Anything).Return(
		func(ctx workflow.Context, form FormActivity) (any, error) {
			return expected, nil
		})
	dsl := Workflow{
		Variables: map[string]interface{}{
			"formSchema": FormSchema{},
		},
		Start: "Form1",
		States: []Statement{
			{
				Name: "Form1",
				Next: "SampleActivity1",
				Form: &Form{
					ActivityBase: ActivityBase{
						Arguments: []string{"formSchema"},
						Result:    "Form1_Result",
					},
				},
			},
			{
				Name: "SampleActivity1",
				Next: "End",
				Activity: &ActivityInvocation{
					Type: "SampleActivity1",
					ActivityBase: ActivityBase{
						Result:    "",
						Arguments: []string{},
					},
				},
			},
			{
				Name: "End",
				End:  &End{Terminate: false},
			},
		},
	}
	env.ExecuteWorkflow(ExecuteWorkflow, dsl)
	s.True(env.IsWorkflowCompleted())
	s.NoError(env.GetWorkflowError())

	var result map[string]any
	s.NoError(env.GetWorkflowResult(&result))
	s.Equal(expected, result["Form1_Result"])
}

func (s *UnitTestSuite) TestExecuteWorkflow_Form_Error() {
	env := s.NewTestWorkflowEnvironment()
	expectedError := errors.New("child workflow error")
	env.RegisterWorkflow(FormWorkflow)
	env.OnWorkflow("FormWorkflow", mock.Anything, mock.Anything, mock.Anything).Return(
		func(ctx workflow.Context, form FormActivity) (any, error) {
			return nil, expectedError
		})
	dsl := Workflow{
		Variables: map[string]interface{}{
			"formSchema": FormSchema{},
		},
		Start: "Form1",
		States: []Statement{
			{
				Name: "Form1",
				Next: "SampleActivity1",
				Form: &Form{
					ActivityBase: ActivityBase{
						Arguments: []string{"formSchema"},
						Result:    "Form1_Result",
					},
				},
			},
		},
	}
	env.ExecuteWorkflow(ExecuteWorkflow, dsl)
	s.True(env.IsWorkflowCompleted())
	s.ErrorAs(env.GetWorkflowError(), &expectedError)
}

func (s *UnitTestSuite) TestExecuteWorkflow_Form_InvalidForm() {
	env := s.NewTestWorkflowEnvironment()
	dsl := Workflow{
		Variables: map[string]interface{}{
			"formSchema": nil,
		},
		Start: "Form1",
		States: []Statement{
			{
				Name: "Form1",
				Next: "SampleActivity1",
				Form: &Form{
					ActivityBase: ActivityBase{
						Arguments: []string{"formSchema"},
						Result:    "Form1_Result",
					},
				},
			},
		},
	}
	env.ExecuteWorkflow(ExecuteWorkflow, dsl)
	s.True(env.IsWorkflowCompleted())
	s.Error(env.GetWorkflowError())
}

func (s *UnitTestSuite) TestExecuteWorkflow_Event() {
	env := s.NewTestWorkflowEnvironment()
	expected := "event result"
	env.RegisterActivity(&Activities{})
	var a *Activities
	env.OnActivity(a.SampleEvent1, mock.Anything, mock.Anything).Return(expected, nil)
	dsl := Workflow{
		Start: "Event",
		States: []Statement{
			{
				Name: "Event",
				Next: "End",
				Event: &Event{
					Type:         "SampleEvent1",
					Arguments:    []string{""},
					Interrupting: true,
					Result:       "Event1_Result",
				},
			},
			{
				Name: "End",
				End:  &End{Terminate: false},
			},
		},
	}
	env.ExecuteWorkflow(ExecuteWorkflow, dsl)
	s.True(env.IsWorkflowCompleted())
	s.NoError(env.GetWorkflowError())
	var result map[string]any
	s.NoError(env.GetWorkflowResult(&result))
	s.Equal(expected, result["Event1_Result"])
}

func (s *UnitTestSuite) TestExecuteWorkflow_Event_Error() {
	env := s.NewTestWorkflowEnvironment()
	expectedError := errors.New("event error")
	env.RegisterActivity(&Activities{})
	var a *Activities
	env.OnActivity(a.SampleEvent1, mock.Anything, mock.Anything).Return("", expectedError)
	dsl := Workflow{
		Start: "Event",
		States: []Statement{
			{
				Name: "Event",
				Next: "End",
				Event: &Event{
					Type:         "SampleEvent1",
					Arguments:    []string{""},
					Interrupting: true,
					Result:       "Event1_Result",
				},
			},
			{
				Name: "End",
				End:  &End{Terminate: false},
			},
		},
	}
	env.ExecuteWorkflow(ExecuteWorkflow, dsl)
	s.True(env.IsWorkflowCompleted())
	s.ErrorAs(env.GetWorkflowError(), &expectedError)
}

func (s *UnitTestSuite) TestExecuteWorkflow_Event_Interrupting() {
	env := s.NewTestWorkflowEnvironment()
	expected := "interrupted"
	env.RegisterActivity(&Activities{})
	var a *Activities
	env.OnActivity(a.SampleEvent1, mock.Anything, mock.Anything).Return(expected, nil)
	dsl := Workflow{
		Variables: map[string]interface{}{
			"Delay_Input": 1, "Activity1_Input": "test",
		},
		Start: "Delay",
		States: []Statement{
			{
				Name: "Delay",
				Next: "Activity1",
				Activity: &ActivityInvocation{
					Type: "Delay",
					ActivityBase: ActivityBase{
						Result:         "Delay_Result",
						Arguments:      []string{"Delay_Input"},
						BoundaryEvents: []string{"Event1"},
					},
					Options: &ActivityInvocationOptions{
						TaskQueue:              "test",
						HeartbeatTimeout:       300,
						ScheduleToCloseTimeout: 300,
						ScheduleToStartTimeout: 300,
						StartToCloseTimeout:    300,
						WaitForCancellation:    true,
					},
				},
			},
			{
				Name: "Activity1",
				Next: "End",
				Activity: &ActivityInvocation{
					Type: "SampleActivity1",
					Options: &ActivityInvocationOptions{
						TaskQueue:              "test",
						HeartbeatTimeout:       300,
						ScheduleToCloseTimeout: 300,
						ScheduleToStartTimeout: 300,
						StartToCloseTimeout:    300,
						WaitForCancellation:    true,
					},
					ActivityBase: ActivityBase{
						Result:    "Activity1_Result",
						Arguments: []string{"Activity1_Input"},
					},
				},
			},
			{
				Name: "Event1",
				Next: "End",
				Event: &Event{
					Type:         "SampleEvent1",
					Arguments:    []string{""},
					Interrupting: true,
					Result:       "Event1_Result",
				},
			},
			{
				Name: "End",
				End:  &End{Terminate: false},
			},
		},
	}
	env.ExecuteWorkflow(ExecuteWorkflow, dsl)
	s.True(env.IsWorkflowCompleted())
	s.NoError(env.GetWorkflowError())
	var result map[string]any
	s.NoError(env.GetWorkflowResult(&result))
	s.Equal(expected, result["Event1_Result"])
}

func (s *UnitTestSuite) TestExecuteWorkflow_Event_NonInterrupting() {
	env := s.NewTestWorkflowEnvironment()
	expected := "Activity1 not interrupted"
	env.RegisterActivity(&Activities{})
	var a *Activities
	env.OnActivity(a.SampleEvent1, mock.Anything, mock.Anything).Return("", nil)
	env.OnActivity(a.SampleActivity1, mock.Anything, mock.Anything).Return(expected, nil)
	dsl := Workflow{
		Variables: map[string]interface{}{
			"Delay_Input": 1, "Activity1_Input": "test",
		},
		Start: "Delay",
		States: []Statement{
			{
				Name: "Delay",
				Next: "Activity1",
				Activity: &ActivityInvocation{
					Type: "Delay",
					Options: &ActivityInvocationOptions{
						TaskQueue:              "test",
						HeartbeatTimeout:       300,
						ScheduleToCloseTimeout: 300,
						ScheduleToStartTimeout: 300,
						StartToCloseTimeout:    300,
						WaitForCancellation:    true,
					},
					ActivityBase: ActivityBase{
						Result:         "Delay_Result",
						Arguments:      []string{"Delay_Input"},
						BoundaryEvents: []string{"Event1"},
					},
				},
			},
			{
				Name: "Activity1",
				Next: "End",
				Activity: &ActivityInvocation{
					Type: "SampleActivity1",
					Options: &ActivityInvocationOptions{
						TaskQueue:              "test",
						HeartbeatTimeout:       300,
						ScheduleToCloseTimeout: 300,
						ScheduleToStartTimeout: 300,
						StartToCloseTimeout:    300,
						WaitForCancellation:    true,
					},
					ActivityBase: ActivityBase{
						Result:    "Activity1_Result",
						Arguments: []string{"Activity1_Input"},
					},
				},
			},
			{
				Name: "Event1",
				Next: "End",
				Event: &Event{
					Type:         "SampleEvent1",
					Arguments:    []string{""},
					Interrupting: false,
					Result:       "Event1_Result",
				},
			},
			{
				Name: "End",
				End:  &End{Terminate: false},
			},
		},
	}
	env.ExecuteWorkflow(ExecuteWorkflow, dsl)
	s.True(env.IsWorkflowCompleted())
	s.NoError(env.GetWorkflowError())
	var result map[string]any
	s.NoError(env.GetWorkflowResult(&result))
	s.Equal(expected, result["Activity1_Result"])
}

func (s *UnitTestSuite) TestExecuteWorkflow_Event_NonInterrupting_Error() {
	env := s.NewTestWorkflowEnvironment()
	expectedError := errors.New("event error")
	env.RegisterActivity(&Activities{})
	var a *Activities
	env.OnActivity(a.SampleEvent1, mock.Anything, mock.Anything).Return("", nil)
	env.OnActivity(a.SampleActivity1, mock.Anything, mock.Anything).Return("", expectedError)
	dsl := Workflow{
		Variables: map[string]interface{}{
			"Delay_Input": 1, "Activity1_Input": "test",
		},
		Start: "Delay",
		States: []Statement{
			{
				Name: "Delay",
				Next: "End",
				Activity: &ActivityInvocation{
					Type: "Delay",
					Options: &ActivityInvocationOptions{
						TaskQueue:              "test",
						HeartbeatTimeout:       300,
						ScheduleToCloseTimeout: 300,
						ScheduleToStartTimeout: 300,
						StartToCloseTimeout:    300,
						WaitForCancellation:    true,
					},
					ActivityBase: ActivityBase{
						Result:         "Delay_Result",
						Arguments:      []string{"Delay_Input"},
						BoundaryEvents: []string{"Event1"},
					},
				},
			},
			{
				Name: "Event1",
				Next: "Activity1",
				Event: &Event{
					Type:         "SampleEvent1",
					Arguments:    []string{""},
					Interrupting: false,
					Result:       "Event1_Result",
				},
			},
			{
				Name: "Activity1",
				Next: "End",
				Activity: &ActivityInvocation{
					Type: "SampleActivity1",
					Options: &ActivityInvocationOptions{
						TaskQueue:              "test",
						HeartbeatTimeout:       300,
						ScheduleToCloseTimeout: 300,
						ScheduleToStartTimeout: 300,
						StartToCloseTimeout:    300,
						WaitForCancellation:    true,
					},
					ActivityBase: ActivityBase{
						Result:    "Activity1_Result",
						Arguments: []string{"Activity1_Input"},
					},
				},
			},
			{
				Name: "End",
				End:  &End{Terminate: false},
			},
		},
	}
	env.ExecuteWorkflow(ExecuteWorkflow, dsl)
	s.True(env.IsWorkflowCompleted())
	s.ErrorAs(env.GetWorkflowError(), &expectedError)
}

func Test_compare(t *testing.T) {
	type args[T interface{ float64 | string }] struct {
		op  string
		lhs T
		rhs T
	}
	type testCase[T interface{ float64 | string }] struct {
		name    string
		args    args[T]
		want    bool
		wantErr bool
	}
	testsFloat := []testCase[float64]{
		{
			name: "equal (float64)",
			args: args[float64]{
				op:  "EQ",
				lhs: 1.0,
				rhs: 1.0,
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "not equal (float64)",
			args: args[float64]{
				op:  "NEQ",
				lhs: 1.0,
				rhs: 2.0,
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "more than (float64)",
			args: args[float64]{
				op:  "MT",
				lhs: 2.0,
				rhs: 1.0,
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "less than (float64)",
			args: args[float64]{
				op:  "LT",
				lhs: 1.0,
				rhs: 2.0,
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "more than or equal (float64)",
			args: args[float64]{
				op:  "MTE",
				lhs: 2.0,
				rhs: 1.0,
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "less than or equal (float64)",
			args: args[float64]{
				op:  "LTE",
				lhs: 1.0,
				rhs: 2.0,
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "invalid operator (float64)",
			args: args[float64]{
				op:  "",
				lhs: 1.0,
				rhs: 2.0,
			},
			want:    false,
			wantErr: true,
		},
	}
	for _, tt := range testsFloat {
		t.Run(tt.name, func(t *testing.T) {
			got, err := compare(tt.args.op, tt.args.lhs, tt.args.rhs)
			if (err != nil) != tt.wantErr {
				t.Errorf("compare() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("compare() got = %v, want %v", got, tt.want)
			}
		})
	}
	testsString := []testCase[string]{
		{
			name: "equal (string)",
			args: args[string]{
				op:  "EQ",
				lhs: "hello",
				rhs: "hello",
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "not equal (string)",
			args: args[string]{
				op:  "NEQ",
				lhs: "hello",
				rhs: "hi",
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "more than (string)",
			args: args[string]{
				op:  "MT",
				lhs: "Ab",
				rhs: "ABCDE",
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "less than (string)",
			args: args[string]{
				op:  "LT",
				lhs: "aB",
				rhs: "abcde",
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "more than or equal (string)",
			args: args[string]{
				op:  "MTE",
				lhs: "hello123",
				rhs: "hello",
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "less than or equal (string)",
			args: args[string]{
				op:  "LTE",
				lhs: "hello",
				rhs: "hello123",
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "invalid operator (string)",
			args: args[string]{
				op:  "",
				lhs: "abc",
				rhs: "abc",
			},
			want:    false,
			wantErr: true,
		},
	}
	for _, tt := range testsString {
		t.Run(tt.name, func(t *testing.T) {
			got, err := compare(tt.args.op, tt.args.lhs, tt.args.rhs)
			if (err != nil) != tt.wantErr {
				t.Errorf("compare() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("compare() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_compareBool(t *testing.T) {
	type args struct {
		op  string
		lhs bool
		rhs bool
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "equal",
			args: args{
				op:  "EQ",
				lhs: true,
				rhs: true,
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "not equal",
			args: args{
				op:  "NEQ",
				lhs: false,
				rhs: true,
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "invalid operator",
			args: args{
				op:  "",
				lhs: false,
				rhs: false,
			},
			want:    false,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := compareBool(tt.args.op, tt.args.lhs, tt.args.rhs)
			if (err != nil) != tt.wantErr {
				t.Errorf("compareBool() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("compareBool() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_evaluateCondition(t *testing.T) {
	type args struct {
		op    string
		input any
		value any
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "float64 evaluation",
			args: args{
				op:    "EQ",
				input: 1.101,
				value: 1.101,
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "float64 evaluation error",
			args: args{
				op:    "",
				input: 1.101,
				value: 1.101,
			},
			want:    false,
			wantErr: true,
		},
		{
			name: "string evaluation",
			args: args{
				op:    "EQ",
				input: "abcd",
				value: "abcd",
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "string evaluatione error",
			args: args{
				op:    "",
				input: "abcd",
				value: "abcd",
			},
			want:    false,
			wantErr: true,
		},
		{
			name: "bool evaluation",
			args: args{
				op:    "EQ",
				input: true,
				value: true,
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "bool evaluation error",
			args: args{
				op:    "",
				input: true,
				value: true,
			},
			want:    false,
			wantErr: true,
		},
		{
			name: "invalid type evaluation",
			args: args{
				op:    "EQ",
				input: map[string]any{"key": "value"},
				value: map[string]any{"key": "value"},
			},
			want:    false,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := evaluateCondition(tt.args.op, tt.args.input, tt.args.value)
			if (err != nil) != tt.wantErr {
				t.Errorf("evaluateCondition() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("evaluateCondition() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func (s *UnitTestSuite) TestRecoverWorkflow() {
	env := s.NewTestWorkflowEnvironment()
	input := []string{"condition1", "condition2", "condition3"}
	env.RegisterDelayedCallback(func() {
		env.SignalWorkflow("testSignal", "condition1")
	}, time.Second)
	env.ExecuteWorkflow(RecoverWorkflow, input, "testSignal")
	s.True(env.IsWorkflowCompleted())
	s.NoError(env.GetWorkflowError())
}

func (s *UnitTestSuite) TestRecoverWorkflow_Timeout() {
	env := s.NewTestWorkflowEnvironment()
	input := []string{"condition1", "condition2", "condition3"}
	env.ExecuteWorkflow(RecoverWorkflow, input, "testSignal")
	s.True(env.IsWorkflowCompleted())
	s.Error(env.GetWorkflowError())
}

func (s *UnitTestSuite) TestFormWorkflow() {
	env := s.NewTestWorkflowEnvironment()
	expected := map[string]any{"textfield": "test"}
	formData := FormActivity{Name: "Form1", Schema: FormSchema{Components: []Component{{Type: "textfield"}}}}
	env.RegisterDelayedCallback(func() {
		env.SignalWorkflow("Form1", map[string]any{"textfield": "test"})
	}, time.Second)
	env.ExecuteWorkflow(FormWorkflow, formData)
	s.True(env.IsWorkflowCompleted())
	var result interface{}
	s.NoError(env.GetWorkflowResult(&result))
	s.Equal(expected, result)
}

func (s *UnitTestSuite) TestFormWorkflow_Invalid_Input() {
	env := s.NewTestWorkflowEnvironment()
	formData := FormActivity{}
	env.RegisterDelayedCallback(func() {
		env.SignalWorkflow("Form1", map[string]any{"textfield": "test"})
	}, time.Second)
	env.ExecuteWorkflow(FormWorkflow, formData)
	s.True(env.IsWorkflowCompleted())
	s.Error(env.GetWorkflowError())
}

func (s *UnitTestSuite) TestFormSubmit() {
	server := httptest.NewServer(http.HandlerFunc(func(rw http.ResponseWriter, req *http.Request) {
		//nolint:errcheck
		rw.Write([]byte(`{"key": "value"}`))
	}))
	defer server.Close()

	env := s.NewTestWorkflowEnvironment()
	expected := map[string]any{"key": "value"}
	formData := FormActivity{Name: "Form1", Schema: FormSchema{Components: []Component{{Type: "textfield"}}}}
	env.RegisterDelayedCallback(func() {
		env.SignalWorkflow("Form1", expected)
	}, time.Second)
	env.ExecuteWorkflow(FormWorkflow, formData)
	s.True(env.IsWorkflowCompleted())
	var result map[string]any
	s.NoError(env.GetWorkflowResult(&result))
	s.Equal(expected, result)
}

func Test_findNextActivities(t *testing.T) {
	type args struct {
		start  string
		states []Statement
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "OK findNextActivities",
			args: args{
				start: "switch1",
				states: []Statement{
					{
						Name: "switch1",
						Switch: &Switch{
							Cases: []*Case{
								{
									Name: "go to parallel",
									Next: "parallel1",
								},
								{
									Name: "go to switch2",
									Next: "activity3",
								},
							},
							Default: &Default{Next: "activity4"},
						},
					},
					{
						Name: "parallel1",
						Parallel: &Parallel{
							Branches: []Branch{
								{
									Next: "activity1",
								},
								{
									Next: "activity2",
								},
							},
						},
					},
					{
						Name:     "activity1",
						Activity: &ActivityInvocation{},
					},
					{
						Name:     "activity2",
						Activity: &ActivityInvocation{},
					},
					{
						Name:     "activity3",
						Activity: &ActivityInvocation{},
					},
					{
						Name:     "activity4",
						Activity: &ActivityInvocation{},
					},
				},
			},
			want: []string{"activity1", "activity2", "activity3", "activity4"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := findNextActivities(tt.args.start, tt.args.states); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("findNextActivities() = %v, want %v", got, tt.want)
			}
		})
	}
}
