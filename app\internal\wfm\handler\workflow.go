package handler

import (
	"errors"
	"io"
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
	"github.com/google/uuid"
	aohhttp "github.com/mssfoobar/aoh-golib/http"
	aohlog "github.com/mssfoobar/aoh-golib/logger"
	"github.com/mssfoobar/app/wfe/internal/model"
	"github.com/mssfoobar/app/wfe/internal/wfm/handler/util"
	"github.com/mssfoobar/app/wfe/internal/wfm/temporal"
	"go.uber.org/zap"
)

type (
	Workflow struct {
		workflowStore model.WorkflowTemplateStore
		temporal      temporal.Service
	}

	startReqBody struct {
		TemplateId string         `json:"template_id"`
		Metadata   map[string]any `json:"metadata,omitempty"`
	}

	workflowUriParam struct {
		WorkflowId string `json:"workflow_id" validate:"required"`
	}

	terminateReqBody struct {
		Reason string `json:"reason,omitempty"`
	}

	signalUriParam struct {
		WorkflowId   string `json:"workflow_id" validate:"required"`
		ActivityName string `json:"activity_name" validate:"required"`
	}

	signalReqBody struct {
		Data interface{} `json:"data"`
	}
)

func (r *startReqBody) Bind(_ *http.Request) error {
	if r.TemplateId == "" {
		return errors.New("missing required fields")
	}
	return nil
}

func (r *terminateReqBody) Bind(_ *http.Request) error {
	return nil
}

func (r *signalReqBody) Bind(_ *http.Request) error {
	return nil
}

func RegisterExecute(factory *model.Factory, temporal temporal.Service, keycloakUrl string) http.Handler {
	r := chi.NewRouter()
	util.SetMiddleware(r, keycloakUrl)

	w := Workflow{
		workflowStore: factory.NewWorkflowTemplateStore(),
		temporal:      temporal,
	}

	r.Route("/", func(r chi.Router) {
		r.Post("/", w.Start)
		r.Get("/", w.ListRunningWorkflow)
		r.Post("/{workflow_id}/activity_name/{activity_name}", w.Signal)
		r.Delete("/{workflow_id}", w.Terminate)
		r.Get("/{workflow_id}", w.GetWorkflowHistory)
	})

	return r
}

// Start execute temporal workflow by workflow template
func (wf *Workflow) Start(w http.ResponseWriter, r *http.Request) {
	var (
		req      startReqBody
		id       uuid.UUID
		jwt      *aohhttp.JwtClaim
		workflow *model.WorkflowTemplateResponse
		run      *temporal.WorkflowRun
		err      error
	)

	if err := render.Bind(r, &req); err != nil {
		_ = render.Render(w, r, aohhttp.ErrResponse(http.StatusBadRequest, "", []error{err}))
		return
	}

	if id, err = util.ParseUUID(w, r, req.TemplateId); err != nil {
		return
	}

	if jwt, err = util.GetJwt(w, r); err != nil {
		return
	}

	workflow, err = wf.workflowStore.GetWorkflowTemplate(r.Context(), &model.GetWorkflowTemplateRequest{
		Id:       id,
		TenantId: jwt.ActiveTenant.Id,
	})
	if err != nil {
		aohlog.Error("[Workflow] get workflow template failed",
			zap.String("template_id", req.TemplateId),
			zap.String("tenant_id", jwt.ActiveTenant.Id),
			zap.Error(err))
		_ = render.Render(w, r, aohhttp.ErrResponse(http.StatusInternalServerError, "", nil))
		return
	}

	run, err = wf.temporal.ExecuteWorkflow(r.Context(), workflow.WorkflowJson, req.Metadata)
	if err != nil {
		aohlog.Error("[Workflow] start workflow failed",
			zap.String("template_id", req.TemplateId),
			zap.String("tenant_id", jwt.ActiveTenant.Id),
			zap.Error(err))
		_ = render.Render(w, r, aohhttp.ErrResponse(http.StatusInternalServerError, "", nil))
		return
	}

	aohlog.Info("[Workflow] started workflow successfully",
		zap.String("workflow_id", run.WorkflowId),
		zap.String("template_id", req.TemplateId))
	_ = render.Render(w, r, aohhttp.Response(http.StatusOK, "", run))
}

// Signal send data to temporal signal channel
func (wf *Workflow) Signal(w http.ResponseWriter, r *http.Request) {
	var (
		uri signalUriParam
		req signalReqBody
		err error
	)

	if err = util.ValidateUriParams(w, r, &uri); err != nil {
		return
	}

	if err := render.Bind(r, &req); err != nil {
		_ = render.Render(w, r, aohhttp.ErrResponse(http.StatusBadRequest, "", []error{err}))
		return
	}

	err = wf.temporal.SignalWorkflow(r.Context(), uri.WorkflowId, uri.ActivityName, req.Data)
	if err != nil {
		aohlog.Error("[Workflow] signal workflow failed",
			zap.String("workflow_id", uri.WorkflowId),
			zap.String("activity_name", uri.ActivityName),
			zap.Any("data", req.Data),
			zap.Error(err))
		_ = render.Render(w, r, aohhttp.ErrResponse(http.StatusInternalServerError, "", nil))
		return
	}

	aohlog.Info(
		"[Workflow] signaled workflow successfully",
		zap.String("workflow_id", uri.WorkflowId),
		zap.String("activity_name", uri.ActivityName),
		zap.Any("data", req.Data),
	)
	_ = render.Render(w, r, aohhttp.Response(http.StatusOK, "", nil))
}

// Terminate terminates temporal workflow
func (wf *Workflow) Terminate(w http.ResponseWriter, r *http.Request) {
	var (
		uri workflowUriParam
		req terminateReqBody
	)

	if err := util.ValidateUriParams(w, r, &uri); err != nil {
		return
	}

	if err := render.Bind(r, &req); err != nil && !errors.Is(err, io.EOF) {
		_ = render.Render(w, r, aohhttp.ErrResponse(http.StatusBadRequest, "", []error{err}))
		return
	}

	err := wf.temporal.TerminateWorkflow(r.Context(), uri.WorkflowId, req.Reason)
	if err != nil {
		aohlog.Error("[Workflow] terminate workflow failed",
			zap.String("workflow_id", uri.WorkflowId),
			zap.String("reason", req.Reason),
			zap.Error(err))
		_ = render.Render(w, r, aohhttp.ErrResponse(http.StatusInternalServerError, "", nil))
		return
	}

	aohlog.Info("[Workflow] terminated workflow successfully", zap.String("workflow_id", uri.WorkflowId))
	_ = render.Render(w, r, aohhttp.Response(http.StatusOK, "", nil))
}

func (wf *Workflow) ListRunningWorkflow(w http.ResponseWriter, r *http.Request) {
	page, err := util.GetQueryPagination(w, r)
	if err != nil {
		return
	}

	var startTimeAsc bool
	for _, v := range page.Sorts {
		if v.Column == "start_time" && v.Direction == "asc" {
			startTimeAsc = true
		}
	}

	resp, count, err := wf.temporal.ListOpenWorkflow(r.Context(), page.Number, page.Size, startTimeAsc)
	if err != nil {
		aohlog.Error("[Workflow] list running workflow failed", zap.Error(err))
		_ = render.Render(w, r, aohhttp.ErrResponse(http.StatusInternalServerError, "", nil))
		return
	}

	respPage := aohhttp.PageResponse{
		Number:       page.Number,
		Size:         page.Size,
		TotalRecords: count,
		Count:        len(resp),
		Sort:         []string{page.Sorts.String()},
	}

	aohlog.Info("[Workflow] list running workflow successfully", zap.Int("count", len(resp)))
	_ = render.Render(w, r, aohhttp.PaginationResponse(http.StatusOK, "", respPage, resp))
}

func (wf *Workflow) GetWorkflowHistory(w http.ResponseWriter, r *http.Request) {
	var (
		uri           workflowUriParam
		page          *aohhttp.PageRequest
		timestampDesc bool
		err           error
	)

	if err = util.ValidateUriParams(w, r, &uri); err != nil {
		return
	}

	if page, err = util.GetQueryPagination(w, r); err != nil {
		return
	}

	for _, v := range page.Sorts {
		if v.Column == "timestamp" && v.Direction == "desc" {
			timestampDesc = true
		}
	}

	resp, count, err := wf.temporal.GetWorkflowHistory(r.Context(), uri.WorkflowId, page.Number, page.Size, timestampDesc)
	if err != nil {
		aohlog.Error("[Workflow] get workflow history failed", zap.Error(err))
		_ = render.Render(w, r, aohhttp.ErrResponse(http.StatusInternalServerError, "", []error{err}))
		return
	}

	respPage := aohhttp.PageResponse{
		Number:       page.Number,
		Size:         page.Size,
		TotalRecords: count,
		Count:        len(resp),
		Sort:         []string{page.Sorts.String()},
	}
	aohlog.Info("[Workflow] Get Workflow History")

	_ = render.Render(w, r, aohhttp.PaginationResponse(http.StatusOK, "", respPage, resp))
}
