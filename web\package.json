{"name": "@mssfoobar/wfe-web", "version": "2.0.0", "private": true, "type": "module", "scripts": {"dev": "env-cmd npx env-cmd -f .env.development --silent npx vite dev --host | pino-pretty -c -l -t", "build": "vite build", "preview": "vite preview", "test": "npm run test:integration && npm run test:unit", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --check . && eslint .", "format": "prettier --write .", "test:integration": "playwright test", "test:unit": "vitest run --coverage --watch false", "test:unit:dev": "vitest watch --ui --api 9527 --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.2", "@iconify-json/fa6-solid": "^1.2.1", "@iconify-json/mdi": "^1.2.0", "@iconify-json/mdi-light": "^1.2.0", "@iconify/svelte": "^4.0.2", "@iconify/tailwind": "^1.1.3", "@playwright/test": "1.45.3", "@storybook/addon-essentials": "^8.4.2", "@storybook/addon-interactions": "^8.4.2", "@storybook/addon-svelte-csf": "^5.0.0-next.10", "@storybook/addon-themes": "^8.4.2", "@storybook/blocks": "^8.4.2", "@storybook/svelte": "^8.4.2", "@storybook/sveltekit": "^8.4.2", "@storybook/test": "^8.4.2", "@sveltejs/adapter-node": "^5.2.2", "@sveltejs/kit": "^2.7.3", "@sveltejs/vite-plugin-svelte": "^4.0.0", "@types/eslint": "^9.6.0", "@types/node": "^22.8.6", "@vitest/coverage-v8": "^2.1.3", "@vitest/ui": "^2.1.3", "autoprefixer": "^10.4.19", "bits-ui": "^1.0.0-next.77", "clsx": "^2.1.1", "concurrently": "^9.0.1", "env-cmd": "^10.1.0", "eslint": "^9.13.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-storybook": "^0.11.0", "eslint-plugin-svelte": "^2.36.0", "formsnap": "^2.0.0", "globals": "^15.0.0", "lint-staged": "^15.2.10", "lucide-svelte": "^0.469.0", "mode-watcher": "^0.5.0", "paneforge": "^1.0.0-next.2", "pino-pretty": "^11.3.0", "postcss": "^8.4.40", "prettier": "^3.3.2", "prettier-plugin-svelte": "^3.2.6", "prettier-plugin-tailwindcss": "^0.6.5", "storybook": "^8.4.2", "svelte": "^5.1.4", "svelte-check": "^4.0.0", "svelte-sonner": "^0.3.28", "sveltekit-superforms": "^2.22.1", "tailwind-merge": "^2.5.4", "tailwind-variants": "^0.2.1", "tailwindcss": "^3.4.9", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.0", "typescript-eslint": "^8.0.0", "vite": "^5.0.3", "vite-tsconfig-paths": "^5.0.1", "vitest": "^2.0.0", "zod": "^3.24.1"}, "dependencies": {"@bpmn-io/form-js": "^1.9.0", "@codemirror/theme-one-dark": "^6.1.2", "@floating-ui/dom": "^1.3.0", "@fontsource-variable/geist": "^5.0.1", "@iconify/utils": "^2.1.33", "ajv": "^8.17.1", "change-case-all": "^2.1.0", "codemirror": "^6.0.1", "dayjs": "^1.11.12", "flowbite-svelte": "^0.48.4", "http-status-codes": "^2.3.0", "jwt-decode": "^4.0.0", "openid-client": "^6.1.3", "rappid": "file:rappid.tgz", "rough-notation": "^0.5.1", "svelte-codemirror-editor": "^1.4.1"}, "lint-staged": {"*.{ts,js,svelte}": ["prettier --check --write", "eslint"], "*.{css,md,html,json}": ["prettier --check --write"]}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}