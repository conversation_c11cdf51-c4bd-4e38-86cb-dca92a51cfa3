<script lang="ts">
	import * as Command from "$lib/aoh/wfd/components/ui/command/index.js";
	import * as Popover from "$lib/aoh/wfd/components/ui/popover/index.js";
	import { buttonVariants } from "$lib/aoh/wfd/components/ui/button";

	let {
		selectedItem,
		listData = [],
		onSelect = (_e: string) => {},
	}: { selectedItem: string; listData: string[]; onSelect: (e: string) => void } = $props();
	let openDropdown = $state(false);
</script>

<Popover.Root bind:open={openDropdown}>
	<Popover.Trigger
		role="combobox"
		class={buttonVariants({ variant: "outline" }) + " w-full truncate justify-between"}
	>
		<span class="truncate !block" title={selectedItem}>
			{selectedItem || ""}
		</span>
		<span class="icon-[fa6-solid--caret-down] min-w-[0.63em]" aria-hidden="true"></span>
	</Popover.Trigger>
	<Popover.Content class="p-0 w-[310px]">
		<Command.Root>
			<Command.Input placeholder="Search" />
			<Command.List>
				<Command.Empty>Select Data Source</Command.Empty>
				<Command.Group>
					{#each listData as item}
						<Command.Item
							value={item}
							class="aria-selected:bg-primary aria-selected:text-primary-foreground"
							onSelect={() => {
								openDropdown = false;
								onSelect(item);
							}}
						>
							{item}
						</Command.Item>
					{/each}
				</Command.Group>
			</Command.List>
		</Command.Root>
	</Popover.Content>
</Popover.Root>
