<script lang="ts">
	import { onMount } from "svelte";
	import { type Activity } from "$lib/aoh/wfd/activitySDK/api";
	import { Accordion } from "bits-ui";
	import ChevronRight from "lucide-svelte/icons/chevron-right";
	import ArrowOutIcon from "lucide-svelte/icons/square-arrow-out-up-right";

	import { Label } from "../../components/ui/label";
	import { Textarea } from "../../components/ui/textarea";

	import EmailTooltip from "../SendSOPEmail/EmailTooltip.svelte";
	import DataDialog from "../SendSOPEmail/DataDialog.svelte";

	type EmailParamsKeys = "recipientsEmail" | "recipientsUsers" | "recipientsRoles";

	const parameterMapping = {
		recipientsEmail: "Recipients(Email Address)",
		recipientsUsers: "Recipients(C3 Users)",
		recipientsRoles: "Recipients(C3 Roles)",
	};

	// must declare this props to receive activity from parent
	let { activity }: { activity: Activity } = $props();

	let emailParams = $state(
		Object.keys(parameterMapping).reduce(
			(acc, key) => {
				acc[key as EmailParamsKeys] =
					(activity.getParameter(parameterMapping[key as EmailParamsKeys]) as string) || "";
				return acc;
			},
			{} as Record<EmailParamsKeys, string>
		)
	);
	let errorMessages = $state<Record<string, string>>({ recipientsEmail: "" });

	let iamsUsers = $state<any[]>([]);
	let iamsRoles = $state<any[]>([]);
	let currentKey: string = "";

	let open = $state(false);

	let modalTitle = $state("Select Users");
	let rows = $state<any[]>([]);
	let columns = $state<any[]>([]);
	let selectedItems = $state<any[]>([]);
	let keyToCompare = $state<string>("");

	onMount(async () => {
		try {
			const response = await fetch("wfd/api/MOHIncidentReport", {
				method: "GET",
			});
			const data = await response.json();

			if (!data) return;
			iamsUsers = data.users
				?.filter((i: Record<string, any>) => i.user?.email)
				?.map((i: Record<string, any>) => ({
					id: i.user?.id,
					name: `${i.user?.firstName || ""} ${i.user?.lastName || ""}`.trim() || i.user?.username,
					email: i.user?.email,
					userName: i.user?.username || "-",
				}));
			iamsRoles = data.roles;
		} catch (error) {
			console.error("Error fetching data:", error);
		}
	});

	function selectUsers(key: string) {
		open = true;
		currentKey = key;
		rows = iamsUsers;
		columns = [
			{ key: "name", name: "User" },
			{ key: "email", name: "Email" },
		];
		selectedItems =
			emailParams[key as EmailParamsKeys].split(", ")?.map((i) => {
				const [id] = i.split(":");
				return id;
			}) || [];
		keyToCompare = "id";
		modalTitle = "Select Users";
	}

	function selectRoles(key: string) {
		open = true;
		currentKey = key;
		rows = iamsRoles;
		columns = [
			{ key: "name", name: "Role" },
			{ key: "description", name: "Description" },
		];
		keyToCompare = "name";
		selectedItems = emailParams[key as EmailParamsKeys].split(", ") || [];
		modalTitle = "Select Roles";
	}

	const onFilteringClosed = (data: string[]) => {
		const savedKey = parameterMapping[currentKey as EmailParamsKeys];
		if (savedKey) {
			if (["recipientsUsers"].includes(currentKey)) {
				data = data.filter(Boolean).map((i) => {
					const user = iamsUsers?.find((user) => user.id === i);
					return user ? `${user.id}:${user.userName}:${user.email}` : i;
				});
			}
			const transformedData = (emailParams[currentKey as EmailParamsKeys] = data.filter(Boolean).join(", "));
			setTimeout(() => {
				activity.setParameter(savedKey, transformedData);
			}, 200);
		}
	};

	function getUserNames(idsString: string) {
		if (!idsString) return "";
		const ids = idsString.split(", ");
		return ids
			.map((item) => {
				const [id] = item.split(":");
				const user = iamsUsers?.find((user) => user.id === id);
				return user ? `${user.name}${user.email ? ` (${user.email})` : ""}` : "";
			})
			.filter(Boolean)
			.join(", ");
	}

	const validateEmails = (emails: string) => {
		if (!emails) return [];
		const nameEmailPattern = /^[\w\s]+ ?\([\w.-]+@[\w.-]+\.\w+\)$/;
		const invalidNameEmails = emails
			.split(/,|\n/)
			.map((entry) => entry.trim())
			.filter((entry) => entry !== "" && !nameEmailPattern.test(entry));
		return invalidNameEmails;
	};

	const saveEmailParam = (key: EmailParamsKeys) => {
		const invalidEmails = validateEmails(emailParams[key]);
		if (invalidEmails.length > 0) {
			errorMessages[key] = `Please enter email address in format: Name (<EMAIL>)`;
		} else {
			setTimeout(() => {
				activity.setParameter(parameterMapping[key], emailParams[key]);
			}, 200);
		}
	};

	const resetEmailError = (key: EmailParamsKeys) => {
		if (errorMessages[key]) {
			errorMessages[key] = "";
		}
	};
</script>

<Accordion.Root value={["1"]} class="w-[calc(100%+20px)] border border-solid mt-2 -mx-[10px] px-4" type="multiple">
	<Accordion.Item value="1" class="border-dark-10 px-1.5">
		<Accordion.Header>
			<Accordion.Trigger
				class="flex w-full flex-1 select-none items-center justify-between my-5 text-[15px] font-medium transition-all [&[data-state=open]>span>svg]:rotate-90"
			>
				<span
					class="hover:bg-dark-10 inline-flex mr-2 items-center justify-center rounded-[7px] bg-transparent"
				>
					<ChevronRight />
				</span>
				<span class="w-full text-left"> Recipients </span>
			</Accordion.Trigger>
		</Accordion.Header>
		<Accordion.Content class="text-sm tracking-[-0.01em]">
			<div class="pb-[25px] flex flex-col gap-4">
				<div class="form-field">
					<Label for="recipientsEmail">Email Address</Label>
					<div class="flex">
						<Textarea
							class="flex-1 min-h-[40px]"
							name="recipientsEmail"
							bind:value={emailParams.recipientsEmail}
							onblur={() => {
								saveEmailParam("recipientsEmail");
							}}
							oninput={() => {
								resetEmailError("recipientsEmail");
							}}
							placeholder="John Doe (<EMAIL>), ..."
						></Textarea>
						<EmailTooltip />
					</div>
					<div class="mt-2 text-red-600 text-xs">{@html errorMessages.recipientsEmail}</div>
				</div>
				<div class="form-field">
					<Label for="recipientsUsers">C3 Users</Label>
					<div class="flex">
						<Textarea
							readonly
							class="!w-full min-h-[62px]"
							name="recipientsUsers"
							value={getUserNames(emailParams.recipientsUsers)}
						></Textarea>
						<button
							class="ml-2"
							onclick={() => {
								selectUsers("recipientsUsers");
							}}><ArrowOutIcon /></button
						>
					</div>
				</div>
				<div class="form-field">
					<Label for="recipientsRoles">Roles</Label>
					<div class="flex">
						<Textarea
							readonly
							class="!w-full min-h-[62px]"
							name="recipientsRoles"
							value={emailParams.recipientsRoles}
						></Textarea>
						<button
							class="ml-2"
							onclick={() => {
								selectRoles("recipientsRoles");
							}}><ArrowOutIcon /></button
						>
					</div>
				</div>
			</div>
		</Accordion.Content>
	</Accordion.Item>
</Accordion.Root>

{#if open}
	<DataDialog
		bind:open
		{modalTitle}
		{rows}
		{columns}
		{keyToCompare}
		{selectedItems}
		on:close={(e) => onFilteringClosed(e?.detail)}
	/>
{/if}
