<script lang="ts">
	import Button from "$lib/aoh/core/components/ui/button/button.svelte";
	import type { PageData } from "./$types";

	interface Props {
		data: PageData;
	}
	const { data }: Props = $props();

	async function onLogin() {
		window.location.href = data.LOGIN_API;
	}
</script>

<div class="grid h-screen w-full items-center justify-items-center bg-background px-32">
	<div class="flex w-1/2 flex-col items-center justify-items-center gap-12">
		<img src="/images/logo.png" alt="AGIL Ops Hub Logo" class="max-w-[318px]" />
		<p class="text-4xl leading-[115%] tracking-[0.4rem] font-semibold text-center text-foreground">AGIL OPS HUB</p>
		<Button class="w-[500px] xl:w-1/2 h-10 bg-primary uppercase" onclick={onLogin}>Login</Button>
	</div>
</div>
