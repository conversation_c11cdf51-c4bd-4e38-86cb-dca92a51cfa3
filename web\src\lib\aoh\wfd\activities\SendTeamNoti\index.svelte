<script lang="ts">
	import { onMount } from "svelte";
	import { type Activity } from "$lib/aoh/wfd/activitySDK/api";
	import { Accordion } from "bits-ui";
	import ChevronRight from "lucide-svelte/icons/chevron-right";
	import ArrowOutIcon from "lucide-svelte/icons/square-arrow-out-up-right";
	import * as Popover from "$lib/aoh/wfd/components/ui/popover/index.js";
	import * as Command from "$lib/aoh/wfd/components/ui/command/index.js";
	import { Label } from "../../components/ui/label";
	import { Textarea } from "../../components/ui/textarea";
	import Input from "../../components/ui/input/input.svelte";
	import EmailTooltip from "../SendSOPEmail/EmailTooltip.svelte";
	import DataDialog from "../SendSOPEmail/DataDialog.svelte";
	import { buttonVariants } from "$lib/aoh/wfd/components/ui/button";
	import { SelectSearch } from "../../components/ui/select";
	import { dia } from "rappid/rappid";
	import Tooltip from "$lib/components/ui/tooltip/tooltip.svelte";

	type TeamNotiParamsKeys = "subject" | "messageBody" | "recipientsEmail" | "recipientsUsers" | "recipientsRoles";
	const parameterMapping = {
		subject: "Subject",
		messageBody: "MessageBody",
		recipientsEmail: "Recipients(Email Address)",
		recipientsUsers: "Recipients(C3 Users)",
		recipientsRoles: "Recipients(C3 Roles)",
	};

	// must declare this props to receive activity from parent
	let { activity, graph }: { activity: Activity; graph?: dia.Graph } = $props();

	let openDropdown = $state(false);
	let teamNotiParams = $state(
		Object.keys(parameterMapping).reduce(
			(acc, key) => {
				acc[key as TeamNotiParamsKeys] =
					(activity.getParameter(parameterMapping[key as TeamNotiParamsKeys]) as string) || "";
				return acc;
			},
			{} as Record<TeamNotiParamsKeys, string>
		)
	);
	let errorMessages = $state<Record<string, string>>({ recipientsEmail: "" });

	let iamsUsers = $state<any[]>([]);
	let iamsRoles = $state<any[]>([]);
	let currentKey: string = "";

	let open = $state(false);
	let accordionValue = $state(["1", "2"]); // Track accordion state

	let modalTitle = $state("Select Users");
	let rows = $state<any[]>([]);
	let columns = $state<any[]>([]);
	let selectedItems = $state<any[]>([]);
	let dataSourceList = $state<any[]>([]);
	let keyToCompare = $state<string>("");
	let selectedParameter: string = $state((activity.getParameter("selectedParameter") as string) || "");
	// Get ALL current elements from the live graph (including unsaved ones)
	let liveGraphElements = $derived(graph ? graph.getElements() : []);

	let liveStateNames = $derived(
		liveGraphElements
			.filter((el) => el.attr("type/text") === "SetMilestone")
			.map((el) => el.attr("label/text") || `Unnamed_${el.id}`)
	);

	onMount(async () => {
		try {
			const response = await fetch("wfd/api/MOHIncidentReport", {
				method: "GET",
			});
			const data = await response.json();

			if (!data) return;
			iamsUsers = data.users
				?.filter((i: Record<string, any>) => i.user?.email)
				?.map((i: Record<string, any>) => ({
					id: i.user?.id,
					name: `${i.user?.firstName || ""} ${i.user?.lastName || ""}`.trim() || i.user?.username,
					email: i.user?.email,
					userName: i.user?.username || "-",
				}));
			iamsRoles = data.roles;
		} catch (error) {
			console.error("Error fetching data:", error);
		}
	});

	onMount(async () => {
		try {
			const response = await fetch("wfd/api/SendSOPEmail", {
				method: "GET",
			});
			const data = await response.json();

			if (!data) return;
			iamsUsers = data.users?.map((i: Record<string, any>) => ({
				id: i.user?.id,
				name: `${i.user?.firstName || ""} ${i.user?.lastName || ""}`.trim() || i.user?.username,
				email: i.user?.email,
				username: i.user?.username,
			}));
			iamsRoles = data.roles;
			dataSourceList = data.datasources?.map((i: Record<string, any>) => i.Name) || [];
		} catch (error) {
			console.error("Error fetching data:", error);
		}
	});

	function selectUsers(key: string) {
		open = true;
		currentKey = key;
		rows = iamsUsers;
		columns = [
			{ key: "name", name: "User" },
			{ key: "username", name: "Username" },
			{ key: "email", name: "Email" },
		];
		selectedItems =
			teamNotiParams[key as TeamNotiParamsKeys].split(", ")?.map((i) => {
				const [id] = i.split(":");
				return id;
			}) || [];
		keyToCompare = "id";
		modalTitle = "Select Users";
	}

	function selectRoles(key: string) {
		open = true;
		currentKey = key;
		rows = iamsRoles;
		columns = [
			{ key: "name", name: "Role" },
			{ key: "description", name: "Description" },
		];
		keyToCompare = "name";
		selectedItems = teamNotiParams[key as TeamNotiParamsKeys].split(", ") || [];
		modalTitle = "Select Roles";
	}

	const onFilteringClosed = (data: string[]) => {
		const savedKey = parameterMapping[currentKey as TeamNotiParamsKeys];
		if (savedKey) {
			if (["recipientsUsers"].includes(currentKey)) {
				data = data.filter(Boolean).map((i) => {
					const user = iamsUsers?.find((user) => user.id === i);
					return user ? `${user.id}:${user.username}:${user.email}` : i;
				});
			}
			const transformedData = (teamNotiParams[currentKey as TeamNotiParamsKeys] = data
				.filter(Boolean)
				.join(", "));
			setTimeout(() => {
				activity.setParameter(savedKey, transformedData);
			}, 200);
		}
	};

	function getUserNames(idsString: string) {
		if (!idsString) return "";
		const ids = idsString.split(", ");
		return ids
			.map((item) => {
				const [id] = item.split(":");
				const user = iamsUsers?.find((user) => user.id === id);
				return user ? `${user.name}${user.email ? ` (${user.email})` : ""}` : "";
			})
			.filter(Boolean)
			.join(", ");
	}

	const validateEmails = (emails: string) => {
		if (!emails) return [];
		const nameEmailPattern = /^[\w\s]+ ?\([\w.-]+@[\w.-]+\.\w+\)$/;
		const invalidNameEmails = emails
			.split(/,|\n/)
			.map((entry) => entry.trim())
			.filter((entry) => entry !== "" && !nameEmailPattern.test(entry));
		return invalidNameEmails;
	};

	const saveEmailParam = (key: TeamNotiParamsKeys) => {
		const invalidEmails = validateEmails(teamNotiParams[key]);
		if (invalidEmails.length > 0) {
			errorMessages[key] = `Please enter email address in format: Name (<EMAIL>)`;
		} else {
			setTimeout(() => {
				activity.setParameter(parameterMapping[key], teamNotiParams[key]);
			}, 200);
		}
	};

	const resetEmailError = (key: TeamNotiParamsKeys) => {
		if (errorMessages[key]) {
			errorMessages[key] = "";
		}
	};
	async function copyToClipboard(value: string) {
		if (!value) return;

		const text = `{{${value}}}`;
		if (window.isSecureContext && navigator.clipboard) {
			try {
				await navigator.clipboard.writeText(text);
			} catch (err) {
				console.error("Failed to write to clipboard:", err);
			}
		} else {
			// Fallback for insecure contexts
			const textarea = document.createElement("textarea");
			textarea.value = text;
			textarea.style.position = "fixed";
			textarea.style.left = "-9999px";
			document.body.appendChild(textarea);
			textarea.focus();
			textarea.select();
			try {
				document.execCommand("copy");
			} catch (err) {
				console.error("Fallback copy failed:", err);
			} finally {
				document.body.removeChild(textarea);
			}
		}
	}
</script>

<Accordion.Root
	bind:value={accordionValue}
	class="w-[calc(100%+20px)] border border-solid mt-2 -mx-[10px] px-4"
	type="multiple"
>
	<div class="flex flex-col mt-4">
		<Label>Select Target Milestone</Label>
		<div class="flex">
			<SelectSearch
				listData={liveStateNames || ["No states available"]}
				selectedItem={activity.getParameter("ActivityName") as string}
				onSelect={(value) => {
					activity.setParameter("ActivityName", value);
				}}
			/>
			<Tooltip message="Select the target milestone to update user response." />
		</div>
	</div>
	<Accordion.Item value="1" class="border-dark-10 border-b px-1.5">
		<Accordion.Header>
			<Accordion.Trigger
				class="my-5 flex w-full flex-1 select-none items-center justify-between text-[15px] font-medium transition-all [&[data-state=open]>span>svg]:rotate-90"
			>
				<span
					class="hover:bg-dark-10 inline-flex mr-2 items-center justify-center rounded-[7px] bg-transparent"
				>
					<ChevronRight />
				</span>
				<span class="w-full text-left"> SOP Notification Message </span>
			</Accordion.Trigger>
		</Accordion.Header>
		<Accordion.Content class="text-sm tracking-[-0.01em]">
			<div class="pb-[25px] flex flex-col gap-4">
				<div class="flex gap-2 items-center">
					<Label for="datasourceList" class="whitespace-nowrap">Select Parameter</Label>
					<Popover.Root bind:open={openDropdown}>
						<Popover.Trigger
							role="combobox"
							class={buttonVariants({ variant: "outline" }) + " w-full truncate justify-between"}
						>
							<span class="truncate !block" title={selectedParameter}>
								{selectedParameter || ""}
							</span>
							<span class="icon-[fa6-solid--caret-down] min-w-[0.63em]" aria-hidden="true"></span>
						</Popover.Trigger>
						<Popover.Content class="p-0 w-[310px]">
							<Command.Root>
								<Command.Input placeholder="Search" />
								<Command.List>
									<Command.Empty>Select Parameter</Command.Empty>
									<Command.Group>
										{#each dataSourceList as type}
											<Command.Item
												value={type}
												class="aria-selected:bg-primary aria-selected:text-primary-foreground"
												onSelect={() => {
													openDropdown = false;
													setTimeout(() => {
														activity.setParameter("selectedParameter", type);
													}, 200);
												}}
											>
												{type}
											</Command.Item>
										{/each}
									</Command.Group>
								</Command.List>
							</Command.Root>
						</Popover.Content>
					</Popover.Root>
					<button
						class="relative !w-full cursor-pointer"
						onclick={(e) => {
							copyToClipboard(selectedParameter);
						}}
					>
						<Input
							class="!w-full min-h-[40px] pr-6 cursor-pointer"
							name="selectedParameter"
							readonly
							value={selectedParameter ? `{{${selectedParameter}}}` : ""}
						/>

						<i
							class="icon-[fa6-solid--copy] absolute right-2 top-0 h-full flex items-center"
							aria-label="Copy"
						></i>
					</button>
				</div>
				<div class="form-field">
					<Label for="subject">Subject</Label>
					<Textarea
						class="!w-full min-h-[40px]"
						name="subject"
						bind:value={teamNotiParams.subject}
						onblur={() => {
							setTimeout(() => {
								activity.setParameter("Subject", teamNotiParams.subject);
							}, 2000);
						}}
					/>
				</div>
				<div>
					<Label for="messageBody">Message Body</Label>
					<Textarea
						class="!w-full min-h-[62px]"
						name="messageBody"
						bind:value={teamNotiParams.messageBody}
						onblur={() => {
							setTimeout(() => {
								activity.setParameter("MessageBody", teamNotiParams.messageBody);
							}, 200);
						}}
					/>
				</div>
			</div></Accordion.Content
		>
	</Accordion.Item>
	<Accordion.Item value="2" class="border-dark-10 px-1.5">
		<Accordion.Header>
			<Accordion.Trigger
				class="flex w-full flex-1 select-none items-center justify-between my-5 text-[15px] font-medium transition-all [&[data-state=open]>span>svg]:rotate-90"
			>
				<span
					class="hover:bg-dark-10 inline-flex mr-2 items-center justify-center rounded-[7px] bg-transparent"
				>
					<ChevronRight />
				</span>
				<span class="w-full text-left"> Recipients </span>
			</Accordion.Trigger>
		</Accordion.Header>
		<Accordion.Content class="text-sm tracking-[-0.01em]">
			<div class="pb-[25px] flex flex-col gap-4">
				<div class="form-field">
					<Label for="recipientsEmail">Email Address</Label>
					<div class="flex">
						<Textarea
							class="flex-1 min-h-[40px]"
							name="recipientsEmail"
							bind:value={teamNotiParams.recipientsEmail}
							onblur={() => {
								saveEmailParam("recipientsEmail");
							}}
							oninput={() => {
								resetEmailError("recipientsEmail");
							}}
							placeholder="John Doe (<EMAIL>), ..."
						/>
						<EmailTooltip />
					</div>
					<div class="mt-2 text-red-600 text-xs">{@html errorMessages.recipientsEmail}</div>
				</div>
				<div class="form-field">
					<Label for="recipientsUsers">C3 Users</Label>
					<div class="flex">
						<Textarea
							readonly
							class="!w-full min-h-[62px]"
							name="recipientsUsers"
							value={getUserNames(teamNotiParams.recipientsUsers)}
						/>
						<button
							class="ml-2"
							onclick={() => {
								selectUsers("recipientsUsers");
							}}><ArrowOutIcon /></button
						>
					</div>
				</div>
				<div class="form-field">
					<Label for="recipientsRoles">Roles</Label>
					<div class="flex">
						<Textarea
							readonly
							class="!w-full min-h-[62px]"
							name="recipientsRoles"
							value={teamNotiParams.recipientsRoles}
						/>
						<button
							class="ml-2"
							onclick={() => {
								selectRoles("recipientsRoles");
							}}><ArrowOutIcon /></button
						>
					</div>
				</div>
			</div>
		</Accordion.Content>
	</Accordion.Item>
</Accordion.Root>

{#if open}
	<DataDialog
		bind:open
		{modalTitle}
		{rows}
		{columns}
		{keyToCompare}
		{selectedItems}
		on:close={(e) => onFilteringClosed(e?.detail)}
	/>
{/if}
