name: wfe-designer - C<PERSON> - CVE Issues

env:
    org_name: hcc3
    app_name: hcc3-wfe-designer
    mytag: cveissues

    APP_PORT: 8080
    LOG_LEVEL: info

on:
    workflow_dispatch:
    push:
      branches:
        - 'bugfix/cveissues'
      paths:
        - '**'
        - '!.github/workflows/**'

concurrency:
    group: ci-hcc3-wfe-designer-${{ github.ref }}
    cancel-in-progress: true

defaults:
    run:
        shell: bash
jobs:
    build-and-publish-image:
        name: 'Build & Publish Container Image'
        runs-on: ubuntu-latest

        steps:
        - name: 'Checkout code'
          uses: actions/checkout@v3
          with:
            ref: 'bugfix/cveissues'
          #with:
          #  path: ./web
        - name: Configure AWS credentials 
          uses: aws-actions/configure-aws-credentials@v3
          with:
            aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
            aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
            aws-region: ap-southeast-1

        - name: Login to Amazon ECR
          id: login-ecr
          uses: aws-actions/amazon-ecr-login@v1

        - name: Get timestamp
          id: get-timestamp
          run: echo "::set-output name=timestamp::$(date +'%s')"
        
        - name: '${{ env.app_name }} - Build and upload container image'
          id: build-image
          env: 
            ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
            # ECR_REPOSITORY: ${{ secrets.REPO_NAME }}
            # IMAGE_TAG: ${{ steps.get-timestamp.outputs.timestamp }}
            IMAGE_TAG: ${{ env.mytag }}
          run: |
              cd ./web
              export IMAGE_TAG=${IMAGE_TAG}
              docker build . --file Dockerfile --tag $ECR_REGISTRY/${{ env.app_name }}:$IMAGE_TAG \
              --build-arg PORT=8080 \
              --build-arg ORIGIN=http://wfd.************.nip.io  \
              --build-arg IAM_URL=http://iams-keycloak.************.nip.io/realms/AOH/.well-known/openid-configuration \
              --build-arg IAM_CLIENT_ID=wfm_client \
              --build-arg PUBLIC_DOMAIN=************.nip.io \
              --build-arg PUBLIC_COOKIE_PREFIX=wfd \
              --build-arg OIDC_ALLOW_INSECURE_REQUESTS=1 \
              --build-arg LOGIN_DESTINATION=/aoh/wfd \
              --build-arg WFM_URL=http://workflow-manager:8080 \
              --build-arg ACTIVITY_AAS_URL=http://iams-aas.************.nip.io \
              --build-arg DATA_AGG_URL=http://data-agg:5003
              docker push $ECR_REGISTRY/${{ env.app_name }}:$IMAGE_TAG
