@top Expression { expression* }

@skip { spaces }

commaSep<content> { content ("," content)* }

expression {
    Number |
    String |
    Operator |
    Identifier |

    @specialize[@name=BooleanLiteral]<Identifier, "true" | "false"> |
    @specialize[@name=Nil]<Identifier, "nil"> |
    @specialize[@name=Keyword]<Identifier, "matches" | "not" | "and" | "or" | "in"> |
    @specialize[@name=StringOperator]<Identifier, "contains" | "startsWith"| "endWith"> |

    MemberExpression {
      Identifier "." ( PropertyName | "[" expression "]")
    } |

    ArrayExpression {
        "[" commaSep<expression | ""> "]"
    } |

    ObjectPattern {
      "{" commaSep<Identifier ":" expression> "}"
    }
}

@tokens {
    spaces[@export] { $[\u0009 \u000b\u00a0]+ }

    identifierChar { @asciiLetter | $[_$\u{a1}-\u{10ffff}] }

    Identifier { identifierChar (identifierChar | @digit)* }

    PropertyName { Identifier }

    String {
        '"' (![\\\n"] | "\\" _)* '"'? |
        "'" (![\\\n'] | "\\" _)* "'"?
    }

    hex { @digit | $[a-fA-F] }

    Number {
        ((@digit+) ("." @digit+)?) | "0x" hex+
    }

    Operator {
        "+" | "-" | "*" | "/" | "%"  | "^"  | "**" | "?" | "??" | ".." | "|" | "&&" | "||" |
        (">" | "<" | "!" | "=") "="?
    }
}
