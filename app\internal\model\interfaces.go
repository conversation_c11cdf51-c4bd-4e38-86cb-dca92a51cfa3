//go:generate mockgen -source=interfaces.go -destination=mock/store_mock.go -package=mock

package model

import (
	"context"
	"encoding/json"

	"github.com/google/uuid"
	aohhttp "github.com/mssfoobar/aoh-golib/http"
	"github.com/mssfoobar/app/wfe/internal/model/sqlplugin"
)

// WorkflowTemplateStore interface for managing workflow templates in database
type (
	WorkflowTemplateStore interface {
		SaveWorkflowTemplate(ctx context.Context, request *WorkflowTemplateRequest) (*WorkflowTemplateResponse, error)
		PublishWorkflowTemplate(ctx context.Context, request *WorkflowTemplateRequest) (*WorkflowTemplateResponse, error)
		GetWorkflowTemplate(ctx context.Context, request *GetWorkflowTemplateRequest) (*WorkflowTemplateResponse, error)
		ListWorkflowTemplate(ctx context.Context, request *ListWorkflowTemplateRequest) (*ListWorkflowTemplateResponse, error)
		DeleteWorkflowTemplate(ctx context.Context, request *DeleteWorkflowTemplateRequest) error
	}

	// WorkflowTemplateResponse represents a notification template in the database
	WorkflowTemplateResponse = sqlplugin.WorkflowTemplateRow

	WorkflowTemplateRequest struct {
		Name         string
		WorkflowJson json.RawMessage
		DesignerJson json.RawMessage
		Requester    string
		TenantId     string
		OccLock      int
	}

	GetWorkflowTemplateRequest struct {
		Id       uuid.UUID
		TenantId string
	}

	ListWorkflowTemplateRequest struct {
		Page     aohhttp.PageRequest
		TenantId string
	}

	ListWorkflowTemplateResponse struct {
		WorkflowTemplates []WorkflowTemplateResponse
		TotalCount        int
	}

	DeleteWorkflowTemplateRequest struct {
		Id       uuid.UUID
		TenantId string
	}
)

// FormTemplateStore interface for managing form templates in database
type (
	FormTemplateStore interface {
		SaveFormTemplate(ctx context.Context, request *FormTemplateRequest) (*FormTemplateResponse, error)
		GetFormTemplate(ctx context.Context, request *GetFormTemplateRequest) (*FormTemplateResponse, error)
		ListFormTemplate(ctx context.Context, request *ListFormTemplateRequest) (*ListFormTemplateResponse, error)
		DeleteFormTemplate(ctx context.Context, request *DeleteFormTemplateRequest) error
	}

	// FormTemplateResponse represents a notification template in the database
	FormTemplateResponse = sqlplugin.FormTemplateRow

	FormTemplateRequest struct {
		Name          string
		FormJson      json.RawMessage
		ComponentKeys json.RawMessage
		Requester     string
		TenantId      string
		OccLock       int
	}

	GetFormTemplateRequest struct {
		Id       uuid.UUID
		TenantId string
	}

	ListFormTemplateRequest struct {
		Page     aohhttp.PageRequest
		TenantId string
	}

	ListFormTemplateResponse struct {
		FormTemplates []FormTemplateResponse
		TotalCount    int
	}

	DeleteFormTemplateRequest struct {
		Id       uuid.UUID
		TenantId string
	}
)

// ServiceActivityStore interface for managing service activities in database
type (
	ServiceActivityStore interface {
		CreateServiceActivity(ctx context.Context, request *CreateServiceActivityRequest) (*ServiceActivityResponse, error)
		UpdateServiceActivity(ctx context.Context, request *UpdateServiceActivityRequest) (*ServiceActivityResponse, error)
		GetServiceActivity(ctx context.Context, request *GetServiceActivityRequest) (*ServiceActivityResponse, error)
		ListServiceActivity(ctx context.Context, request *ListServiceActivityRequest) (*ListServiceActivityResponse, error)
		DeleteServiceActivity(ctx context.Context, request *DeleteServiceActivityRequest) error
	}

	// ServiceActivityResponse represents a service activity in the database
	ServiceActivityResponse = sqlplugin.ServiceActivityRow

	CreateServiceActivityRequest struct {
		ServiceName     string
		ActivityType    string
		ActivityIcon    string
		ActivityParam   json.RawMessage
		ActivityResult  json.RawMessage
		TimeoutInSecond float64
	}

	UpdateServiceActivityRequest struct {
		Id              uuid.UUID
		ServiceName     string
		ActivityType    string
		ActivityIcon    string
		ActivityParam   json.RawMessage
		ActivityResult  json.RawMessage
		TimeoutInSecond float64
	}

	GetServiceActivityRequest struct {
		Id uuid.UUID
	}

	ListServiceActivityRequest struct {
		Page aohhttp.PageRequest
	}

	ListServiceActivityResponse struct {
		ServiceActivities []ServiceActivityResponse
		TotalCount        int
	}

	DeleteServiceActivityRequest struct {
		Id uuid.UUID
	}
)

// ServiceEventStore interface for managing service events in database
type (
	ServiceEventStore interface {
		CreateServiceEvent(ctx context.Context, request *CreateServiceEventRequest) (*ServiceEventResponse, error)
		UpdateServiceEvent(ctx context.Context, request *UpdateServiceEventRequest) (*ServiceEventResponse, error)
		GetServiceEvent(ctx context.Context, request *GetServiceEventRequest) (*ServiceEventResponse, error)
		ListServiceEvent(ctx context.Context, request *ListServiceEventRequest) (*ListServiceEventResponse, error)
		DeleteServiceEvent(ctx context.Context, request *DeleteServiceEventRequest) error
	}

	// ServiceEventResponse represents a service activity in the database
	ServiceEventResponse = sqlplugin.ServiceEventRow

	CreateServiceEventRequest struct {
		ServiceName string
		EventType   string
		EventIcon   string
		EventParam  json.RawMessage
		EventResult json.RawMessage
	}

	UpdateServiceEventRequest struct {
		Id          uuid.UUID
		ServiceName string
		EventType   string
		EventIcon   string
		EventParam  json.RawMessage
		EventResult json.RawMessage
	}

	GetServiceEventRequest struct {
		Id uuid.UUID
	}

	ListServiceEventRequest struct {
		Page aohhttp.PageRequest
	}

	ListServiceEventResponse struct {
		ServiceEvents []ServiceEventResponse
		TotalCount    int
	}

	DeleteServiceEventRequest struct {
		Id uuid.UUID
	}
)
