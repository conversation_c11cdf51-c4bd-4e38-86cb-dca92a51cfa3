<script lang="ts">
	import { dia } from "rappid/rappid";
	import { Button, buttonVariants } from "$lib/aoh/wfd/components/ui/button/index.js";
	import { toast } from "svelte-sonner";

	let { graph }: { graph: dia.Graph } = $props();
	let inputImport: HTMLInputElement;

	const importFile = async (e: Event) => {
		const fileList = (e.target as HTMLInputElement).files;
		if (!fileList) {
			return;
		}

		const file = fileList.item(0);
		if (!file) {
			return;
		}
		const content = await file.text();
		const contentAsJson = JSON.parse(content);

		if (contentAsJson.designer_json) {
			graph.fromJSON(contentAsJson.designer_json);
		} else {
			toast.error("Failed to import workflow");
		}

		inputImport.value = "";
	};
</script>

<input
	bind:this={inputImport}
	id="bpmn-file"
	class="hidden"
	type="file"
	accept=".json"
	onchange={importFile}
	multiple
/>
<Button class={buttonVariants({ variant: "secondary" })} onclick={() => inputImport.click()}>Import</Button>
