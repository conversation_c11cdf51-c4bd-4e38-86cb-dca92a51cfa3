<script lang="ts">
	import { onMount, onDestroy } from "svelte";
	import type { Form as FormJs } from "@bpmn-io/form-js";
	import "@bpmn-io/form-js/dist/assets/form-js.css";

	let { formJson }: { formJson: unknown } = $props();
	let container: HTMLElement = $state()!;

	let form: FormJs;
	let Form: typeof FormJs;

	onMount(async () => {
		({ Form } = await import("@bpmn-io/form-js"));
		form = new Form({
			container: container,
		});
		if (formJson) {
			await form.importSchema(formJson);
		}
	});

	onDestroy(() => {
		if (form) {
			form.destroy();
		}
	});
</script>

<div bind:this={container} class="bg-white"></div>

<!-- Hide form-js bpmn icon display -->
<style>
	:global(.fjs-powered-by) {
		display: none !important;
	}
</style>
