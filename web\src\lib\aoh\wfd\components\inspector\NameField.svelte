<script lang="ts">
	import { dia, ui } from "rappid/rappid";
	import { Input } from "$lib/aoh/wfd/components/ui/input/index.js";
	import { Label } from "$lib/aoh/wfd/components/ui/label/index.js";
	import { util } from "$lib/aoh/wfd/bpmn/shapes";
	import { InputValidatorError } from "$lib/aoh/wfd/bpmn/utils/error";

	let { inspector, path }: { inspector: ui.Inspector; path: string } = $props();

	const cell = inspector.options.cell as dia.Cell;
	let value = $state(cell.prop(path));
	let errorMessage = $state("");

	const validateInput = (path: string, value: unknown, inspector: ui.Inspector): boolean => {
		const cell = inspector.options.cell;
		try {
			if (cell && util.hasInputValidate(cell)) {
				cell.validateProperty(path, value);
			}
		} catch (error) {
			if (error instanceof InputValidatorError) {
				errorMessage = error.message;
			}
			return false;
		}
		return true;
	};

	const updateValue = () => {
		const isValid = validateInput(path, value, inspector);
		if (isValid) {
			cell.prop(path, value);
			errorMessage = "";
		}
	};
</script>

<Label for="name">Name</Label>
<Input id="name" onblur={updateValue} bind:value spellcheck="false" />
<div class="text-destructive text-xs normal-case">{errorMessage}</div>
