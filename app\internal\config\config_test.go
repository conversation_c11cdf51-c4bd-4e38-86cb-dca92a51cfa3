package config

import (
	"os"
	"testing"
)

func TestInit(t *testing.T) {
	// Create a temporary file
	tmpFile, err := os.CreateTemp("", "temp")
	if err != nil {
		t.<PERSON>al(err)
	}
	defer os.Remove(tmpFile.Name())

	// Write some test data to the file
	_, err = tmpFile.Write([]byte("SQL_HOST=localhost\nSQL_PORT=5432"))
	if err != nil {
		t.Fatal(err)
	}
	err = tmpFile.Close()
	if err != nil {
		t.Fatal(err)
	}

	// Call the Init function with the temporary file
	err = Init(tmpFile.Name())
	if err != nil {
		t.<PERSON>rf("Init returned error: %v", err)
	}

	// Check if the AppConfig is populated correctly
	if AppConfig.SQL.Host != "localhost" {
		t.Errorf("SQL_HOST not set correctly, expected 'localhost', got '%s'", AppConfig.SQL.Host)
	}
	if AppConfig.SQL.Port != "5432" {
		t.<PERSON>("SQL_PORT not set correctly, expected '5432', got %s", AppConfig.SQL.Port)
	}
}
