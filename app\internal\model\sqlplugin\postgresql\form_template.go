package postgresql

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/mssfoobar/app/wfe/internal/model/sqlplugin"
)

const (
	createFormTemplateQuery = `INSERT INTO form_template (id, name, form_json, component_keys,
								created_by, updated_by, tenant_id) VALUES ($1, $2, $3, $4, $5, $6, $7)`

	updateFormTemplateQuery = `UPDATE form_template SET name = $1, form_json = $2, component_keys = $3,
								updated_by = $4, occ_lock = $5 WHERE id = $6 AND tenant_id = $7`

	getFormTemplateQuery       = `SELECT * FROM form_template WHERE id = $1 AND tenant_id = $2`
	getFormTemplateByNameQuery = `SELECT * FROM form_template WHERE name = $1 AND tenant_id = $2`

	deleteFormTemplateByIdQuery = `DELETE FROM form_template WHERE id = $1 AND tenant_id = $2`

	listFormTemplate        = `SELECT * FROM form_template WHERE tenant_id = $1 LIMIT $2 OFFSET $3`
	listFormTemplateOrderBy = `SELECT * FROM form_template WHERE tenant_id = $1 ORDER BY %s LIMIT $2 OFFSET $3`

	getTotalCountFormTemplate = `SELECT COUNT(*) FROM form_template WHERE tenant_id = $1`
)

func (pdb *db) InsertIntoFormTemplate(
	ctx context.Context,
	rows *sqlplugin.FormTemplateRow,
) (sql.Result, error) {
	return pdb.conn.ExecContext(
		ctx,
		createFormTemplateQuery,
		rows.Id,
		rows.Name,
		rows.FormJson,
		rows.ComponentKeys,
		rows.CreatedBy,
		rows.UpdatedBy,
		rows.TenantId,
	)
}

func (pdb *db) UpdateFormTemplate(
	ctx context.Context,
	rows *sqlplugin.FormTemplateRow,
) (sql.Result, error) {
	return pdb.conn.ExecContext(
		ctx,
		updateFormTemplateQuery,
		rows.Name,
		rows.FormJson,
		rows.ComponentKeys,
		rows.UpdatedBy,
		rows.OccLock,
		rows.Id,
		rows.TenantId,
	)
}

func (pdb *db) SelectFromFormTemplate(
	ctx context.Context,
	filter sqlplugin.FormTemplateFilter,
) (*sqlplugin.FormTemplateRow, error) {
	switch {
	case filter.Id != nil:
		return pdb.selectFromFormTemplate(ctx, filter)
	case filter.Name != nil:
		return pdb.selectFromFormTemplateByName(ctx, filter)
	default:
		return nil, fmt.Errorf("missing filter argument")
	}
}

func (pdb *db) selectFromFormTemplate(
	ctx context.Context,
	filter sqlplugin.FormTemplateFilter,
) (*sqlplugin.FormTemplateRow, error) {
	var row sqlplugin.FormTemplateRow
	err := pdb.conn.GetContext(ctx, &row, getFormTemplateQuery, filter.Id, filter.TenantId)
	if err != nil {
		return nil, err
	}
	return &row, nil
}

func (pdb *db) selectFromFormTemplateByName(
	ctx context.Context,
	filter sqlplugin.FormTemplateFilter,
) (*sqlplugin.FormTemplateRow, error) {
	var row sqlplugin.FormTemplateRow
	err := pdb.conn.GetContext(ctx, &row, getFormTemplateByNameQuery, filter.Name, filter.TenantId)
	if err != nil {
		return nil, err
	}
	return &row, nil
}

func (pdb *db) ListFromFormTemplate(
	ctx context.Context,
	filter sqlplugin.FormTemplatePaginateFilter,
) ([]sqlplugin.FormTemplateRow, error) {
	switch {
	case filter.OrderBy != "":
		return pdb.listFromFormTemplateOrderBy(ctx, filter)
	default:
		return pdb.listFromFormTemplate(ctx, filter)
	}
}

func (pdb *db) listFromFormTemplate(
	ctx context.Context,
	filter sqlplugin.FormTemplatePaginateFilter,
) ([]sqlplugin.FormTemplateRow, error) {
	var rows []sqlplugin.FormTemplateRow
	err := pdb.conn.SelectContext(ctx,
		&rows,
		listFormTemplate,
		filter.TenantId,
		filter.Limit,
		filter.Offset,
	)
	return rows, err
}

func (pdb *db) listFromFormTemplateOrderBy(
	ctx context.Context,
	filter sqlplugin.FormTemplatePaginateFilter,
) ([]sqlplugin.FormTemplateRow, error) {
	var rows []sqlplugin.FormTemplateRow
	err := pdb.conn.SelectContext(ctx,
		&rows,
		fmt.Sprintf(listFormTemplateOrderBy, filter.OrderBy),
		filter.TenantId,
		filter.Limit,
		filter.Offset,
	)
	return rows, err
}

func (pdb *db) DeleteFromFormTemplate(
	ctx context.Context,
	filter sqlplugin.FormTemplateFilter,
) (sql.Result, error) {
	return pdb.conn.ExecContext(ctx, deleteFormTemplateByIdQuery, filter.Id, filter.TenantId)
}

func (pdb *db) CountFromFormTemplate(
	ctx context.Context,
	filter sqlplugin.FormTemplateCountFilter,
) (int, error) {
	var total int
	err := pdb.conn.GetContext(ctx, &total, getTotalCountFormTemplate, filter.TenantId)
	return total, err
}
