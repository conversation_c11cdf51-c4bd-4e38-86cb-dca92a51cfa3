name: wfe-designer - Main CI FPT

env:
  org_name: hcc3
  app_name: hcc3-wfe-designer-fpt
  manifest_repo: hcc3/hcc3-wfe-designer-infra
  manifest_branch: develop
  github_ref_name: develop
  mytag: latest
  aws_instance_id: i-0bd319566541f7baf

  APP_PORT: 8080
  LOG_LEVEL: info

on:
  workflow_dispatch:

concurrency:
  group: ci-hcc3-wfe-designer-${{ github.ref }}
  cancel-in-progress: true

defaults:
  run:
    shell: bash

jobs:
  validate-branch:
    runs-on: ubuntu-latest
    outputs:
      run_ci: ${{ steps.check-branch.outputs.run_ci }}
    steps:
      - name: Check if branch name matches sprint pattern
        id: check-branch
        run: |
          echo "GITHUB_REF_NAME=${GITHUB_REF_NAME}"
          if [[ "$GITHUB_REF_NAME" =~ ^release/sprint-[0-9]+-fpt$ ]]; then
            echo "✅ Valid sprint branch"
            echo "run_ci=true" >> $GITHUB_OUTPUT
          else
            echo "❌ Invalid branch. Skipping workflow."
            echo "run_ci=false" >> $GITHUB_OUTPUT
          fi

  build-and-publish-image:
    needs: validate-branch
    if: needs.validate-branch.outputs.run_ci == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          path: ./web

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v3
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-southeast-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Ensure ECR repository exists
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          aws ecr describe-repositories --repository-names ${{ env.app_name }} || \
          aws ecr create-repository --repository-name ${{ env.app_name }}

      - name: Build and push Docker image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ env.mytag }}
        run: |
          cd ./web
          docker build . --file Dockerfile -t $ECR_REGISTRY/${{ env.app_name }}:$IMAGE_TAG \
            --build-arg PORT=8080 \
            --build-arg ORIGIN=http://wfd.*************.nip.io \
            --build-arg IAM_URL=http://iams-keycloak.*************.nip.io/realms/AOH/.well-known/openid-configuration \
            --build-arg IAM_CLIENT_ID=wfm_client \
            --build-arg PUBLIC_DOMAIN=*************.nip.io \
            --build-arg PUBLIC_COOKIE_PREFIX=wfd \
            --build-arg OIDC_ALLOW_INSECURE_REQUESTS=1 \
            --build-arg LOGIN_DESTINATION=/aoh/wfd \
            --build-arg WFM_URL=http://workflow-manager:8080 \
            --build-arg ACTIVITY_AAS_URL=http://iams-aas.*************.nip.io \
            --build-arg DATA_AGG_URL=http://data-agg:5003

          docker push $ECR_REGISTRY/${{ env.app_name }}:$IMAGE_TAG

      - name: Post success message to Slack
        if: success()
        run: |
          curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"✅ Build succeeded on *${{ github.repository }}* for branch \`${{ github.ref_name }}\`.\
            <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Build>\"}" \
            ${{ secrets.SLACK_WEBHOOK }}
      - name: Post failure message to Slack
        if: failure()
        run: |
          curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"❌ Build failed on *${{ github.repository }}* for branch \`${{ github.ref_name }}\`.\
            <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Build>\"}" \
            ${{ secrets.SLACK_WEBHOOK }}
