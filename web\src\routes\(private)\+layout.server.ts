/** @type {import('./$types').LayoutLoad} */
import { LOGIN_API } from "$lib/aoh/core/provider/auth/auth";
import { redirect } from "@sveltejs/kit";
import dayjs from "dayjs";
import Duration from "dayjs/plugin/duration.js";
// import { log as logger } from "$lib/aoh/core/logger/Logger.js";
import { StatusCodes } from "http-status-codes";
dayjs.extend(Duration);

// const log = logger.child({ src: new URL(import.meta.url).pathname });

export async function load({ locals }) {
	//INFO: Then based on its authenticated status, decide what to do. As this route is protected, we will
	//redirect the user to the login page if they are not authenticated.

	const authResult = locals.authResult;

	if (authResult.success) {
		// log.debug("User is authenticated");

		return {
			user: authResult.claims,
		};
	} else {
		// log.debug("User is not authenticated");
		redirect(StatusCodes.TEMPORARY_REDIRECT, LOGIN_API); //Redirect login page.
	}
}
