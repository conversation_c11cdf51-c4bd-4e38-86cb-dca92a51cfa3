import { env as envPrivate } from "$env/dynamic/private";
import { env } from "$env/dynamic/public";
import { type Configuration, discovery, type DiscoveryRequestOptions, allowInsecureRequests } from "openid-client";
import dayjs from "dayjs";
import Duration from "dayjs/plugin/duration";
import { authenticate, LOGIN_API, type AuthResult } from "$lib/aoh/core/provider/auth/auth";
import { building } from "$app/environment";
// import { log } from "$lib/aoh/core/logger/Logger";
import { error, type Handle } from "@sveltejs/kit";

dayjs.extend(Duration);

// log.info("Environment Mode (NODE_ENV): " + envPrivate.NODE_ENV);

/* -------------------------------------------------------------------------- */
/*                               AUTHENTICATION                               */
/* -------------------------------------------------------------------------- */

const discoveryRequestOptions: DiscoveryRequestOptions = {};
if (envPrivate.OIDC_ALLOW_INSECURE_REQUESTS === "1") {
	discoveryRequestOptions.execute = [allowInsecureRequests];
}

const oidc_config: Configuration = await discovery(
	new URL(envPrivate.IAM_URL),
	envPrivate.IAM_CLIENT_ID ?? "",
	undefined,
	undefined,
	discoveryRequestOptions
);

/* -------------------------------------------------------------------------- */
/*                           VALIDATE ENV VARIABLES                           */
/* -------------------------------------------------------------------------- */
const missingEnvVars: string[] = [];

//Public env variables
if (!env.PUBLIC_DOMAIN) {
	// log.warn(
	// 	"PUBLIC_DOMAIN is not set, cookie behaviour might not be as expected! This is desired only in local development using localhost."
	// );
}
if (!env.PUBLIC_COOKIE_PREFIX) missingEnvVars.push("PUBLIC_COOKIE_PREFIX");

//Private variables
if (!envPrivate.IAM_CLIENT_ID) missingEnvVars.push("IAM_CLIENT_ID");
if (!envPrivate.IAM_URL) missingEnvVars.push("IAM_URL");
if (!envPrivate.ORIGIN) missingEnvVars.push("ORIGIN");
if (!envPrivate.LOGIN_DESTINATION) missingEnvVars.push("LOGIN_DESTINATION");
if (!envPrivate.LOGIN_PAGE) {
	// log.warn(`LOGIN_PAGE is not set, defaulting to ${LOGIN_API}`);
}

if (missingEnvVars.length > 0 && !building) {
	// log.error({ missingEnvVars }, "Server cannot start with missing environment variables");
	process.exit(1);
}

/* -------------------------------------------------------------------------- */
/*                             TOKENS AND COOKIES                             */
/* -------------------------------------------------------------------------- */

export const handle: Handle = async ({ event, resolve }) => {
	try {
		const authResult: AuthResult = await authenticate(oidc_config, event.cookies, event.request, event.url);
		event.locals.clients = {
			oidc_config,
		};
		event.locals.authResult = authResult;
	} catch (err) {
		// log.error({ err }, "critical authentication failure");

		if (envPrivate.NODE_ENV === "development") {
			error(500, {
				message: (err as Error).message,
			});
		}
	}

	return resolve(event);
};
