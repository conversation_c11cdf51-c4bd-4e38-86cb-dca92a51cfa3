<script lang="ts">
	import { ui, dia } from "rappid/rappid";
	import * as Command from "$lib/aoh/wfd/components/ui/command/index.js";
	import * as Popover from "$lib/aoh/wfd/components/ui/popover/index.js";
	import { Label } from "$lib/aoh/wfd/components/ui/label";
	import { buttonVariants } from "$lib/aoh/wfd/components/ui/button";
	import { activityStore } from "$lib/aoh/wfd/stores/activities";
	import { eventStore } from "$lib/aoh/wfd/stores/events";
	import { Event as EventShape } from "$lib/aoh/wfd/bpmn/shapes";

	let { inspector }: { inspector: ui.Inspector } = $props();

	let open = $state(false);

	const cell = inspector.options.cell as dia.Cell;
	const isEventElement = cell.constructor === EventShape;

	const selectMessage = isEventElement ? "Select an event type..." : "Select an activity type...";
	const searchMessage = isEventElement ? "Search event types..." : "Search activity types...";
	const emptyMessage = isEventElement ? "No event types found." : "No activity types found.";

	let value = $state(cell.attr("type/text"));

	const typeList = isEventElement
		? [...$eventStore.map((f) => f.event_type)]
		: [...$activityStore.map((f) => f.activity_type)];
	const selectedValue = $derived(
		isEventElement
			? $eventStore.find((f) => f.event_type === value)?.event_type
			: $activityStore.find((f) => f.activity_type === value)?.activity_type
	);

	const applyChangeToCell = () => {
		const activity = $activityStore.find((f) => f.activity_type === value);
		const event = $eventStore.find((f) => f.event_type === value);
		cell.attr("type/text", isEventElement ? event?.event_type : activity?.activity_type);
		cell.attr("data/taskResult", isEventElement ? event?.event_result : activity?.activity_result);
		cell.attr(
			"data/resultType",
			isEventElement
				? event?.event_result === null
					? "null"
					: typeof event?.event_result
				: activity?.activity_result === null
					? "null"
					: typeof activity?.activity_result
		);
		cell.attr("icon/iconType", isEventElement ? event?.event_icon : activity?.activity_icon);
	};

	const closeAndUpdateValue = () => {
		open = false;
		// shad-cn popover closing animation is slower than jointJS inspector refresh
		// so we need to delay the applyChangeToCell function
		setTimeout(() => {
			applyChangeToCell();
		}, 200);
	};
</script>

<Label for="type">Type</Label>
<Popover.Root bind:open>
	<Popover.Trigger role="combobox" class={buttonVariants({ variant: "outline" }) + " w-full justify-between"}>
		{selectedValue || selectMessage}
		<span class="icon-[fa6-solid--caret-down]" aria-hidden="true"></span>
	</Popover.Trigger>
	<Popover.Content class="p-0 w-[310px]">
		<Command.Root>
			<Command.Input placeholder={searchMessage} />
			<Command.List>
				<Command.Empty>{emptyMessage}</Command.Empty>
				<Command.Group>
					{#each typeList as type}
						<Command.Item
							value={type}
							class="aria-selected:bg-primary aria-selected:text-primary-foreground"
							onSelect={() => {
								value = type;
								closeAndUpdateValue();
							}}
						>
							{type}
						</Command.Item>
					{/each}
				</Command.Group>
			</Command.List>
		</Command.Root>
	</Popover.Content>
</Popover.Root>
