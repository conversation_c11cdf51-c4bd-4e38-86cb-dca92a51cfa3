<script lang="ts">
	import { type dia, ui } from "rappid/rappid";

	type InspectorEventCallback = (inspector: ui.Inspector) => (value: string, el: HTMLElement) => void;

	interface Props {
		opts: (cell: dia.Cell) => ui.Inspector.Options;
		events: { name: string; callback: InspectorEventCallback }[];
		inspectorControl: { open: (cell: dia.Cell) => void; close: () => void };
	}

	let { opts, events = [], inspectorControl = $bindable() }: Props = $props();

	let container: HTMLElement = $state()!;
	let cell: dia.Cell = $state()!;
	let show: boolean = $state(false);

	$effect(() => {
		if (container) {
			const inspector = ui.Inspector.create(container, opts(cell));

			events.forEach((evt) => {
				inspector.on(evt.name, evt.callback(inspector));
			});
		}
	});

	const open = (inputCell: dia.Cell) => {
		cell = inputCell;
		show = true;
	};

	const close = () => {
		ui.Inspector.close();
		show = false;
	};

	inspectorControl = { open, close };
</script>

{#if show}
	<div bind:this={container}></div>
{/if}
