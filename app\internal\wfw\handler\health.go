package handler

import (
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	aohhttp "github.com/mssfoobar/aoh-golib/http"
)

func NewHealthRouter() http.Handler {
	r := chi.NewRouter()
	r.Use(middleware.Recoverer)

	health := aohhttp.NewHealthCheck(r)
	health.AddReadinessCheck("readiness check", func() error {
		return nil
	})
	health.AddLivenessCheck("liveness check", func() error {
		return nil
	})

	return r
}
