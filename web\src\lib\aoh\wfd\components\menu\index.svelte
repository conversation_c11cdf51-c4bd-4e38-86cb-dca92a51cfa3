<script lang="ts">
	import { dia } from "rappid/rappid";
	import Load from "$lib/aoh/wfd/components/menu/load.svelte";
	import New from "$lib/aoh/wfd/components/menu/new.svelte";
	import Save from "$lib/aoh/wfd/components/menu/save.svelte";
	import Import from "$lib/aoh/wfd/components/menu/import.svelte";
	import Export from "$lib/aoh/wfd/components/menu/export.svelte";

	let { graph }: { graph: dia.Graph } = $props();
</script>

<div class="ml-auto flex w-full space-x-2 sm:justify-end mr-2">
	<Load {graph} />
	<New {graph} />
	<Save {graph} publish={false} />
	<Save {graph} publish={true} />
	<Import {graph} />
	<Export {graph} />
</div>
