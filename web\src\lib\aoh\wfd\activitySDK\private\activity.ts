import { ui, dia } from "rappid/rappid";
import { type Activity } from "$lib/aoh/wfd/activitySDK/api";

/**
 * concrete implementation of Activity
 *
 * @internal
 */
class InternalActivity implements Activity {
	readonly _inspector: ui.Inspector;
	readonly _path: string;

	constructor(inspector: ui.Inspector, path: string) {
		this._inspector = inspector;
		this._path = path;
	}

	public setParameter(name: string, value: unknown) {
		const cell = this._inspector.options.cell as dia.Cell;
		cell.attr("data/typeOptions/" + name, value);
	}

	public getParameter(name: string): unknown {
		const cell = this._inspector.options.cell as dia.Cell;
		return cell.attr("data/typeOptions/" + name);
	}

	public clearParameter(name: string) {
		const cell = this._inspector.options.cell as dia.Cell;
		cell.removeAttr("data/typeOptions/" + name);
		cell.removeAttr("data/typeOptions/" + name + "Tmp");
		cell.removeAttr("data/typeOptions/" + name + "Validate");
	}

	public setUiState(name: string, value: unknown) {
		const cell = this._inspector.options.cell as dia.Cell;
		cell.attr("data/UiState/" + name, value);
	}

	public getUiState(name: string): unknown {
		const cell = this._inspector.options.cell as dia.Cell;
		return cell.attr("data/UiState/" + name);
	}
}

export { InternalActivity };
