import { json, error } from "@sveltejs/kit";
import type { RequestHandler } from "@sveltejs/kit";
import { StatusCodes } from "http-status-codes";
import { env } from "$env/dynamic/private";

export const GET: RequestHandler = async ({ locals }) => {
	if (!locals.authResult.success) {
		return new Response(JSON.stringify({ message: "Unauthorized" }), { status: StatusCodes.UNAUTHORIZED });
	}

	const tenantId = locals?.authResult?.claims?.active_tenant?.tenant_id || "";
	const bearer = `Bearer ${locals.authResult.access_token}`;

	const headers = { "Content-Type": "application/json", Authorization: bearer };
	const options = { method: "GET", headers };

	const userPromise = fetch(env.ACTIVITY_AAS_URL + `/admin/tenants/${tenantId}/memberships`, options);
	const rolePromise = fetch(env.ACTIVITY_AAS_URL + `/admin/tenants/${tenantId}/roles`, options);

	const [userResp, roleResp] = await Promise.all([userPromise, rolePromise]);

	if (!userResp.ok || !roleResp.ok) {
		return error(StatusCodes.INTERNAL_SERVER_ERROR, "Failed to fetch IAMS data");
	}

	const data = { users: await userResp.json(), roles: await roleResp.json() };

	return json(data);
};
