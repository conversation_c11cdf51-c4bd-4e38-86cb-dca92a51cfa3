<script lang="ts">
	import { onMount } from "svelte";
	import { type Activity } from "$lib/aoh/wfd/activitySDK/api";
	import { Accordion } from "bits-ui";
	import ChevronRight from "lucide-svelte/icons/chevron-right";

	import { Label } from "$lib/aoh/wfd/components/ui/label";
	import { Switch } from "$lib/aoh/wfd/components/ui/switch";

	import SelectSearch from "$lib/aoh/wfd/components/ui/select/select-search.svelte";

	type ChannelType = {
		threadTeamId?: string;
		threadChannelId?: string;
		teamName?: string;
		channelName?: string;
	};

	let { activity }: { activity: Activity } = $props();

	const teamAndChannelKey = "Team and Channel";
	const allowMultiReplyKey = "Allow Multi Reply";
	const splitter = "///";

	let channelData = $state<ChannelType[]>([]);
	let listData: string[] = $state([]);

	let on: boolean = $state<boolean>((activity.getParameter(allowMultiReplyKey) as boolean) || false);
	let selectedItem: string = $state<string>("");

	const onSelectHandle = (value: string) => {
		selectedItem = value;
		const row = channelData.find((d) => `${d.teamName} (${d.channelName})` === value);
		let returnText: string = "";
		if (row) returnText = `${row.threadTeamId}${splitter}${row.threadChannelId}${splitter}${row.teamName}${splitter}${row.channelName}`;

		activity.setParameter(teamAndChannelKey, returnText);
	};

	onMount(async () => {
		try {
			const response = await fetch("wfd/api/TeamsChannelList", { method: "GET" });
			channelData = await response.json();
			if (!channelData?.length) {
				return;
			}

			listData = channelData.map((i: ChannelType) => `${i.teamName ?? ""} (${i.channelName ?? ""})`);

			let teamAndChannel = (activity.getParameter(teamAndChannelKey) as string) || "";
			if (teamAndChannel) {
				const [teamId, channelId] = teamAndChannel.split(splitter);
				const row = channelData.find((d) => d.threadTeamId === teamId && d.threadChannelId === channelId);
				if (row) selectedItem = `${row.teamName} (${row.channelName})`;
			}
		} catch (error) {
			console.error("Error fetching data:", error);
		}
	});
</script>

<Accordion.Root value={["1"]} class="w-[calc(100%+20px)] border border-solid mt-2 -mx-[10px] px-4" type="multiple">
	<Accordion.Item value="1" class="border-dark-10 px-1.5">
		<Accordion.Header>
			<Accordion.Trigger
				class="flex w-full flex-1 select-none items-center justify-between my-5 text-[15px] font-medium transition-all [&[data-state=open]>span>svg]:rotate-90"
			>
				<span
					class="hover:bg-dark-10 inline-flex mr-2 items-center justify-center rounded-[7px] bg-transparent"
				>
					<ChevronRight />
				</span>
				<span class="w-full text-left"> Recipients </span>
			</Accordion.Trigger>
		</Accordion.Header>
		<Accordion.Content class="text-sm tracking-[-0.01em]">
			<div class="pb-[25px] flex flex-col gap-4">
				<div class="form-field">
					<Label for="TeamAndChannel">Team & Channel</Label>
					<div class="flex">
						<SelectSearch {listData} {selectedItem} onSelect={onSelectHandle} />
					</div>
				</div>
				<div class="form-field">
					<div class="flex justify-between">
						<Label for="AllowMultiReply">Allow Multi Reply</Label>
						<Switch
							bind:checked={on}
							onCheckedChange={(value) => {
								activity.setParameter(allowMultiReplyKey, value);
							}}
						></Switch>
					</div>
				</div>
			</div>
		</Accordion.Content>
	</Accordion.Item>
</Accordion.Root>
