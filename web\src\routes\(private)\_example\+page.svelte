<svelte:options runes={true} />

<script lang="ts">
	import Button from "$lib/aoh/core/components/ui/button/button.svelte";
	import { annotate } from "rough-notation";

	let isVisibleDidSomething = $state(false);
	let circle: HTMLHeadingElement;
	let underline: HTMLHeadingElement;
	const iconClass: string = "border-2 border-ring w-10 h-10 flex items-center justify-center rounded-full";
</script>

<div class="w-full h-full flex flex-col justify-center bg-background text-foreground items-center py-16">
	<div class="w-1/4 flex h-3/4 items-center flex-col gap-4 text-sm">
		<h1
			bind:this={underline}
			class:invisible={!isVisibleDidSomething}
			class="mt-4 text-8xl text-pretty tracking-wider"
		>
			HOME
		</h1>

		<div class="flex flex-col gap-4 mt-auto">
			<h2 bind:this={circle} class="text-base my-auto text-center font-semibold">
				This is just a sample home page.
			</h2>
			<p>
				Lorem ipsum dolor sit amet consectetur adipisicing elit. Sit, vero fugiat aperiam asperiores possimus
				perferendis distinctio illum totam quaerat voluptatem. Pariatur neque consequuntur adipisci repellendus
				sit laboriosam aliquam, consequatur natus!
			</p>
			<Button
				onclick={() => {
					isVisibleDidSomething = true;

					annotate(circle, {
						type: "circle",
						strokeWidth: 2,
						iterations: 4,
						color: "#ff8800",
					}).show();

					annotate(underline, {
						type: "underline",
						strokeWidth: 3,
						iterations: 2,
						color: "#ff8800",
					}).show();
				}}
				>Do something!
			</Button>
		</div>

		<nav class="p-4 flex gap-4 w-full justify-center mt-auto">
			<Button
				class="flex items-center bg-transparent flex-col text-primary gap-2 h-fit hover:bg-transparent hover:brightness-150"
			>
				<div class={iconClass}>
					<span class="iconify mdi--house text-xl m-auto"></span>
				</div>
				<p class="text-xs">Example Icon A</p>
			</Button>
			<Button
				class="flex items-center bg-transparent flex-col text-primary gap-2 h-fit hover:bg-transparent hover:brightness-150"
			>
				<div class={iconClass}><span class="iconify mdi--folder text-xl m-auto"></span></div>
				<p class="text-xs">Example Icon B</p>
			</Button>
			<Button
				class="flex items-center bg-transparent flex-col text-primary gap-2 h-fit hover:bg-transparent hover:brightness-150"
			>
				<div class={iconClass}><span class="iconify mdi--user text-xl m-auto"></span></div>
				<p class="text-xs">Example Icon C</p>
			</Button>
			<Button
				class="flex items-center bg-transparent flex-col text-primary gap-2 h-fit hover:bg-transparent hover:brightness-150"
			>
				<div class={iconClass}><span class="iconify mdi--logout text-xl m-auto"></span></div>
				<p class="text-xs">Example Icon D</p>
			</Button>
		</nav>
	</div>
</div>
