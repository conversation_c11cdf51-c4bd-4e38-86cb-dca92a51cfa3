<script lang="ts">
	import type { ui } from "rappid/rappid";
	import * as Select from "$lib/aoh/wfd/components/ui/select";
	import { Label } from "$lib/aoh/wfd/components/ui/label";
	import { Flow, Switch, util } from "$lib/aoh/wfd/bpmn/shapes";
	import { InputValidatorError } from "$lib/aoh/wfd/bpmn/utils/error";

	let { inspector, path }: { inspector: ui.Inspector; path: string } = $props();

	const cell = inspector.options.cell as Flow;
	const isFromSwitch = cell.isLink() && cell.getSourceElement() instanceof Switch;
	const options = isFromSwitch ? ["default", "conditional"] : ["sequence"];

	let selected = $state(cell.prop(path) ?? options[0]);
	let errorMessage = $state("");

	const applyChangeToCell = () => {
		cell.prop(path, selected);
	};

	const validateInput = (path: string, value: unknown, inspector: ui.Inspector): boolean => {
		const cell = inspector.options.cell;
		try {
			if (cell && util.hasInputValidate(cell)) {
				cell.validateProperty(path, value);
			}
		} catch (error) {
			if (error instanceof InputValidatorError) {
				errorMessage = error.message;
			}
			return false;
		}
		return true;
	};

	const updateCellValue = () => {
		const isValid = validateInput(path, selected, inspector);
		if (isValid) {
			// shad-cn popover closing animation is slower than jointJS inspector refresh
			// so we need to delay the applyChangeToCell function
			setTimeout(() => {
				applyChangeToCell();
			}, 200);
		}
	};
	const triggerContent = $derived(options.find((f) => f === selected) ?? options[0]);
</script>

<Label>Type</Label>
<Select.Root type="single" allowDeselect={false} bind:value={selected} onValueChange={updateCellValue}>
	<Select.Trigger class="w-full">
		{triggerContent}
	</Select.Trigger>
	<Select.Content>
		<Select.Group>
			{#each options as opt}
				<Select.Item value={opt} label={opt}>{opt}</Select.Item>
			{/each}
		</Select.Group>
	</Select.Content>
</Select.Root>
<div class="text-destructive text-xs normal-case">{errorMessage}</div>
