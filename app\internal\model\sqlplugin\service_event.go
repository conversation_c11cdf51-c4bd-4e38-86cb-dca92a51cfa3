package sqlplugin

import (
	"context"
	"database/sql"
	"encoding/json"

	"github.com/google/uuid"
)

type (
	ServiceEventRow struct {
		Id          uuid.UUID       `db:"id" json:"id"`
		ServiceName string          `db:"service_name" json:"service_name"`
		EventType   string          `db:"event_type" json:"event_type"`
		EventIcon   string          `db:"event_icon" json:"event_icon"`
		EventParam  json.RawMessage `db:"event_param" json:"event_param"`
		EventResult json.RawMessage `db:"event_result" json:"event_result"`
	}

	ServiceEventFilter struct {
		Id uuid.UUID
	}

	ServiceEventPaginateFilter struct {
		Limit   int
		Offset  int
		OrderBy string
	}

	ServiceEvent interface {
		InsertIntoServiceEvent(ctx context.Context, rows *ServiceEventRow) (sql.Result, error)
		UpdateServiceEvent(ctx context.Context, rows *ServiceEventRow) (sql.Result, error)
		SelectFromServiceEvent(ctx context.Context, filter ServiceEventFilter) (*ServiceEventRow, error)
		ListFromServiceEvent(ctx context.Context, filter ServiceEventPaginateFilter) ([]ServiceEventRow, error)
		DeleteFromServiceEvent(ctx context.Context, filter ServiceEventFilter) (sql.Result, error)
		CountFromServiceEvent(ctx context.Context) (int, error)
	}
)
