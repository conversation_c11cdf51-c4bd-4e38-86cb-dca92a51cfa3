package handler

import (
	"bytes"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/mssfoobar/app/wfe/internal/wfm/handler/util"
	"github.com/stretchr/testify/require"
)

func TestRegisterValidate(t *testing.T) {
	handler := RegisterValidate("http://localhost:8080")

	req, err := http.NewRequest(http.MethodPost, "/expression", nil)
	if err != nil {
		t.Fatal(err)
	}

	w := httptest.NewRecorder()
	handler.ServeHTTP(w, req)

	require.Equal(t, http.StatusUnauthorized, w.Code)
}

func TestValidate_Expression(t *testing.T) {
	wf := Validate{}
	t.Run("valid expression, expect 200", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(http.MethodPost, "/expression", bytes.NewReader(util.StructToBytes(validateReqBody{
			Expression: "1 * 2 + 3",
		})))
		r.Header.Add("content-type", "application/json")
		wf.Expression(w, r)
		require.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("invalid expression, expect 400", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(http.MethodPost, "/expression", bytes.NewReader(util.StructToBytes(validateReqBody{
			Expression: "1 * 'abc'",
		})))
		r.Header.Add("content-type", "application/json")
		wf.Expression(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid request body, expect 400", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(http.MethodPost, "/expression", bytes.NewReader(util.StructToBytes(validateReqBody{})))
		r.Header.Add("content-type", "application/json")
		wf.Expression(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestValidate_Condition(t *testing.T) {
	wf := Validate{}
	t.Run("valid condition, expect 200", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(http.MethodPost, "/condition", bytes.NewReader(util.StructToBytes(validateReqBody{
			Expression: "a == b",
			Variables:  map[string]interface{}{"a": 1, "b": 2},
		})))
		r.Header.Add("content-type", "application/json")
		wf.Condition(w, r)
		require.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("invalid condition, expect 400", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(http.MethodPost, "/condition", bytes.NewReader(util.StructToBytes(validateReqBody{
			Expression: "a == b",
			Variables:  map[string]interface{}{"a": "c", "b": 2},
		})))
		r.Header.Add("content-type", "application/json")
		wf.Condition(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid request body, expect 400", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(http.MethodPost, "/condition", bytes.NewReader(util.StructToBytes(validateReqBody{})))
		r.Header.Add("content-type", "application/json")
		wf.Condition(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})
}
