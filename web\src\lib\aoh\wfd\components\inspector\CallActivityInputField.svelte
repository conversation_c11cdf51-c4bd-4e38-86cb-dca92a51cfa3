<script lang="ts">
	import { ui, dia } from "rappid/rappid";
	import * as Command from "$lib/aoh/wfd/components/ui/command/index.js";
	import * as Popover from "$lib/aoh/wfd/components/ui/popover/index.js";
	import * as Dialog from "$lib/aoh/wfd/components/ui/dialog/index.js";
	import { Label } from "$lib/aoh/wfd/components/ui/label";
	import { buttonVariants } from "$lib/aoh/wfd/components/ui/button";
	import { workflowStore } from "$lib/aoh/wfd/stores/workflows";
	import WorkflowPreview from "$lib/aoh/wfd/components/inspector/WorkflowPreview.svelte";

	let { inspector }: { inspector: ui.Inspector } = $props();
	const cell = inspector.options.cell as dia.Cell;

	let open = $state(false);
	let value = $state(cell.attr("data/workflowTemplate"));

	const selectedValue = $derived($workflowStore.find((f) => f.id === value)?.name);
	const workflowJson = $derived($workflowStore.find((f) => f.id === value)?.workflow_json);
	const designerJson = $derived($workflowStore.find((f) => f.id === value)?.designer_json);

	const applyChangeToCell = () => {
		cell.attr("data/workflowTemplate", value);
		cell.removeAttr("data/typeOptions");
		cell.attr("data/typeOptions/WorkflowJSON", workflowJson);
	};

	const closeAndUpdateValue = () => {
		open = false;
		// shad-cn popover closing animation is slower than jointJS inspector refresh
		// so we need to delay the applyChangeToCell function
		setTimeout(() => {
			applyChangeToCell();
		}, 200);
	};
</script>

<div class="flex items-center justify-between py-2">
	<Label for="type">Workflow Template</Label>
	<Dialog.Root>
		<Dialog.Trigger class={buttonVariants({ variant: "secondary" })}>Preview</Dialog.Trigger>
		<Dialog.Content style="max-width: 900px; max-height: 600px;">
			<Dialog.Header>
				<Dialog.Title>Workflow Preview</Dialog.Title>
			</Dialog.Header>
			<WorkflowPreview {designerJson} />
		</Dialog.Content>
	</Dialog.Root>
</div>
<Popover.Root bind:open>
	<Popover.Trigger role="combobox" class={buttonVariants({ variant: "outline" }) + " w-full justify-between"}>
		{selectedValue || "Select a workflow..."}
		<span class="icon-[fa6-solid--caret-down]" aria-hidden="true"></span>
	</Popover.Trigger>
	<Popover.Content class="p-0 w-[310px]">
		<Command.Root>
			<Command.Input placeholder="Search workflows..." />
			<Command.List>
				<Command.Empty>No workflows found.</Command.Empty>
				<Command.Group>
					{#each $workflowStore as workflow}
						<Command.Item
							value={workflow.id}
							class="aria-selected:bg-primary aria-selected:text-primary-foreground"
							onSelect={() => {
								value = workflow.id;
								closeAndUpdateValue();
							}}
						>
							{workflow.name}
						</Command.Item>
					{/each}
				</Command.Group>
			</Command.List>
		</Command.Root>
	</Popover.Content>
</Popover.Root>
