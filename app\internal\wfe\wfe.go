package wfe

import (
	"context"
	"errors"
	"fmt"
	"net"
	"net/http"
	"sync"

	aohlog "github.com/mssfoobar/aoh-golib/logger"
	"github.com/mssfoobar/aoh-golib/temporal"
	"github.com/mssfoobar/app/wfe/internal/config"
	"github.com/mssfoobar/app/wfe/internal/wfe/dsl"
	"github.com/mssfoobar/app/wfe/internal/wfe/handler"
	"go.temporal.io/sdk/worker"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
)

const (
	DslWorkflowType          = "Workflow"
	FormWorkflowType         = "Form"
	RecoverWorkflowType      = "Recover"
	CallActivityWorkflowType = "CallActivity"
)

type WorkflowEngine struct {
	http *http.Server
	wg   sync.WaitGroup
}

func New() *WorkflowEngine {
	return &WorkflowEngine{}
}

// runWorker initialize temporal worker instance
func (wfe *WorkflowEngine) runWorker(c config.Option, interruptCh <-chan interface{}) error {
	client, err := temporal.NewClient(temporal.Config{
		HostPort:  c.Temporal.Host + ":" + c.Temporal.Port,
		Namespace: c.Temporal.Namespace,
	})
	if err != nil {
		return fmt.Errorf("unable to initialize temporal client: %w", err)
	}
	defer client.Close()

	w := worker.New(client, c.TaskQueue, worker.Options{
		LocalActivityWorkerOnly: true,
	})
	dsl.SetConfig(dsl.Config{
		Client: client,
	})

	w.RegisterWorkflowWithOptions(
		dsl.ExecuteWorkflow,
		workflow.RegisterOptions{Name: DslWorkflowType},
	)
	w.RegisterWorkflowWithOptions(dsl.FormWorkflow, workflow.RegisterOptions{Name: FormWorkflowType})
	w.RegisterWorkflowWithOptions(dsl.RecoverWorkflow, workflow.RegisterOptions{Name: RecoverWorkflowType})
	w.RegisterWorkflowWithOptions(dsl.ChildExecuteWorkflow, workflow.RegisterOptions{Name: CallActivityWorkflowType})

	return w.Run(interruptCh)
}

func (wfe *WorkflowEngine) Start(c config.Option) {
	interruptCh := make(chan interface{})
	wfe.http = &http.Server{
		Addr:    net.JoinHostPort("0.0.0.0", c.Port),
		Handler: handler.NewHealthRouter(),
	}

	aohlog.Info("[INIT] http server starting", zap.String("port", c.Port))
	wfe.wg.Add(1)
	go func() {
		defer wfe.wg.Done()
		if err := wfe.http.ListenAndServe(); !errors.Is(err, http.ErrServerClosed) {
			aohlog.Fatal("unable to start http server", zap.Error(err))
		}
		aohlog.Info("[SHUTDOWN] http server shutdown")
		interruptCh <- struct{}{}
	}()

	wfe.wg.Add(1)
	go func() {
		defer wfe.wg.Done()
		if err := wfe.runWorker(c, interruptCh); err != nil {
			aohlog.Fatal("unable to start temporal worker", zap.Error(err))
		}
		aohlog.Info("[SHUTDOWN] temporal worker shutdown")
	}()
}

// Stop gracefully exit the app
func (wfe *WorkflowEngine) Stop() {
	aohlog.Info("[SHUTDOWN] workflow engine shutting down")
	err := wfe.http.Shutdown(context.Background())
	if err != nil {
		aohlog.Error("[SHUTDOWN] http server shutdown error", zap.Error(err))
	}
	wfe.wg.Wait()
	aohlog.Info("[SHUTDOWN] workflow engine shutdown successfully")
}
