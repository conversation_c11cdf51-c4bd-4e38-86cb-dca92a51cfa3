import { sveltekit } from "@sveltejs/kit/vite";
import { defineConfig, configDefaults } from "vitest/config";

export default defineConfig({
	plugins: [sveltekit()],
	test: {
		include: ["src/**/*.{test,spec}.{js,ts}"],
		exclude: [...configDefaults.exclude, "src/lib/aoh/core/components/ui/*.ts"],
	},
	build: {
		outDir: "build",
		chunkSizeWarningLimit: 200000, // Increase chunk size warning limit
	},
	server: {
		port: process.env.PORT ? parseInt(process.env.PORT, 10) : 3000, // Example of using .env value
	},
});
