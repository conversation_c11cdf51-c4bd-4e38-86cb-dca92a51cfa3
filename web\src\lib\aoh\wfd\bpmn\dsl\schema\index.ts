// Code generated by jtd-codegen for TypeScript v0.2.1

export interface WorkflowSchemaJson {
	specVersion: string;
	start: string;
	states: Statement[];
	variables: { [key: string]: unknown };
}

export interface Workflow {
	name: string;
	workflow_json: WorkflowSchemaJson;
	designer_json?: unknown;
	id?: string;
}

export interface ActivityInvocationOptions {
	startToCloseTimeout?: number;
}

export interface ActivityInvocation {
	arguments: string[];
	boundaryEvents: string[];
	id: string;
	result: string;
	type: string;
	options?: ActivityInvocationOptions;
}

export interface CallActivity {
	arguments: string[];
	boundaryEvents: string[];
	id: string;
	result: string;
	type: string;
}

export interface ConditionalAdvance {
	expression: string;
}

export enum ConditionalBasicOperator {
	Eq = "EQ",
	Lt = "LT",
	Lte = "LTE",
	Mt = "MT",
	Mte = "MTE",
	Neq = "NEQ",
}

export interface ConditionalBasic {
	input: string;
	operator: ConditionalBasicOperator;
	value: unknown;
}

export interface Conditional {
	advance?: ConditionalAdvance;
	basic?: ConditionalBasic;
}

export interface End {
	id: string;
	terminate: boolean;
}

export interface Event {
	arguments: string[];
	id: string;
	interrupting: boolean;
	result: string;
	type: string;
}

export interface Form {
	arguments: string[];
	boundaryEvents: string[];
	id: string;
	result: string;
	type: string;
}

export interface ParallelBranch {
	next?: string;
}

export interface Parallel {
	branches: ParallelBranch[];
	id: string;
}

export interface Statement {
	name: string;
	activity?: ActivityInvocation | null;
	callActivity?: CallActivity | null;
	end?: End | null;
	event?: Event | null;
	form?: Form | null;
	next?: string;
	parallel?: Parallel | null;
	switch?: Switch | null;
}

export interface SwitchCase {
	name: string;
	conditional?: Conditional;
	next?: string;
}

export interface SwitchDefault {
	next?: string;
}

export interface Switch {
	cases: SwitchCase[];
	id: string;
	default?: SwitchDefault;
}
