package wfm

import (
	"context"
	"errors"
	"net"
	"net/http"

	aohlog "github.com/mssfoobar/aoh-golib/logger"
	aohtmpl "github.com/mssfoobar/aoh-golib/temporal"
	"github.com/mssfoobar/app/wfe/internal/config"
	"github.com/mssfoobar/app/wfe/internal/model"
	"github.com/mssfoobar/app/wfe/internal/wfm/handler"
	"github.com/mssfoobar/app/wfe/internal/wfm/temporal"
	"go.uber.org/zap"
)

type WorkflowMgr struct {
	http *http.Server
}

func New() *WorkflowMgr {
	return &WorkflowMgr{}
}

func (wfm *WorkflowMgr) Start(c config.Option) {
	factory, err := model.NewFactory(c.SQL)
	if err != nil {
		aohlog.Fatal("unable to initialize sql connection", zap.Error(err))
	}

	client, err := aohtmpl.NewClient(aohtmpl.Config{
		HostPort:  c.Temporal.Host + ":" + c.Temporal.Port,
		Namespace: c.Temporal.Namespace,
	})
	if err != nil {
		aohlog.Fatal("unable to initialize temporal client", zap.Error(err))
	}

	wfm.http = &http.Server{
		Addr:    net.JoinHostPort("0.0.0.0", c.Port),
		Handler: handler.NewRouter(factory, temporal.NewService(client, c.Temporal), c.Keycloak.Url),
	}

	aohlog.Info("[INIT] http server starting", zap.String("port", c.Port))
	go func() {
		if err := wfm.http.ListenAndServe(); !errors.Is(err, http.ErrServerClosed) {
			aohlog.Fatal("unable to start http server", zap.Error(err))
		}
		aohlog.Info("[SHUTDOWN] http server shutdown")
	}()
}

// Stop gracefully exit the app
func (wfm *WorkflowMgr) Stop() {
	aohlog.Info("[SHUTDOWN] workflow worker shutting down")
	err := wfm.http.Shutdown(context.Background())
	if err != nil {
		aohlog.Error("[SHUTDOWN] http server shutdown error", zap.Error(err))
	}
	aohlog.Info("[SHUTDOWN] workflow worker shutdown successfully")
}
