package handler

import (
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
	aohhttp "github.com/mssfoobar/aoh-golib/http"
	aohlog "github.com/mssfoobar/aoh-golib/logger"
	"github.com/mssfoobar/app/wfe/internal/model"
	"github.com/mssfoobar/app/wfe/internal/wfm/handler/util"
	"go.uber.org/zap"
)

type ServiceEvent struct {
	store model.ServiceEventStore
}

func RegisterServiceEvent(factory *model.Factory, keycloakUrl string) http.Handler {
	r := chi.NewRouter()
	util.SetMiddleware(r, keycloakUrl)

	e := ServiceEvent{
		store: factory.NewServiceEventStore(),
	}

	r.Route("/", func(r chi.Router) {
		r.Get("/", e.List)
	})

	return r
}

// List retrieves a list of service event
func (e *ServiceEvent) List(w http.ResponseWriter, r *http.Request) {
	page, err := util.GetQueryPagination(w, r)
	if err != nil {
		return
	}

	resp, err := e.store.ListServiceEvent(r.Context(), &model.ListServiceEventRequest{
		Page: *page,
	})
	if err != nil {
		aohlog.Error("[ServiceEvent] list service event failed",
			zap.Error(err))
		code, err := util.CheckStoreError(err)
		_ = render.Render(w, r, aohhttp.ErrResponse(code, "", []error{err}))
		return
	}

	respData := append(make([]model.ServiceEventResponse, 0), resp.ServiceEvents...)
	respPage := aohhttp.PageResponse{
		Number:       page.Number,
		Size:         page.Size,
		TotalRecords: resp.TotalCount,
		Count:        len(respData),
		Sort:         []string{page.Sorts.String()},
	}

	aohlog.Info("[ServiceEvent] list service event successfully", zap.Int("count", len(respData)))
	_ = render.Render(w, r, aohhttp.PaginationResponse(http.StatusOK, "", respPage, respData))
}
