<script lang="ts">
	import { type Activity } from "$lib/aoh/wfd/activitySDK/api";
	import { onMount } from "svelte";
	import { Label } from "../../components/ui/label";
	import EmailTooltip from "./EmailTooltip.svelte";
	import { Accordion } from "bits-ui";
	import { Textarea } from "../../components/ui/textarea";
	import ChevronRight from "lucide-svelte/icons/chevron-right";
	import Input from "../../components/ui/input/input.svelte";
	import DataDialog from "./DataDialog.svelte";
	import ArrowOutIcon from "lucide-svelte/icons/square-arrow-out-up-right";
	import * as Command from "$lib/aoh/wfd/components/ui/command/index.js";
	import * as Popover from "$lib/aoh/wfd/components/ui/popover/index.js";
	import { buttonVariants } from "$lib/aoh/wfd/components/ui/button";

	type EmailParamsKeys =
		| "subject"
		| "messageBody"
		| "recipientsEmail"
		| "recipientsUsers"
		| "recipientsRoles"
		| "ccEmail"
		| "ccUsers"
		| "ccRoles"
		| "bccEmail"
		| "bccUsers"
		| "bccRoles";

	const parameterMapping = {
		subject: "Subject",
		messageBody: "MessageBody",
		recipientsEmail: "Recipients(Email Address)",
		recipientsUsers: "Recipients(C3 Users)",
		recipientsRoles: "Recipients(C3 Roles)",
		ccEmail: "CC Recipients(Email Address)",
		ccUsers: "CC Recipients(C3 Users)",
		ccRoles: "CC Recipients(C3 Roles)",
		bccEmail: "BCC Recipients(Email Address)",
		bccUsers: "BCC Recipients(C3 Users)",
		bccRoles: "BCC Recipients(C3 Roles)",
	};

	// must declare this props to receive activity from parent
	let { activity }: { activity: Activity } = $props();

	let emailParams = $state(
		Object.keys(parameterMapping).reduce(
			(acc, key) => {
				acc[key as EmailParamsKeys] =
					(activity.getParameter(parameterMapping[key as EmailParamsKeys]) as string) || "";
				return acc;
			},
			{} as Record<EmailParamsKeys, string>
		)
	);
	let errorMessages = $state<Record<string, string>>({ recipientsEmail: "", ccEmail: "", bccEmail: "" });

	let iamsUsers = $state<any[]>([]);
	let iamsRoles = $state<any[]>([]);
	let dataSourceList = $state<any[]>([]);
	let currentKey: string = "";

	let open = $state(false);
	let openDropdown = $state(false);

	let modalTitle = $state("Select Users");
	let rows = $state<any[]>([]);
	let columns = $state<any[]>([]);
	let selectedItems = $state<any[]>([]);
	let keyToCompare = $state<string>("");

	onMount(async () => {
		try {
			const response = await fetch("wfd/api/Send2WaysNoti", {
				method: "GET",
			});
			const data = await response.json();

			if (!data) return;
			iamsUsers = data.users?.map((i: Record<string, any>) => ({
				id: i.user?.id,
				name: `${i.user?.firstName || ""} ${i.user?.lastName || ""}`.trim() || i.user?.username,
				email: i.user?.email,
			}));
			iamsRoles = data.roles;
			dataSourceList = data.datasources?.map((i: Record<string, any>) => i.Name) || [];
		} catch (error) {
			console.error("Error fetching data:", error);
		}
	});

	let selectedDataSource: string = $state((activity.getParameter("selectedDataSource") as string) || "");

	function selectUsers(key: string) {
		open = true;
		currentKey = key;
		rows = iamsUsers;
		columns = [
			{ key: "name", name: "User" },
			{ key: "email", name: "Email" },
		];
		selectedItems =
			emailParams[key as EmailParamsKeys].split(", ")?.map((i) => {
				const [id] = i.split(":");
				return id;
			}) || [];
		keyToCompare = "id";
		modalTitle = "Select Users";
	}

	function selectRoles(key: string) {
		open = true;
		currentKey = key;
		rows = iamsRoles;
		columns = [
			{ key: "name", name: "Role" },
			{ key: "description", name: "Description" },
		];
		keyToCompare = "name";
		selectedItems = emailParams[key as EmailParamsKeys].split(", ") || [];
		modalTitle = "Select Roles";
	}

	const onFilteringClosed = (data: string[]) => {
		const savedKey = parameterMapping[currentKey as EmailParamsKeys];
		if (savedKey) {
			if (["recipientsUsers", "ccUsers", "bccUsers"].includes(currentKey)) {
				data = data.filter(Boolean).map((i) => {
					const user = iamsUsers?.find((user) => user.id === i);
					return user ? `${i}:${user.name}` : i;
				});
			}
			const transformedData = (emailParams[currentKey as EmailParamsKeys] = data.filter(Boolean).join(", "));
			setTimeout(() => {
				activity.setParameter(savedKey, transformedData);
			}, 200);
		}
	};

	function getUserNames(idsString: string) {
		if (!idsString) return "";
		const ids = idsString.split(", ");
		return ids
			.map((item) => {
				const [id] = item.split(":");
				const user = iamsUsers?.find((user) => user.id === id);
				return user ? `${user.name}${user.email ? ` (${user.email})` : ""}` : "";
			})
			.filter(Boolean)
			.join(", ");
	}

	async function copyToClipboard(value: string) {
		if (!value) return;

		const text = `{{${value}}}`;
		if (window.isSecureContext && navigator.clipboard) {
			try {
				await navigator.clipboard.writeText(text);
			} catch (err) {
				console.error("Failed to write to clipboard:", err);
			}
		} else {
			// Fallback for insecure contexts
			const textarea = document.createElement("textarea");
			textarea.value = text;
			textarea.style.position = "fixed";
			textarea.style.left = "-9999px";
			document.body.appendChild(textarea);
			textarea.focus();
			textarea.select();
			try {
				document.execCommand("copy");
			} catch (err) {
				console.error("Fallback copy failed:", err);
			} finally {
				document.body.removeChild(textarea);
			}
		}
	}

	const validateEmails = (emails: string) => {
		if (!emails) return [];
		const nameEmailPattern = /^[\w\s]+ ?\([\w.-]+@[\w.-]+\.\w+\)$/;
		const invalidNameEmails = emails
			.split(/,|\n/)
			.map((entry) => entry.trim())
			.filter((entry) => entry !== "" && !nameEmailPattern.test(entry));
		return invalidNameEmails;
	};

	const saveEmailParam = (key: EmailParamsKeys) => {
		const invalidEmails = validateEmails(emailParams[key]);
		if (invalidEmails.length > 0) {
			errorMessages[key] = `Please enter email address in format: Name (<EMAIL>)`;
		} else {
			setTimeout(() => {
				activity.setParameter(parameterMapping[key], emailParams[key]);
			}, 200);
		}
	};

	const resetEmailError = (key: EmailParamsKeys) => {
		if (errorMessages[key]) {
			errorMessages[key] = "";
		}
	};
</script>

<Accordion.Root
	value={["1", "2", "3", "4"]}
	class="w-[calc(100%+20px)] border border-solid mt-2 -mx-[10px] px-4"
	type="multiple"
>
	<Accordion.Item value="1" class="border-dark-10 border-b px-1.5">
		<Accordion.Header>
			<Accordion.Trigger
				class="my-5 flex w-full flex-1 select-none items-center justify-between text-[15px] font-medium transition-all [&[data-state=open]>span>svg]:rotate-90"
			>
				<span
					class="hover:bg-dark-10 inline-flex mr-2 items-center justify-center rounded-[7px] bg-transparent"
				>
					<ChevronRight />
				</span>
				<span class="w-full text-left"> SOP Notification Message </span>
			</Accordion.Trigger>
		</Accordion.Header>
		<Accordion.Content class="text-sm tracking-[-0.01em]">
			<div class="pb-[25px] flex flex-col gap-4">
				<div class="flex gap-2 items-center">
					<Label for="datasourceList" class="whitespace-nowrap">Select Parameter</Label>
					<Popover.Root bind:open={openDropdown}>
						<Popover.Trigger
							role="combobox"
							class={buttonVariants({ variant: "outline" }) + " w-full truncate justify-between"}
						>
							<span class="truncate !block" title={selectedDataSource}>
								{selectedDataSource || ""}
							</span>
							<span class="icon-[fa6-solid--caret-down] min-w-[0.63em]" aria-hidden="true"></span>
						</Popover.Trigger>
						<Popover.Content class="p-0 w-[310px]">
							<Command.Root>
								<Command.Input placeholder="Search" />
								<Command.List>
									<Command.Empty>Select Parameter</Command.Empty>
									<Command.Group>
										{#each dataSourceList as type}
											<Command.Item
												value={type}
												class="aria-selected:bg-primary aria-selected:text-primary-foreground"
												onSelect={() => {
													openDropdown = false;
													setTimeout(() => {
														activity.setParameter("selectedDataSource", type);
													}, 200);
												}}
											>
												{type}
											</Command.Item>
										{/each}
									</Command.Group>
								</Command.List>
							</Command.Root>
						</Popover.Content>
					</Popover.Root>
					<button
						class="relative !w-full cursor-pointer"
						onclick={(e) => {
							copyToClipboard(selectedDataSource);
						}}
					>
						<Input
							class="!w-full min-h-[40px] pr-6 cursor-pointer"
							name="selectedDataSource"
							readonly
							value={selectedDataSource ? `{{${selectedDataSource}}}` : ""}
						/>

						<i
							class="icon-[fa6-solid--copy] absolute right-2 top-0 h-full flex items-center"
							aria-label="Copy"
						></i>
					</button>
				</div>
				<div class="form-field">
					<Label for="subject">Subject</Label>
					<Textarea
						class="!w-full min-h-[40px]"
						name="subject"
						bind:value={emailParams.subject}
						onblur={() => {
							setTimeout(() => {
								activity.setParameter("Subject", emailParams.subject);
							}, 2000);
						}}
					></Textarea>
				</div>
				<div>
					<Label for="messageBody">Message Body</Label>
					<Textarea
						class="!w-full min-h-[62px]"
						name="messageBody"
						bind:value={emailParams.messageBody}
						onblur={() => {
							setTimeout(() => {
								activity.setParameter("MessageBody", emailParams.messageBody);
							}, 200);
						}}
					></Textarea>
				</div>
			</div></Accordion.Content
		>
	</Accordion.Item>
	<Accordion.Item value="2" class="border-dark-10 border-b px-1.5">
		<Accordion.Header>
			<Accordion.Trigger
				class="flex w-full flex-1 select-none items-center justify-between my-5 text-[15px] font-medium transition-all [&[data-state=open]>span>svg]:rotate-90"
			>
				<span
					class="hover:bg-dark-10 inline-flex mr-2 items-center justify-center rounded-[7px] bg-transparent"
				>
					<ChevronRight />
				</span>
				<span class="w-full text-left"> Recipients </span>
			</Accordion.Trigger>
		</Accordion.Header>
		<Accordion.Content class="text-sm tracking-[-0.01em]">
			<div class="pb-[25px] flex flex-col gap-4">
				<div class="form-field">
					<Label for="recipientsEmail">Email Address</Label>
					<div class="flex">
						<Textarea
							class="flex-1 min-h-[40px]"
							name="recipientsEmail"
							bind:value={emailParams.recipientsEmail}
							onblur={() => {
								saveEmailParam("recipientsEmail");
							}}
							oninput={() => {
								resetEmailError("recipientsEmail");
							}}
							placeholder="John Doe (<EMAIL>), ..."
						></Textarea>
						<EmailTooltip />
					</div>
					<div class="mt-2 text-red-600 text-xs">{@html errorMessages.recipientsEmail}</div>
				</div>
				<div class="form-field">
					<Label for="recipientsUsers">C3 Users</Label>
					<div class="flex">
						<Textarea
							readonly
							class="!w-full min-h-[62px]"
							name="recipientsUsers"
							value={getUserNames(emailParams.recipientsUsers)}
						></Textarea>
						<button
							class="ml-2"
							onclick={() => {
								selectUsers("recipientsUsers");
							}}><ArrowOutIcon /></button
						>
					</div>
				</div>
				<div class="form-field">
					<Label for="recipientsRoles">Roles</Label>
					<div class="flex">
						<Textarea
							readonly
							class="!w-full min-h-[62px]"
							name="recipientsRoles"
							value={emailParams.recipientsRoles}
						></Textarea>
						<button
							class="ml-2"
							onclick={() => {
								selectRoles("recipientsRoles");
							}}><ArrowOutIcon /></button
						>
					</div>
				</div>
			</div>
		</Accordion.Content>
	</Accordion.Item>
	<Accordion.Item value="3" class="border-dark-10 border-b px-1.5">
		<Accordion.Header>
			<Accordion.Trigger
				class="flex w-full flex-1 select-none items-center justify-between my-5 text-[15px] font-medium transition-all [&[data-state=open]>span>svg]:rotate-90"
			>
				<span
					class="hover:bg-dark-10 inline-flex mr-2 items-center justify-center rounded-[7px] bg-transparent"
				>
					<ChevronRight />
				</span>
				<span class="w-full text-left"> CC Recipients </span>
			</Accordion.Trigger>
		</Accordion.Header>
		<Accordion.Content class="text-sm tracking-[-0.01em]">
			<div class="pb-[25px] flex flex-col gap-4">
				<div class="form-field">
					<Label for="ccEmail">Email Address</Label>
					<div class="flex">
						<Textarea
							class="flex-1 min-h-[40px]"
							name="ccEmail"
							bind:value={emailParams.ccEmail}
							onblur={() => {
								saveEmailParam("ccEmail");
							}}
							oninput={() => {
								resetEmailError("ccEmail");
							}}
							placeholder="John Doe (<EMAIL>), ..."
						/>
						<EmailTooltip />
					</div>
					<div class="mt-2 text-red-600 text-xs">{@html errorMessages.ccEmail}</div>
				</div>
				<div class="form-field">
					<Label for="ccUsers">C3 Users</Label>
					<div class="flex">
						<Textarea
							readonly
							class="!w-full min-h-[62px]"
							name="ccUsers"
							value={getUserNames(emailParams.ccUsers)}
						></Textarea>
						<button
							class="ml-2"
							onclick={() => {
								selectUsers("ccUsers");
							}}><ArrowOutIcon /></button
						>
					</div>
				</div>
				<div class="form-field">
					<Label for="ccRoles">Roles</Label>
					<div class="flex">
						<Textarea readonly class="!w-full min-h-[62px]" name="ccRoles" value={emailParams.ccRoles} />
						<button
							class="ml-2"
							onclick={() => {
								selectRoles("ccRoles");
							}}><ArrowOutIcon /></button
						>
					</div>
				</div>
			</div>
		</Accordion.Content>
	</Accordion.Item>
	<Accordion.Item value="4" class="border-dark-10 px-1.5">
		<Accordion.Header>
			<Accordion.Trigger
				class="flex w-full flex-1 select-none items-center justify-between my-5 text-[15px] font-medium transition-all [&[data-state=open]>span>svg]:rotate-90"
			>
				<span
					class="hover:bg-dark-10 inline-flex mr-2 items-center justify-center rounded-[7px] bg-transparent"
				>
					<ChevronRight />
				</span>
				<span class="w-full text-left"> BCC Recipients </span>
			</Accordion.Trigger>
		</Accordion.Header>
		<Accordion.Content class="text-sm tracking-[-0.01em]">
			<div class="pb-[25px] flex flex-col gap-4">
				<div class="form-field">
					<Label for="bccEmail">Email Address</Label>
					<div class="flex">
						<Textarea
							class="flex-1 min-h-[40px]"
							name="bccEmail"
							bind:value={emailParams.bccEmail}
							onblur={() => {
								saveEmailParam("bccEmail");
							}}
							oninput={() => {
								resetEmailError("bccEmail");
							}}
							placeholder="John Doe (<EMAIL>), ..."
						/>
						<EmailTooltip />
					</div>
					<div class="mt-2 text-red-600 text-xs">{@html errorMessages.bccEmail}</div>
				</div>
				<div class="form-field">
					<Label for="bccUsers">C3 Users</Label>
					<div class="flex">
						<Textarea
							readonly
							class="!w-full min-h-[62px]"
							name="bccUsers"
							value={getUserNames(emailParams.bccUsers)}
						></Textarea>
						<button
							class="ml-2"
							onclick={() => {
								selectUsers("bccUsers");
							}}><ArrowOutIcon /></button
						>
					</div>
				</div>
				<div class="form-field">
					<Label for="bccRoles">Roles</Label>
					<div class="flex">
						<Textarea readonly class="!w-full min-h-[62px]" name="bccRoles" value={emailParams.bccRoles}
						></Textarea>
						<button
							class="ml-2"
							onclick={() => {
								selectRoles("bccRoles");
							}}><ArrowOutIcon /></button
						>
					</div>
				</div>
			</div>
		</Accordion.Content>
	</Accordion.Item>
</Accordion.Root>

{#if open}
	<DataDialog
		bind:open
		{modalTitle}
		{rows}
		{columns}
		{keyToCompare}
		{selectedItems}
		on:close={(e) => onFilteringClosed(e?.detail)}
	/>
{/if}
