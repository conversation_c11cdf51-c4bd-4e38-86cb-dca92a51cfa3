<script lang="ts">
	import { PUBLIC_STATIC_BUILD_VERSION } from "$env/static/public";

	import { env } from "$env/dynamic/public";

	const env_vars = [
		{ key: "PUBLIC_STATIC_BUILD_VERSION", value: PUBLIC_STATIC_BUILD_VERSION },
		{ key: "PUBLIC_DOMAIN", value: env.PUBLIC_DOMAIN },
		{ key: "PUBLIC_COOKIE_PREFIX", value: env.PUBLIC_COOKIE_PREFIX },
	];
</script>

<div class="flex flex-col bg-background text-foreground w-screen h-screen justify-center items-center p-4">
	<div class="text p-2">Public Environment Variables</div>
	<div class="grid grid-cols-2 bg-muted rounded-lg">
		<div class="p-4 border-b border-b-accent-foreground/75">Key</div>
		<div class="p-4 border-b border-b-accent-foreground/75">Value</div>
		{#each env_vars as ev, i}
			<div
				class:border-b={i !== env_vars.length - 1}
				class:border-b-accent-foreground={i !== env_vars.length - 1}
				class="border-opacity-25 px-4 py-2 text-muted-foreground"
			>
				{ev.key}
			</div>
			<div
				class:border-b={i !== env_vars.length - 1}
				class:border-b-accent-foreground={i !== env_vars.length - 1}
				class="border-opacity-25 px-4 py-2"
			>
				{ev.value}
			</div>
		{/each}
	</div>
</div>
