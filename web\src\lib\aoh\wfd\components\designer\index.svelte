<script lang="ts" module>
	import { ui, dia, shapes, setTheme, linkTools } from "rappid/rappid";
	import { Group, util } from "$lib/aoh/wfd/bpmn/shapes";
	import type { Workflow, Activity, Event, Form } from "$lib/aoh/wfd/types";
	// import { logger } from "@mssfoobar/logger/Logger";

	interface Props {
		workflows: Array<Workflow>;
		activities: Array<Activity>;
		events: Array<Event>;
		forms: Array<Form>;
	}

	const onAdd = function (cell: dia.Cell) {
		if (util.hasOnAdd(cell)) {
			cell.onAdd();
		}
	};

	const onChangeAttrs = function (cell: dia.Cell, newAttrs: unknown, property: { propertyPath: string }) {
		if (util.hasOnChangeAttrs(cell)) {
			cell.onChangeAttrs(property.propertyPath);
		}
	};

	const onRemove = function (graph: dia.Graph) {
		return function (cell: dia.Cell) {
			if (util.hasOnRemove(cell)) {
				cell.onRemove(graph);
			}
		};
	};

	/* Graph initialization */
	const graph = new dia.Graph({ type: "bpmn" }, { cellNamespace: shapes });
	graph.on("add", onAdd);
	graph.on("change:attrs", onChangeAttrs);
	graph.on("remove", onRemove(graph));
	const commandManager = new dia.CommandManager({ graph: graph });

	/* Clipboard */
	const clipboard = new ui.Clipboard({ useLocalStorage: false });

	// const log = logger.child({ src: new URL(import.meta.url).pathname });
</script>

<script lang="ts">
	import { onMount } from "svelte";
	import "rappid/rappid.css";
	import "$lib/aoh/wfd/bpmn/bpmn.css";
	import * as Resizable from "$lib/aoh/wfd/components/ui/resizable";
	import { Toaster } from "$lib/aoh/wfd/components/ui/sonner/index";
	import Separator from "$lib/aoh/wfd/components/ui/separator/separator.svelte";
	import { initHalo } from "$lib/aoh/wfd/bpmn/halo";
	import Menu from "$lib/aoh/wfd/components/menu/index.svelte";
	import Paper from "$lib/aoh/wfd/components/jointUI/Paper.svelte";
	import Stencil from "$lib/aoh/wfd/components/jointUI/Stencil.svelte";
	import Inspector from "$lib/aoh/wfd/components/jointUI/Inspector.svelte";
	import { inspectorConfig } from "$lib/aoh/wfd/bpmn/inspector";
	import { workflowStore } from "$lib/aoh/wfd/stores/workflows";
	import { activityStore } from "$lib/aoh/wfd/stores/activities";
	import { eventStore } from "$lib/aoh/wfd/stores/events";
	import { formStore } from "$lib/aoh/wfd/stores/forms";
	import { validators } from "$lib/aoh/wfd/bpmn/validators";

	let { workflows, activities, events, forms }: Props = $props();

	/* JointJS */
	let paper: dia.Paper = $state()!;
	let selection: ui.Selection = $state()!;
	let snaplines: ui.Snaplines = $state()!;

	// Control
	let graphScale = 1;

	let inspector: {
		open: (cell: dia.Cell) => void;
		close: () => void;
	} = $state()!;

	// check if the cell is already opened
	let currentOpenedCellId: string | null = $state(null);

	const closeTools = () => {
		currentOpenedCellId = null;
		paper.removeTools();
		ui.FreeTransform.clear(paper);
		ui.Halo.clear(paper);
		inspector.close();
	};

	const createElementHalo = (cellView: dia.CellView) => {
		const haloBar = new ui.Halo({
			cellView: cellView,
			type: "toolbar",
			theme: "bpmn",
			useModelGeometry: true,
			clearAll: true,
			boxContent: false,
		});

		haloBar.removeHandle("clone");
		haloBar.removeHandle("fork");
		haloBar.removeHandle("unlink");
		haloBar.removeHandle("resize");
		haloBar.removeHandle("rotate");

		initHalo(haloBar, graph, paper, commandManager, openTools);
		haloBar.render();
	};

	const createLinkTools = (linkView: dia.LinkView) => {
		const ns = linkTools;
		const toolsView = new dia.ToolsView({
			name: "link-pointerdown",
			tools: [
				new ns.Vertices(),
				new ns.SourceAnchor(),
				new ns.TargetAnchor(),
				new ns.SourceArrowhead(),
				new ns.TargetArrowhead(),
				new ns.Segments(),
				new ns.Boundary({ padding: 15 }),
				new ns.Remove({ offset: -20, distance: 40 }),
			],
		});

		linkView.addTools(toolsView);
	};

	const openTools = (cellView: dia.CellView) => {
		if (currentOpenedCellId === cellView.cid) return;
		closeTools();
		currentOpenedCellId = cellView.cid;
		const cell = cellView.model;
		selection.collection.reset();
		selection.collection.add(cell, { silent: true });

		inspector.open(cell);

		if (cell.isElement()) {
			createElementHalo(cellView);

			if (cell instanceof Group) {
				new ui.FreeTransform({
					cellView,
					allowOrthogonalResize: false,
					allowRotation: false,
					minWidth: 30,
					minHeight: 30,
				}).render();
			}
		} else {
			createLinkTools(cellView as dia.LinkView);
		}
	};

	onMount(() => {
		workflowStore.set(workflows);
		activityStore.set(activities);
		eventStore.set(events);
		formStore.set(forms);
		setTheme("bpmn");

		/* Validator */
		const validator = new dia.Validator({ commandManager: commandManager });
		validator.validate("add", ...validators.add({ paper, selection }));

		// validator error handling
		const validatorErrorCallback = (err: Error, _command: unknown, next: (err?: Error) => void) => {
			if (err) {
				if (err?.message) {
					// log.error(err);
				}
			}
			return next(err);
		};
		["add", "change", "remove"].forEach((e) => validator.validate(e, validatorErrorCallback));

		/* JointJS+ Keyboard */
		new ui.Keyboard().on({
			"delete backspace": () => {
				graph.removeCells(selection.collection.toArray());
			},
			"ctrl+z": () => commandManager.undo(),
			"ctrl+y": () => commandManager.redo(),
			"ctrl+c": () => {
				clipboard.copyElements(selection.collection, paper.model);
			},
			"ctrl+x": () => {
				clipboard.cutElements(selection.collection, paper.model);
			},
			"ctrl+v": () => {
				clipboard.pasteCells(paper.model);
			},
		});
	});

	// Event handlers
	const paperEvent = {
		blankPointerdown: (event: dia.Event, _x: number, _y: number) => {
			closeTools();
			selection.startSelecting(event);
		},
		cellPointerdown: (cellView: dia.CellView, _e: dia.Event, _x: number, _y: number) => {
			openTools(cellView);
		},
		linkMouseenter: (linkView: dia.LinkView) => {
			// Open tool only if there is none yet
			if (linkView.hasTools()) return;

			const ns = linkTools;
			const toolsView = new dia.ToolsView({
				name: "link-hover",
				tools: [new ns.Vertices({ vertexAdding: false }), new ns.SourceArrowhead(), new ns.TargetArrowhead()],
			});

			linkView.addTools(toolsView);
		},
		linkMouseleave: (linkView: dia.LinkView) => {
			// Remove only the hover tool, not the pointerdown tool
			if (linkView.hasTools("link-hover")) {
				linkView.removeTools();
			}
		},
	};

	function paperScale(sx: number, sy: number) {
		paper.scale(sx, sy);
	}

	function zoomOut() {
		graphScale -= 0.1;
		paperScale(graphScale, graphScale);
	}

	function zoomIn() {
		graphScale += 0.1;
		paperScale(graphScale, graphScale);
	}

	function resetZoom() {
		graphScale = 1;
		paperScale(graphScale, graphScale);
	}

	function handleUndo() {
		commandManager.undo();
	}

	function handleRedo() {
		commandManager.redo();
	}
</script>

<div class="h-full w-full flex-col overflow-hidden">
	<div class="flex flex-col py-4 sm:flex-row sm:items-center sm:space-y-0 md:h-16">
		<h2 class="text-lg pl-3 font-semibold whitespace-nowrap">Workflow Designer</h2>
		<Menu {graph}></Menu>
	</div>
	<Separator />
	<Resizable.PaneGroup direction="horizontal" class="relative max-h-[calc(100%-4.0rem)]">
		<Resizable.Pane defaultSize={80}>
			<div class="relative h-full">
				<Paper
					{graph}
					bind:paper
					bind:selection
					bind:snaplines
					blankPointerDown={paperEvent.blankPointerdown}
					linkMouseEnter={paperEvent.linkMouseenter}
					linkMouseLeave={paperEvent.linkMouseleave}
					cellPointerDown={paperEvent.cellPointerdown}
				/>
				<Stencil {paper} {snaplines} elementDrop={openTools} />

				<div class="absolute bottom-10 right-10 flex gap-3 p-2 bg-background rounded border">
					<button class="icon-[fa6-solid--crosshairs]" aria-label="reset zoom" onclick={resetZoom}></button>
					<button class="icon-[fa6-solid--plus]" aria-label="zoom in" onclick={zoomIn}></button>
					<button class="icon-[fa6-solid--minus]" aria-label="zoom out" onclick={zoomOut}></button>
					<button class="icon-[fa6-solid--rotate-left]" aria-label="Undo" onclick={handleUndo}></button>
					<button class="icon-[fa6-solid--rotate-right]" aria-label="Redo" onclick={handleRedo}></button>
				</div>
			</div>
		</Resizable.Pane>
		<Resizable.Handle withHandle />
		<Resizable.Pane defaultSize={20} minSize={20} maxSize={40} class="!overflow-auto">
			<Inspector bind:inspectorControl={inspector} opts={inspectorConfig.opts} events={inspectorConfig.events}
			></Inspector>
		</Resizable.Pane>
	</Resizable.PaneGroup>
</div>

<Toaster />
