import type Backbone from "backbone";
import { dia, shapes, util as jointUtil } from "rappid/rappid";
import { TAB_VALUE } from "$lib/aoh/wfd/components/inspector/constant";
import { ConditionalBasicOperator } from "$lib/aoh/wfd/bpmn/dsl/schema";
import { InputValidatorError } from "$lib/aoh/wfd/bpmn/utils/error";
import { condOperator } from "$lib/aoh/wfd/bpmn/utils/misc";
import { OrderedNumBiDict } from "$lib/aoh/wfd/bpmn/utils/type";

export type ID = string | Backbone.Model["id"];

export interface InputValidate {
	validateProperty(path: string, value: unknown): void;
}

export interface OnChangeAttrs {
	onChangeAttrs(path: string): void;
}

export interface OnAdd {
	onAdd(): void;
}

export interface OnConnect {
	onConnect(flow: Flow): void;
}

export interface OnDisconnect {
	onDisconnect(flow: Flow): void;
}

export interface OnRemove {
	onRemove(graph: dia.Graph): void;
}

export interface OnReset {
	onReset(): void;
}

interface Subscriber {
	update(context: { type?: string; path?: string }): void;
	get: Backbone.Model["get"];
}

interface Publisher {
	subscribe(eventName: string, id: ID): void;
	unsubscribe(eventName: string, id?: ID): void;
	notify(eventName: string): void;
}

function hasOnChangeAttrs(obj: object): obj is OnChangeAttrs {
	return "onChangeAttrs" in obj;
}

function hasOnAdd(obj: object): obj is OnAdd {
	return "onAdd" in obj;
}

function hasInputValidate(obj: object): obj is InputValidate {
	return "validateProperty" in obj;
}

function hasOnConnect(obj: object): obj is OnConnect {
	return "onConnect" in obj;
}

function hasOnDisconnect(obj: object): obj is OnDisconnect {
	return "onDisconnect" in obj;
}

function hasOnRemove(obj: object): obj is OnRemove {
	return "onRemove" in obj;
}

function hasOnReset(obj: object): obj is OnReset {
	return "onReset" in obj;
}

function isSubscriber(obj: object): obj is Subscriber {
	return "update" in obj;
}

export const util = {
	hasOnChangeAttrs,
	hasOnAdd,
	hasInputValidate,
	hasOnDisconnect,
	hasOnConnect,
	hasOnRemove,
	hasOnReset,
};

const styleClasses = {
	text: "hsl(var(--primary))",
	stroke: "hsl(var(--primary))",
	background: "hsl(var(--background))",
};

// Extend existing classes
// Generate unique label name for newly added elements
dia.Element.prototype.onAdd = function (this: dia.Element): void {
	let prefix = this.attr("label/text");
	// Use bpmn2 element name if label wasn't provided
	if (!prefix || prefix === "") {
		prefix = this.attributes.type.slice(3);
	}

	// skip on start and end events
	if (prefix === "start" || prefix === "end") {
		return;
	}

	let labelIndex = 0;
	const label = () => prefix + (labelIndex > 0 ? `${labelIndex}` : "");
	const childs: Array<dia.Cell> = this.collection.models;
	const isParallel = this instanceof Parallel;

	// Generate unique label
	const labelCache: Record<string, number> = {};
	for (const element of childs) {
		const labelInMap = element.attributes.attrs?.label?.text as string;
		// Skip itself
		if (this.id === element.id || !labelInMap) {
			continue;
		}

		labelCache[labelInMap] = labelCache[labelInMap] ? labelCache[labelInMap] + 1 : 1;

		const labelExist = () => {
			if (isParallel) {
				return labelCache[label()] > 1;
			}
			return labelCache[label()];
		};

		while (labelExist()) {
			labelIndex++;
		}
	}

	this.attr("label/text", label());
};

// Validates title
dia.Element.prototype.validateProperty = function (path: string, value: unknown) {
	const gElements = this.graph.getElements();
	// Label
	if (path === "attrs/label/text") {
		// Empty check
		if (value === "" || value === undefined) {
			throw new InputValidatorError("Element name must not be empty");
		}

		const cellId = this.attributes?.id;
		const cellIsParallel = this instanceof Parallel;

		// Duplicate check
		let parallelCount = 0; // Parallel count for duplicate check
		for (const element of gElements) {
			if (element.attributes.attrs?.label?.text === value && element.id !== cellId) {
				if (cellIsParallel && parallelCount < 1) {
					parallelCount++;
					continue;
				}
				throw new InputValidatorError("Duplicated element name");
			}
		}
	}
};

// Change color
dia.Element.prototype.changeColor = function () {
	throw new Error("changeColor is not implemented on this shape");
};

function changeElementColor(el: dia.Element, color?: string) {
	if (color) {
		el.attr("border/stroke", color);
		el.attr("icon/iconColor", color);
	} else {
		el.attr("border/stroke", el.defaults().attrs?.border?.stroke);
		el.attr("icon/iconColor", el.defaults().attrs?.icon?.iconColor);
	}
}

export abstract class ActivityBase extends shapes.bpmn2.Activity implements Publisher {
	private subscribers: Record<string, ID[]> = {};

	constructor(
		attributes?: shapes.bpmn2.Activity.Attributes<shapes.bpmn2.Activity.Selectors>,
		options?: dia.Graph.Options
	) {
		super(attributes, options);

		if (!attributes?.subscribers) {
			return;
		}

		for (const [eventName, subscribers] of Object.entries(attributes.subscribers)) {
			if (!eventName || !subscribers) {
				continue;
			}

			if (!this.subscribers[eventName]) {
				this.subscribers[eventName] = [];
			}

			for (const id of subscribers as string[]) {
				this.subscribers[eventName].push(id);
			}
		}
	}

	// I can't make backbone event work properly for my case so I just roll my own solution
	subscribe(eventName: string, id: ID): void {
		if (!this.subscribers[eventName]) {
			this.subscribers[eventName] = [];
		}

		const cell = this.graph.getCell(id);
		if (cell && isSubscriber(cell)) {
			this.subscribers[eventName].push(id);
		}
	}

	unsubscribe(eventName: string, id?: ID): void {
		if (!id) {
			this.subscribers[eventName] = [];
			return;
		}

		this.subscribers[eventName] = this.subscribers[eventName].filter((sub) => {
			return id !== sub;
		});
	}

	notify(eventName: string): void {
		if (!this.subscribers[eventName]) {
			return;
		}

		this.subscribers[eventName].forEach((s: ID) => {
			const cell = this.graph.getCell(s);
			if (isSubscriber(cell)) {
				cell.update({
					type: this.get("type"),
					path: eventName,
				});
			}
		});
	}

	changeColor(color?: string) {
		changeElementColor(this, color);
	}

	onChangeAttrs(path: string): void {
		this.notify(path);
	}

	toJSON(): dia.Cell.JSON {
		const json = super.toJSON();

		json.subscribers = {};
		for (const [k, v] of Object.entries(this.subscribers)) {
			json.subscribers[k] = v;
		}

		return json;
	}
}

export class Activity extends ActivityBase implements Publisher {
	static get type(): string {
		return "wf.Activity";
	}

	defaults(): Partial<dia.Element.Attributes> {
		// this clone the default element label attributes into new object typeAttrs for annotating element type
		const superLabel = (super.defaults as dia.Element.Attributes).attrs?.label;
		const typeAttrs = jointUtil.cloneDeep(superLabel);
		if (typeAttrs) {
			typeAttrs.refY = "120%";
			typeAttrs.fill = styleClasses.text;
		}

		return jointUtil.defaultsDeep(
			{
				type: Activity.type,
				size: {
					width: 100,
					height: 80,
				},
				attrs: {
					label: {
						fill: styleClasses.text,
					},
					background: {
						fill: styleClasses.background,
					},
					border: {
						stroke: styleClasses.stroke,
					},
					type: typeAttrs,
					data: {
						options: {
							scheduleToStartTimeout: 60,
							startToCloseTimeout: 60,
							scheduleToCloseTimeout: 60,
							heartbeatTimeout: 60,
							waitForCancellation: true,
						},
					},
				},
				markup: (this.markup as dia.MarkupNodeJSON[]).concat({
					tagName: "text",
					selector: "type",
					attributes: {
						fill: styleClasses.text,
					},
				}),
			},
			super.defaults
		);
	}

	validateProperty(path: string, value: unknown) {
		super.validateProperty(path, value);

		if (path === "attrs/data/activityOptionsString") {
			// Empty check
			if (value === "") {
				throw new InputValidatorError("Activity options must not be empty");
			}
		}

		// Activity options retry policy
		const isScheduletoStartTimeout: boolean = path === "attrs/data/options/scheduleToStartTimeout";
		const isStartToCloseTimeout: boolean = path === "attrs/data/options/startToCloseTimeout";
		if (isStartToCloseTimeout || isScheduletoStartTimeout) {
			const data = isStartToCloseTimeout
				? this.attr("data/options/scheduleToStartTimeout")
				: this.attr("data/options/startToStartTimeout");

			const isValid = value === undefined || value === "" ? data !== undefined || data === "" : true;

			if (!isValid) {
				throw new InputValidatorError("Schedule OR Start to close timeout must not be empty");
			}
		}
	}
}

export class Form extends ActivityBase {
	static get type(): string {
		return "wf.Form";
	}

	defaults(): Partial<dia.Element.Attributes> {
		const superLabel = (super.defaults as dia.Element.Attributes).attrs?.label;
		const typeAttrs = jointUtil.cloneDeep(superLabel);
		if (typeAttrs) {
			typeAttrs.refY = "120%";
			typeAttrs.fill = styleClasses.text;
			typeAttrs.text = "Form";
		}

		return jointUtil.defaultsDeep(
			{
				type: Form.type,
				size: {
					width: 100,
					height: 80,
				},
				attrs: {
					label: {
						fill: styleClasses.text,
					},
					background: {
						fill: styleClasses.background,
					},
					border: {
						stroke: styleClasses.stroke,
					},
					type: typeAttrs,
					icon: {
						class: "dark:invert",
						iconType: "user",
						iconColor: "currentColor",
					},
					data: {
						resultType: "object",
						taskResult: {},
					},
				},
				markup: (this.markup as dia.MarkupNodeJSON[]).concat({
					tagName: "text",
					selector: "type",
					attributes: {
						fill: styleClasses.text,
					},
				}),
			},
			super.defaults
		);
	}
}

export class CallActivity extends ActivityBase {
	static get type(): string {
		return "wf.CallActivity";
	}

	defaults(): Partial<dia.Element.Attributes> {
		const superLabel = (super.defaults as dia.Element.Attributes).attrs?.label;
		const typeAttrs = jointUtil.cloneDeep(superLabel);
		if (typeAttrs) {
			typeAttrs.refY = "120%";
			typeAttrs.fill = styleClasses.text;
			typeAttrs.text = "CallActivity";
		}

		return jointUtil.defaultsDeep(
			{
				type: CallActivity.type,
				size: {
					width: 100,
					height: 80,
				},
				attrs: {
					label: {
						fill: styleClasses.text,
					},
					background: {
						fill: styleClasses.background,
					},
					border: {
						stroke: styleClasses.stroke,
						borderType: "thick",
					},
					type: typeAttrs,
					markers: {
						class: "dark:invert",
						iconTypes: ["sub-process"],
						iconColor: "currentColor",
					},
				},
				markup: (this.markup as dia.MarkupNodeJSON[]).concat({
					tagName: "text",
					selector: "type",
					attributes: {
						fill: styleClasses.text,
					},
				}),
			},
			super.defaults
		);
	}
}

export class End extends shapes.bpmn2.Event {
	static get type(): string {
		return "wf.End";
	}

	defaults(): Partial<dia.Element.Attributes> {
		return jointUtil.defaultsDeep(
			{
				type: End.type,
				attrs: {
					label: { text: "End", fill: styleClasses.text },
					border: { stroke: styleClasses.stroke, borderType: "thick" },
					background: { fill: styleClasses.background },
				},
			},
			super.defaults
		);
	}

	changeColor(color: string) {
		changeElementColor(this, color);
	}
}

export class Terminate extends shapes.bpmn2.Event {
	static get type(): string {
		return "wf.Terminate";
	}

	defaults(): Partial<dia.Element.Attributes> {
		return jointUtil.defaultsDeep(
			{
				type: Terminate.type,
				attrs: {
					label: { text: "Terminate", fill: styleClasses.text },
					border: { stroke: styleClasses.stroke, borderType: "thick" },
					background: { fill: styleClasses.background },
					icon: {
						class: "dark:invert",
						iconType: "termination2",
						iconColor: "currentColor",
					},
				},
			},
			super.defaults
		);
	}

	changeColor(color: string) {
		changeElementColor(this, color);
	}
}

export class Start extends shapes.bpmn2.Event {
	static get type(): string {
		return "wf.Start";
	}

	defaults(): Partial<dia.Element.Attributes> {
		return jointUtil.defaultsDeep(
			{
				type: Start.type,
				attrs: {
					label: { text: "Start", fill: styleClasses.text },
					border: { stroke: `${styleClasses.stroke}` },
					background: { fill: styleClasses.background },
				},
			},
			super.defaults
		);
	}

	changeColor(color: string) {
		if (color) {
			this.attr("border/stroke", color);
			this.attr("icon/iconColor", color);
		} else {
			this.attr("border/stroke", this.defaults().attrs?.border?.stroke);
			this.attr("icon/iconColor", this.defaults().attrs?.icon?.iconColor);
		}
	}
}

export class Event extends shapes.bpmn2.Event {
	static get type(): string {
		return "wf.Event";
	}

	defaults(): Partial<dia.Element.Attributes> {
		return jointUtil.defaultsDeep(
			{
				type: Event.type,
				attrs: {
					label: { text: "event", fill: styleClasses.text },
					background: { fill: styleClasses.background },
					border: { stroke: styleClasses.stroke, borderType: "double" },
					icon: {
						class: "dark:invert",
						iconColor: "currentColor",
					},
				},
			},
			super.defaults
		);
	}

	changeColor(color: string) {
		changeElementColor(this, color);
	}
}

type FlowTypes = "sequence" | "default" | "message" | "conditional" | "pipeline";

export class Flow extends shapes.bpmn2.Flow implements OnRemove, Subscriber {
	static get type(): string {
		return "wf.Flow";
	}

	static readonly ConditionType = {
		basic: "Basic",
		advanced: "Advanced",
	};

	constructor(
		attributes?: shapes.bpmn2.Flow.Attributes<shapes.bpmn2.Flow.Selectors>,
		options?: dia.Graph.Options,
		type: FlowTypes = "sequence"
	) {
		super(attributes, options);

		// Change flow attribute
		if (!attributes?.attrs?.line?.flowType) {
			this.to(type);
		}

		this.on("change:source", (flow: Flow) => {
			if (flow.getSourceCell()?.get("type") === Switch.type) {
				flow.to("conditional");
			} else if (flow.isConditional()) {
				flow.to("sequence");
			}
			this.removeAttr("condition/order");
		});
	}

	/**
	 * @param source - ID of source element
	 * @param target - ID of target element
	 * @returns New Flow connect source and target
	 */
	static connects(source: ID, target: ID): Flow {
		return new Flow({
			source: {
				id: source,
			},
			target: {
				id: target,
			},
		});
	}

	defaults(): Partial<dia.Link.Attributes> {
		return jointUtil.defaultsDeep(
			{
				type: Flow.type,
				attrs: {
					line: {
						stroke: styleClasses.stroke,
					},
					condition: {
						operator: ConditionalBasicOperator.Eq,
					},
				},
			},
			super.defaults
		);
	}

	/** Handle label name after type change
	 * */
	private postTypeChange() {
		const type = this.attr("line/flowType") as FlowTypes;
		switch (type) {
			case "sequence":
				this.removeLabel();
				break;
			case "default":
				this.appendLabel({ attrs: { label: { text: "default" } } });
				break;
			case "conditional":
				this.updateConditionalLabel();
				break;
		}
	}

	// we only need one label
	appendLabel(label: dia.Link.Label, opt?: dia.ModelSetOptions): dia.Link.Label[] {
		if (this.hasLabels()) {
			this.removeLabel();
		}
		return super.appendLabel(label, opt);
	}

	/**
	 * Switch type of flow
	 * @param type - Flow type
	 */
	to(type: FlowTypes) {
		let flowType;
		switch (type) {
			case "default":
				flowType = shapes.bpmn2.Flow.FLOW_TYPES.default;
				break;
			case "message":
				flowType = shapes.bpmn2.Flow.FLOW_TYPES.message;
				break;
			case "conditional":
				flowType = shapes.bpmn2.Flow.FLOW_TYPES.conditional;
				break;
			default:
				flowType = shapes.bpmn2.Flow.FLOW_TYPES.sequence;
		}

		this.attr("line/flowType", flowType);
		this.postTypeChange();
	}

	getVariableName(): string {
		const inputElementId = this.attr("condition/variable") as string;
		return this.graph.getCell(inputElementId)?.attr("data/result") || "";
	}

	/** Update label of conditional flow */
	updateConditionalLabel() {
		if (!this.isConditional()) {
			return;
		}

		const sourceElement = this.getSourceElement();
		if (!(sourceElement instanceof Switch)) {
			return;
		}
		const order = sourceElement.orderDict.getByVal(this.id) ?? "";
		const isAdvanced = this.attr("condition/tab") === TAB_VALUE.advanced;
		let label;

		if (isAdvanced) {
			label = this.attr("condition/advanced") || "";
		} else {
			const input = this.getVariableName();
			const operator = this.attr("condition/operator") as ConditionalBasicOperator;

			let value = this.attr("condition/value");

			if (value === undefined || value === null) {
				value = "";
			}
			label = `${input} ${condOperator[operator]} ${value}`;
		}

		this.appendLabel({
			attrs: {
				label: {
					text: `${order ? order + "." : ""} ${label}`,
				},
			},
		});
	}

	update(ctx: { type?: string; path?: string }): void {
		if ((ctx.type === Activity.type || ctx.type === Form.type) && ctx.path === "attrs/data/result") {
			this.updateConditionalLabel();
		}
	}

	isSequence(): boolean {
		return this.attr("line/flowType") === shapes.bpmn2.Flow.FLOW_TYPES.sequence;
	}

	isDefault(): boolean {
		return this.attr("line/flowType") === shapes.bpmn2.Flow.FLOW_TYPES.default;
	}

	isMessage(): boolean {
		return this.attr("line/flowType") === shapes.bpmn2.Flow.FLOW_TYPES.message;
	}

	isConditional(): boolean {
		return this.attr("line/flowType") === shapes.bpmn2.Flow.FLOW_TYPES.conditional;
	}

	isAdvCondition(): boolean {
		return this.isConditional() && this.attr("condition/tab") === Flow.ConditionType.advanced;
	}

	isBasicCondition(): boolean {
		return this.isConditional() && this.attr("condition/tab") === Flow.ConditionType.basic;
	}

	enablePipeline() {
		this.attr("line/sourceMarker", {
			type: "circle",
			r: 5,
			cx: 5,
		});
	}

	disablePipeline() {
		this.removeAttr("line/sourceMarker");
	}

	onAdd() {
		const source = this.getSourceCell();
		if (!source) {
			return;
		}

		// Change flow to conditional if source is switch
		if (source.get("type") === Switch.type) {
			this.to("conditional");
		}
	}

	onChangeAttrs(): void {
		// Execute module callbacks
		this.postTypeChange();
	}

	onRemove(graph: dia.Graph): void {
		// On remove, embedded graph is removed from cell so getSourceCell() cannot be used
		const source = this.source();
		if (!source.id) {
			return;
		}

		if (!this.isConditional()) {
			return;
		}

		const sourceCell = graph.getCell(source.id);
		if (!(sourceCell instanceof Switch)) {
			return;
		}

		sourceCell.unsubscribeCondFlow(this);
		const variable = this.attr("condition/variable");
		if (!variable) {
			return;
		}
	}

	private validateFlowType(value: string) {
		const source = this.getSourceCell();
		if (!source) {
			return;
		}

		if (source.get("type") === Switch.type) {
			// Switch cannot have sequence flow
			if (value === shapes.bpmn2.Flow.FLOW_TYPES.sequence) {
				throw new InputValidatorError("Flows start from switch cannot have sequence type");
			}

			if (value !== shapes.bpmn2.Flow.FLOW_TYPES.default) {
				return;
			}

			// Check for multiple default flows
			const connectedFlows = this.graph.getConnectedLinks(source, {
				outbound: true,
			}) as Flow[];
			if (connectedFlows.find((value) => value.isDefault())) {
				throw new InputValidatorError("A switch cannot have more than one default flow");
			}
		} else if (value !== shapes.bpmn2.Flow.FLOW_TYPES.sequence) {
			// And vice versa, non-Switch cannot have types other than sequence
			throw new InputValidatorError("Flow does not start from switch can only have sequence type");
		}
	}

	validateProperty(path: string, value: unknown): void {
		if (path === "attrs/line/flowType" && typeof value === "string") {
			this.validateFlowType(value);
		}
	}
}

export class Switch extends shapes.bpmn2.Gateway implements OnDisconnect {
	static get type(): string {
		return "wf.Switch";
	}

	readonly orderDict: OrderedNumBiDict<ID>;

	constructor(
		attributes?: shapes.bpmn2.Gateway.Attributes<shapes.bpmn2.Gateway.Selectors>,
		options?: dia.Graph.Options
	) {
		super(attributes, options);
		this.orderDict = new OrderedNumBiDict<ID>();

		if (attributes?.orderDict) {
			this.orderDict.fromJSON(attributes.orderDict);
			delete this.attributes.orderDict;
		}
	}

	defaults(): Partial<dia.Element.Attributes> {
		return jointUtil.defaultsDeep(
			{
				type: Switch.type,
				size: {
					width: 40,
					height: 40,
				},
				attrs: {
					icon: {
						class: "dark:invert",
						iconType: "exclusive",
						iconColor: "currentColor",
					},
					label: {
						text: "Switch",
						fill: styleClasses.text,
					},
					body: { fill: styleClasses.background, stroke: styleClasses.stroke },
				},
			},
			super.defaults
		);
	}

	getOutmostLeafs(): dia.Element[] {
		const result: dia.Element[] = [];
		const graph = this.graph;
		graph.dfs(this, (element: dia.Element) => {
			const outBoundLinks = graph.getConnectedLinks(element, { outbound: true });
			if (outBoundLinks.length === 0) {
				result.push(element);
			}
			return true;
		});

		return result;
	}

	defaultFlow(): Flow | undefined {
		return <Flow>this.graph.getConnectedLinks(this).find((f) => {
			return (f as Flow).isDefault();
		});
	}

	/** Remove conditional flow from switch ordered map */
	unsubscribeCondFlow(flow: Flow) {
		this.orderDict.deleteByVal(flow.id);
	}

	onDisconnect(flow: Flow): void {
		this.unsubscribeCondFlow(flow);
	}

	changeColor(color: string) {
		if (color) {
			this.attr("body/stroke", color);
			this.attr("icon/iconColor", color);
		} else {
			this.attr("body/stroke", this.defaults().attrs?.body?.stroke);
			this.attr("icon/iconColor", this.defaults().attrs?.icon?.iconColor);
		}
	}

	onChangeAttrs(): void {
		const flows = this.graph.getConnectedLinks(this) as Flow[];
		flows.forEach((flow) => {
			if (!flow.isConditional()) {
				return;
			}

			flow.updateConditionalLabel();
		});
	}

	toJSON(): dia.Cell.JSON {
		const json = super.toJSON();
		json.orderDict = this.orderDict.toJSON();
		return json;
	}
}

export class Parallel extends shapes.bpmn2.Gateway {
	static get type(): string {
		return "wf.Parallel";
	}

	defaults(): Partial<dia.Element.Attributes> {
		return jointUtil.defaultsDeep(
			{
				type: "wf.Parallel",
				size: {
					width: 40,
					height: 40,
				},
				attrs: {
					icon: {
						class: "dark:invert",
						iconType: "parallel",
						iconColor: "currentColor",
					},
					label: {
						text: "Parallel",
						fill: styleClasses.text,
					},
					body: { fill: styleClasses.background, stroke: styleClasses.stroke },
				},
			},
			super.defaults
		);
	}

	changeColor(color: string) {
		changeElementColor(this, color);
	}
}

export class Group extends shapes.bpmn2.Group {
	static get type(): string {
		return "wf.Group";
	}

	defaults(): Partial<dia.Element.Attributes> {
		return jointUtil.defaultsDeep(
			{
				type: Group.type,
				size: {
					width: 280,
					height: 210,
				},
				attrs: {
					body: {
						fill: styleClasses.background,
						stroke: styleClasses.stroke,
					},
					label: {
						fill: styleClasses.text,
					},
				},
			},
			super.defaults
		);
	}

	changeColor(color: string) {
		changeElementColor(this, color);
	}
}

Object.assign(shapes, {
	wf: { Activity, CallActivity, Form, Group, Start, End, Terminate, Flow, Switch, Parallel, Event },
});
