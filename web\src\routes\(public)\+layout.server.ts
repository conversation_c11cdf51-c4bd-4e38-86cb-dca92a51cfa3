/** @type {import('./$types').LayoutLoad} */
import dayjs from "dayjs";
import Duration from "dayjs/plugin/duration";
// import { log as logger } from "$lib/aoh/core/logger/Logger";
dayjs.extend(Duration);

// const log = logger.child({ src: new URL(import.meta.url).pathname });

export async function load({ locals }) {
	const authResult = locals.authResult;

	if (authResult.success) {
		// log.debug("User is authenticated");

		return {
			user: authResult.claims,
		};
	} else {
		// log.debug("User is not authenticated, but since this is a public route, we allow the user anyway");
	}
}
