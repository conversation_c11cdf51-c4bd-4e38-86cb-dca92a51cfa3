# Overview

The `web-base` serves 2 main purposes:

1. For projects development, developers can use the `web-base` as the starting point to begin development of their
   web server, and quickly bring in / install AGIL Ops Hub modules that they need. This repository is meant only be a
   starting point - projects typically intend to completely change the look and feel of the website (clients might
   already have their own design system that needs to be conformed with).

2. For product development (contributing to AGIL Ops Hub or any other product domains), developers can use the
   `web-base` as a starting point to develop their modules.

# Development Setup

The `web-base` provides authentication out of the box via a service called `iams`, which uses Keycloak. To get the
requisite services up and running quickly, a `docker-compose` repository is provided with all the services needed
for the relevant modules you need to be working on.

For the `web-base`, the bare minimum is to run the `iams`:

1. Get the `dev-containers` repository which contains all the module docker compose files:

```
git clone https://github.com/mssfoobar/dev-containers
```

2. Go into the newly cloned repository directory:

```
cd dev-containers
```

3. Set the environment variables:

```
cp .env.template .env
```

Example values:

```title=.env
DEV_DOMAIN=127.0.0.1.nip.io
DEV_USER=admin
DEV_PASSWORD= ########## Please use a "COMPLEX" password ##########
```

Note that `DEV_DOMAIN` will be what you use to access all containers and this be `127.0.0.1.nip.io`. You can also
use `localhost` if you wish but you will have to change your hosts file.

4. Run the compose file (we are using `podman` by default, but you can use `docker-compose` instead as well):

```
podman compose --env-file .env -f iams/dc.yml
```

## Environment variables

Please refer to .env.template to fill in the necessary variables and rename it to .env

## Running the Application

-   Install depencencies: `npm install`

-   Either:

    -   Development server: `npm run dev`

    -   Build
        -   Build code: `npm run build`
        -   Preview built code: `npm run preview`

# Directory Structure

This directory contains the following subdirectories:

-   `src`: The source code for the application, including Svelte components and Vite configuration. (Note that /src/lib/ will be a seperate package in the future)
-   `public`: Static assets for the application, such as images and fonts.
-   `build`: The production build of the application, generated by running `npm run build`.
-   `node_modules`: Dependencies installed by running `npm install`.
-   `components`: The components folders are meant to contain Svelte Components - UI components generated by
    `shadcn-svelte` sit inside the `components/ui` folders. Each module might make their own changes to these components, to
    avoid clashes, we keep them namespaced by modules. However, if you want to unify them all, you are expected to merge
    them yourself by lifting the desired component to be shared up into the `$lib/components/ui` directory. For components
    specific to the modules, they're in the `$lib/[project_name]/[module_name]/components/[component_name]`, for example,
    the `Sidebar` component in the web base, which is provided by default by `AOH`, sit's in the `$lib/aoh/core/components/layout/Sidebar/index.svelte`
