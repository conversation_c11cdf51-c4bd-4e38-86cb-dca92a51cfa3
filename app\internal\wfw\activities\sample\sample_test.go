package sample

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"go.temporal.io/sdk/testsuite"
)

func TestActivities_SampleActivityString(t *testing.T) {
	expected := "test"
	a := &Activities{}
	s := &testsuite.WorkflowTestSuite{}
	env := s.NewTestActivityEnvironment()
	env.RegisterActivity(a)
	got, err := env.ExecuteActivity(a.SampleActivityString, expected)
	assert.NoError(t, err)
	var result string
	_ = got.Get(&result)
	assert.Equal(t, expected, result)
}

func TestActivities_SampleActivityNumber(t *testing.T) {
	expected := 12.3
	a := &Activities{}
	s := &testsuite.WorkflowTestSuite{}
	env := s.NewTestActivityEnvironment()
	env.RegisterActivity(a)
	got, err := env.ExecuteActivity(a.SampleActivityNumber, expected)
	assert.NoError(t, err)
	var result float64
	_ = got.Get(&result)
	assert.Equal(t, expected, result)
}

func TestActivities_SampleActivityJson(t *testing.T) {
	expected := map[string]interface{}{"key": "value"}
	a := &Activities{}
	s := &testsuite.WorkflowTestSuite{}
	env := s.NewTestActivityEnvironment()
	env.RegisterActivity(a)
	got, err := env.ExecuteActivity(a.SampleActivityJson, "{\"key\":\"value\"}")
	assert.NoError(t, err)
	var result map[string]interface{}
	_ = got.Get(&result)
	assert.Equal(t, expected, result)
}

func TestActivities_SampleError(t *testing.T) {
	a := &Activities{}
	s := &testsuite.WorkflowTestSuite{}
	env := s.NewTestActivityEnvironment()
	env.RegisterActivity(a)
	_, err := env.ExecuteActivity(a.SampleError)
	assert.Error(t, err)
}
