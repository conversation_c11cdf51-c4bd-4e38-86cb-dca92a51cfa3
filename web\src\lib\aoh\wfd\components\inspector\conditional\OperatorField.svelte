<script lang="ts">
	import { onMount } from "svelte";
	import type { ui } from "rappid/rappid";
	import * as Select from "$lib/aoh/wfd/components/ui/select";
	import { Label } from "$lib/aoh/wfd/components/ui/label";
	import { Flow } from "$lib/aoh/wfd/bpmn/shapes";

	let { inspector, path }: { inspector: ui.Inspector; path: string } = $props();

	const cell = inspector.options.cell as Flow;

	const BasicOperator = [
		{ value: "EQ", label: "==" },
		{ value: "NEQ", label: "!=" },
		{ value: "MT", label: ">" },
		{ value: "LT", label: "<" },
		{ value: "LTE", label: "<=" },
		{ value: "MTE", label: ">=" },
	];

	let value = $state(BasicOperator[0].value);

	onMount(() => {
		value = cell.prop(path);
	});

	const onChangeValue = () => {
		cell.prop(path, value);
	};

	const updateCellValue = () => {
		// shad-cn popover closing animation is slower than jointJS inspector refresh
		// so we need to delay the applyChangeToCell function
		setTimeout(() => {
			onChangeValue();
		}, 200);
	};
	const triggerContent = $derived(BasicOperator.find((f) => f.value === value)?.label ?? BasicOperator[0].label);
</script>

<Label for="operator">Operator</Label>
<Select.Root type="single" allowDeselect={false} name="operator" bind:value onValueChange={updateCellValue}>
	<Select.Trigger class="w-full">
		{triggerContent}
	</Select.Trigger>
	<Select.Content>
		<Select.Group>
			{#each BasicOperator as op}
				<Select.Item value={op.value} label={op.label}>{op.label}</Select.Item>
			{/each}
		</Select.Group>
	</Select.Content>
</Select.Root>
