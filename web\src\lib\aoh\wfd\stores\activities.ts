import { writable, get } from "svelte/store";
import type { Activity } from "../types";

const Activities: Array<Activity> = [];

export const activityStore = writable<typeof Activities>(Activities);

export const getActivityParams = (type: string): Record<string, unknown>[] => {
	const activity = get(activityStore).find((activity: Activity) => activity.activity_type === type);
	return activity ? activity.activity_param : [];
};

export const getActivityTimeout = (type: string): number | undefined => {
	const activity = get(activityStore).find((activity: Activity) => activity.activity_type === type);
	return activity ? activity.timeout_in_second : undefined;
};
