<svelte:options runes={true} />

<script lang="ts">
	import * as Sidebar from "$lib/aoh/core/components/ui/sidebar";
	import AppSidebar, { type SidebarItem } from "$lib/aoh/core/components/layout/Sidebar/index.svelte";
	import Navbar, { type NavbarProps } from "$lib/aoh/core/components/layout/Headerbar/index.svelte";
	import AuthProvider from "$lib/aoh/core/provider/auth/AuthProvider/index.svelte";
	import { Toaster } from "$lib/aoh/core/components/ui/sonner/index.js";

	import { onMount } from "svelte";

	import Workflow from "lucide-svelte/icons/workflow";
	import type { Tenant } from "$lib/aoh/core/provider/auth/auth.js";

	const { data, children } = $props();

	let sidebarItems: SidebarItem[] = $state([
		{
			title: "Workflow Designer",
			icon: Workflow,
			url: "/aoh/wfd",
			isSelected: false,
		},
	]);

	let module: NavbarProps["modules"] = [
		{
			name: "Workflow Engine",
			href: "/",
		},
	];

	let tenant: Tenant | undefined = $state(undefined);
	onMount(() => {
		tenant = data.user?.active_tenant;
	});
</script>

<AuthProvider claims={data.user}>
	<div class="w-full h-full flex bg-background overflow-hidden relative">
		<Sidebar.Provider open={false}>
			<AppSidebar items={sidebarItems} title="AGIL Ops Hub" {tenant} />
			<div class="grow flex flex-col overflow-hidden bg-background w-0">
				<Navbar modules={module} user={data.user} />
				<Toaster position="top-right" closeButton={false} class="flex justify-end" expand={true} />

				<div class="grow relative overflow-y-auto h-1">
					{@render children()}
				</div>
			</div>
		</Sidebar.Provider>
	</div>
</AuthProvider>