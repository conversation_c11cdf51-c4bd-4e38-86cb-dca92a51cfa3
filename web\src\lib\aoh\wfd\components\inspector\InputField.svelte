<script lang="ts">
	import { onMount, type Component } from "svelte";
	import { dia, ui } from "rappid/rappid";
	import { ACTIVITY_COMPONENTS } from "$lib/aoh/wfd/module";
	import { InternalActivity } from "$lib/aoh/wfd/activitySDK/private/activity";
	import ActivityInputField from "$lib/aoh/wfd/components/inspector/ActivityInputField.svelte";

	let { inspector }: { inspector: ui.Inspector } = $props();

	const activity = new InternalActivity(inspector, "");

	const cell = inspector.options.cell as dia.Cell;
	let type: string = cell.attr("type/text");
	let ActivityComponent: Component = $state()!;
	let useCustomUI: boolean = $state(false);

	onMount(() => {
		if (type && ACTIVITY_COMPONENTS[type]) {
			ActivityComponent = ACTIVITY_COMPONENTS[type].default as Component;
			if (ActivityComponent[Symbol.hasInstance]) {
				useCustomUI = true;
			}
		}
	});
</script>

{#if useCustomUI}
	<ActivityComponent {activity}></ActivityComponent>
{:else}
	<ActivityInputField {inspector} {activity}></ActivityInputField>
{/if}
