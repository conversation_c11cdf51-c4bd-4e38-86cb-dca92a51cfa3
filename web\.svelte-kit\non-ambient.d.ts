
// this file is generated — do not edit it


declare module "svelte/elements" {
	export interface HTMLAttributes<T> {
		'data-sveltekit-keepfocus'?: true | '' | 'off' | undefined | null;
		'data-sveltekit-noscroll'?: true | '' | 'off' | undefined | null;
		'data-sveltekit-preload-code'?:
			| true
			| ''
			| 'eager'
			| 'viewport'
			| 'hover'
			| 'tap'
			| 'off'
			| undefined
			| null;
		'data-sveltekit-preload-data'?: true | '' | 'hover' | 'tap' | 'off' | undefined | null;
		'data-sveltekit-reload'?: true | '' | 'off' | undefined | null;
		'data-sveltekit-replacestate'?: true | '' | 'off' | undefined | null;
	}
}

export {};


declare module "$app/types" {
	export interface AppTypes {
		RouteId(): "/(public)/(health)" | "/(public)" | "/(private)" | "/" | "/(public)/_example" | "/(private)/_example" | "/(public)/_example/login" | "/(private)/_example/projects" | "/(public)/aoh" | "/(private)/aoh" | "/(public)/aoh/api" | "/(public)/aoh/api/auth" | "/(public)/aoh/api/auth/login" | "/(public)/aoh/api/auth/logout" | "/(public)/aoh/api/auth/refresh" | "/(public)/aoh/debug" | "/(private)/aoh/wfd" | "/(private)/aoh/wfd/api" | "/(private)/aoh/wfd/api/InAppNotification" | "/(private)/aoh/wfd/api/MOHIncidentReport" | "/(private)/aoh/wfd/api/SendSOPEmail" | "/(private)/aoh/wfd/api/TeamsChannelList" | "/(private)/aoh/wfd/api/getDataSourceList" | "/(public)/(health)/livez" | "/(public)/(health)/readyz";
		RouteParams(): {
			
		};
		LayoutParams(): {
			"/(public)/(health)": Record<string, never>;
			"/(public)": Record<string, never>;
			"/(private)": Record<string, never>;
			"/": Record<string, never>;
			"/(public)/_example": Record<string, never>;
			"/(private)/_example": Record<string, never>;
			"/(public)/_example/login": Record<string, never>;
			"/(private)/_example/projects": Record<string, never>;
			"/(public)/aoh": Record<string, never>;
			"/(private)/aoh": Record<string, never>;
			"/(public)/aoh/api": Record<string, never>;
			"/(public)/aoh/api/auth": Record<string, never>;
			"/(public)/aoh/api/auth/login": Record<string, never>;
			"/(public)/aoh/api/auth/logout": Record<string, never>;
			"/(public)/aoh/api/auth/refresh": Record<string, never>;
			"/(public)/aoh/debug": Record<string, never>;
			"/(private)/aoh/wfd": Record<string, never>;
			"/(private)/aoh/wfd/api": Record<string, never>;
			"/(private)/aoh/wfd/api/InAppNotification": Record<string, never>;
			"/(private)/aoh/wfd/api/MOHIncidentReport": Record<string, never>;
			"/(private)/aoh/wfd/api/SendSOPEmail": Record<string, never>;
			"/(private)/aoh/wfd/api/TeamsChannelList": Record<string, never>;
			"/(private)/aoh/wfd/api/getDataSourceList": Record<string, never>;
			"/(public)/(health)/livez": Record<string, never>;
			"/(public)/(health)/readyz": Record<string, never>
		};
		Pathname(): "/" | "/_example" | "/_example/" | "/_example/login" | "/_example/login/" | "/_example/projects" | "/_example/projects/" | "/aoh" | "/aoh/" | "/aoh/api" | "/aoh/api/" | "/aoh/api/auth" | "/aoh/api/auth/" | "/aoh/api/auth/login" | "/aoh/api/auth/login/" | "/aoh/api/auth/logout" | "/aoh/api/auth/logout/" | "/aoh/api/auth/refresh" | "/aoh/api/auth/refresh/" | "/aoh/debug" | "/aoh/debug/" | "/aoh/wfd" | "/aoh/wfd/" | "/aoh/wfd/api" | "/aoh/wfd/api/" | "/aoh/wfd/api/InAppNotification" | "/aoh/wfd/api/InAppNotification/" | "/aoh/wfd/api/MOHIncidentReport" | "/aoh/wfd/api/MOHIncidentReport/" | "/aoh/wfd/api/SendSOPEmail" | "/aoh/wfd/api/SendSOPEmail/" | "/aoh/wfd/api/TeamsChannelList" | "/aoh/wfd/api/TeamsChannelList/" | "/aoh/wfd/api/getDataSourceList" | "/aoh/wfd/api/getDataSourceList/" | "/livez" | "/livez/" | "/readyz" | "/readyz/";
		ResolvedPathname(): `${"" | `/${string}`}${ReturnType<AppTypes['Pathname']>}`;
		Asset(): "/favicon.ico" | "/favicon.png" | "/images/logo.png" | "/images/wfd/activity.png" | "/images/wfd/callActivity.png" | "/images/wfd/end.png" | "/images/wfd/event.png" | "/images/wfd/form.png" | "/images/wfd/group.png" | "/images/wfd/parallel.png" | "/images/wfd/start.png" | "/images/wfd/switch.png" | "/images/wfd/terminate.png" | string & {};
	}
}