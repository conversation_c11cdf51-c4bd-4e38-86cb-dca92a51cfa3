package sqlplugin

import (
	"context"
	"database/sql"
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

type (
	FormTemplateRow struct {
		Id            uuid.UUID       `db:"id" json:"id"`
		Name          string          `db:"name" json:"name"`
		FormJson      json.RawMessage `db:"form_json" json:"form_json"`
		ComponentKeys json.RawMessage `db:"component_keys" json:"component_keys"`
		// mandatory table columns
		CreatedAt time.Time `db:"created_at" json:"created_at"`
		CreatedBy string    `db:"created_by" json:"created_by"`
		UpdatedAt time.Time `db:"updated_at" json:"updated_at"`
		UpdatedBy string    `db:"updated_by" json:"updated_by"`
		TenantId  string    `db:"tenant_id" json:"tenant_id"`
		OccLock   int       `db:"occ_lock" json:"occ_lock"`
	}

	FormTemplateFilter struct {
		Id       *uuid.UUID
		Name     *string
		TenantId string
	}

	FormTemplatePaginateFilter struct {
		TenantId string
		Limit    int
		Offset   int
		OrderBy  string
	}

	FormTemplateCountFilter struct {
		TenantId string
	}

	FormTemplate interface {
		InsertIntoFormTemplate(ctx context.Context, rows *FormTemplateRow) (sql.Result, error)
		UpdateFormTemplate(ctx context.Context, rows *FormTemplateRow) (sql.Result, error)
		SelectFromFormTemplate(ctx context.Context, filter FormTemplateFilter) (*FormTemplateRow, error)
		ListFromFormTemplate(ctx context.Context, filter FormTemplatePaginateFilter) ([]FormTemplateRow, error)
		DeleteFromFormTemplate(ctx context.Context, filter FormTemplateFilter) (sql.Result, error)
		CountFromFormTemplate(ctx context.Context, filter FormTemplateCountFilter) (int, error)
	}
)
