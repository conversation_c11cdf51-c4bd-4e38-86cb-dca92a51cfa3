// See https://kit.svelte.dev/docs/types#app

import type { AuthResult } from "$lib/aoh/core/provider/auth/auth";
import type { Configuration } from "openid-client";

// for information about these interfaces
declare global {
	namespace App {
		// interface Error {}
		interface Locals {
			authResult: AuthResult;
			clients?: {
				oidc_config?: Configuration;
			};
		}
		// interface PageData {}
		// interface PageState {}
		// interface Platform {}
	}

	type HTTPResponseBody<T> = {
		data?: T;
		message?: string;
		sent_at?: ISO8601Date;
		errors?: Array<Error>;
	};
}

export {};
