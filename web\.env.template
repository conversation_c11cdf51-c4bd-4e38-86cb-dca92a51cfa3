## --------------------- <PERSON><PERSON><PERSON><PERSON><PERSON>K INFORMATION -------------------------
## The full OIDC issuer URL for the OIDC provider, required to retrieve service account
## and access the DB for initial config
## This is the URL to keycloak (e.g. https://iams-keycloak.com) plus the specific realm (e.g. /realms/aoh if your
## realm is called "aoh"), which is the "issuer" endpoint - the OIDC discovery suffix (.well-known/openid-configuration)
## is also appended to prevent the OIDC client from performing extra issuer checks which is a problem when separating
## backchannel from front channel communication with Keycloak
IAM_URL=http://iams-keycloak.127.0.0.1.nip.io/realms/aoh/.well-known/openid-configuration

## The client I.D. used to connect to the identity access management server
IAM_CLIENT_ID=web

## -------------------- MISC -----------------------------------
## This is currently used for setting the cookie domain, if these values are invalid, your cookies will NOT be set
## Note that when developing using "localhost" as the domain, this must be left blank for Chromium browsers
PUBLIC_DOMAIN=127.0.0.1.nip.io

## The prefix to add to the cookie key for the access token, refresh token, and code verifier cookie keys, this is to
## avoid clashes when multiple web servers are deployed on the same domain.
PUBLIC_COOKIE_PREFIX=web

## !! IMPORTANT !!
## The origin must be specified for the web server to know what the origin of the front-end request is meant to be,
## it is used by Svelte Kit to determine how to construct URLS when relative paths are used, and it is also used by
## our server to determine whether to store cookies in secure mode (if HTTPS is used) and how to redirect the user
## during the OIDC Authorization Code Flow.
## Scheme: `http://`
## Domains: `web-example.com`
## Port: 80 (default if blank on HTTP, 443 for HTTPS)
## Note that for development you're probably using http://localhost:5173
## https://svelte.dev/docs/kit/adapter-node#Environment-variables-ORIGIN-PROTOCOL_HEADER-HOST_HEADER-and-PORT_HEADER
ORIGIN=http://127.0.0.1.nip.io:5173

## Allow the OIDC client to make HTTP (insecure requests)
## This controls an override setting for the OIDC client and needs to be set when backend servers do not communicate
## in HTTPS
OIDC_ALLOW_INSECURE_REQUESTS=1

## --------------------- LOGIN INFORMATION -------------------------
## Your path should start from ({private or public})/routes
## if the absolute path is /src/routes/(public)/_example/home,
## the login destination or path should be /_example/home
## "LOGIN_DESTINATION" is where the user is sent after logging in
## "LOGIN_PAGE" is where the user is sent when they are NOT logged in
LOGIN_DESTINATION=/_example
LOGIN_PAGE=

## -------------------- STATIC BUILD VARS --------------------
## Supply this at build-time to fix the build version of the application (useful for checking version)
PUBLIC_STATIC_BUILD_VERSION=next

## -------------------- WORKFLOW MANAGER ENV --------------------
## URL to the Workflow Manager backend service
WFM_URL=http://127.0.0.1.nip.io:5000

## -------------------- CUSTOM ACTIVITY API ENV VARS --------------------
## Env variables for Custom Activity API backend service
## please use the prefix `ACTIVITY` when creating env variable for calling backend services in activity api
## e.g. ACTIVITY_IAMS_URL=http://iams-aas.127.0.0.1.nip.io

ACTIVITY_AAS_URL=http://iams-aas.127.0.0.1.nip.io
DATA_AGG_URL=http://localhost:5003
