import type { Input<PERSON><PERSON>date, <PERSON><PERSON><PERSON>, OnChangeAttrs } from "$lib/aoh/wfd/bpmn/shapes";

// Declare missing JointJS+ types
declare module "rappid/rappid" {
	declare namespace ui {
		interface Inspector {
			options: Inspector.Options & { cell: dia.Cell };
			getFieldValue: Inspector.Options["getFieldValue"];
		}

		interface Halo {
			options: Halo.Options & { cellView: dia.CellView };
		}
	}

	declare namespace dia {
		interface Element extends InputValidate, OnAdd, OnChangeAttrs {
			changeColor(color?: string);
		}
	}

	declare namespace shapes {
		declare namespace wf {
			export class Activity extends shapes.bpmn2.Activity {}
			export class Form extends shapes.bpmn2.Activity {}
			export class End extends shapes.bpmn2.Event {}
			export class Start extends shapes.bpmn2.Event {}
			export class Event extends shapes.bpmn2.Event {}
			export class Flow extends shapes.bpmn2.Flow {}
			export class Switch extends shapes.bpmn2.Gateway {}
			export class Parallel extends shapes.bpmn2.Gateway {}
			export class Group extends shapes.bpmn2.Group {}
		}
	}
}

export interface Workflow {
	id: string;
	name: string;
	workflow_json: unknown;
	designer_json: unknown;
	occ_lock: number;
}

export type Activity = {
	id: string;
	activity_type: string;
	activity_icon: string;
	activity_param: Array<Record<string, unknown>>;
	activity_result: unknown;
	timeout_in_second: number;
};

export type Event = {
	id: string;
	event_type: string;
	event_icon: string;
	event_param: Array<Record<string, unknown>>;
	event_result: unknown;
};

export type Form = {
	id: string;
	name: string;
	form_json: unknown;
	component_keys: unknown;
};
