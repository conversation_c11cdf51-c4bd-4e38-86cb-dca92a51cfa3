<script lang="ts">
	import { type Activity } from "$lib/aoh/wfd/activitySDK/api";
	import { onMount } from "svelte";
	import SelectSearch from "$lib/aoh/wfd/components/ui/select/select-search.svelte";
	import Label from "../ui/label/label.svelte";

	const NUMBER_TYPE = "Number";
	const STRING_TYPE = "String";

	let {
		activity,
		fieldName,
		isNumberType = false,
		isStringType = false,
	}: { activity: Activity; fieldName: string; isNumberType?: boolean; isStringType?: boolean } = $props();

	let selectedDataSource: string = $state((activity.getParameter(fieldName) as string) || "");
	let dataSourceList: string[] = $state([]);

	onMount(async () => {
		try {
			const response = await fetch("wfd/api/getDataSourceList", {
				method: "GET",
			});
			let data = await response.json();
			if (!data?.length) {
				return;
			}
			if (isNumberType) {
				data = data.filter((i: Record<string, any>) => i.Type === NUMBER_TYPE);
			} else if (isStringType) {
				data = data.filter((i: Record<string, any>) => i.Type === STRING_TYPE);
			}
			dataSourceList = data.map((i: Record<string, any>) => i.Name) || [];
		} catch (error) {
			console.error("Error fetching data:", error);
		}
	});
</script>

<Label>Select Data Source</Label>
<SelectSearch
	listData={dataSourceList}
	selectedItem={selectedDataSource}
	onSelect={(value) => {
		setTimeout(() => {
			activity.setParameter("Data Source Name", value);
		}, 200);
	}}
/>
