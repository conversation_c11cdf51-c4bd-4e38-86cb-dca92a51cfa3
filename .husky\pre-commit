#!/bin/sh

staged_files=$(git diff --cached --name-only)

echo "$staged_files" | while read -r line; do
    if echo "$line" | grep -q "^app/"; then
        echo "Running pre-commit check on app"
        cd ./app
        golangci-lint run
        break
    fi
done

echo "$staged_files" | while read -r line; do
    if echo "$line" | grep -q "^web/"; then
        echo "Running pre-commit check on web"
        cd ./web
        npm run check
        npx lint-staged
        break
    fi
done
