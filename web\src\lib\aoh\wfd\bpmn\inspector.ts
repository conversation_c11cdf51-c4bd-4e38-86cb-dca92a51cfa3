import FlowType from "$lib/aoh/wfd/components/inspector/FlowType.svelte";
import FlowOrderField from "$lib/aoh/wfd/components/inspector/FlowOrderField.svelte";
import FlowConditional from "$lib/aoh/wfd/components/inspector/FlowConditional.svelte";
import type { dia, ui } from "rappid/rappid";
import { mount } from "svelte";
import { util } from "$lib/aoh/wfd/bpmn/shapes";
import { InputValidatorError } from "$lib/aoh/wfd/bpmn/utils/error";
import NameField from "$lib/aoh/wfd/components/inspector/NameField.svelte";
import TypeField from "$lib/aoh/wfd/components/inspector/TypeField.svelte";
import InputField from "$lib/aoh/wfd/components/inspector/InputField.svelte";
import OutputField from "$lib/aoh/wfd/components/inspector/OutputField.svelte";
import EventType from "$lib/aoh/wfd/components/inspector/EventType.svelte";
import FormInputField from "$lib/aoh/wfd/components/inspector/FormInputField.svelte";
import CallActivityInputField from "$lib/aoh/wfd/components/inspector/CallActivityInputField.svelte";

const createErrorTextBlock = (root: HTMLElement | null, message: string) => {
	if (!root) {
		return;
	}
	if (root.getElementsByClassName("text-error").length > 0) {
		return;
	}
	const p = document.createElement("p");
	p.classList.add("text-error");
	p.innerText = message;
	root.appendChild(p);
};

const removeErrorTextBlock = (root: HTMLElement) => {
	const errorTexts = root.getElementsByClassName("text-error");
	const errorBoxes = root.getElementsByClassName("error");
	if (errorTexts.length > 0) {
		for (let i = 0; i < errorTexts.length; i++) {
			errorTexts.item(i)?.remove();
		}
	}

	if (errorBoxes.length > 0) {
		for (let i = 0; i < errorBoxes.length; i++) {
			errorBoxes.item(i)?.classList.remove("error");
		}
	}
};

const validateInput = (element: HTMLElement, path: string, type: string, inspector: ui.Inspector): boolean => {
	const value = inspector.getFieldValue?.(element, type);
	const cell = inspector.options.cell;
	try {
		if (cell !== undefined && util.hasInputValidate(cell)) {
			cell.validateProperty(path, value);
		}
	} catch (error) {
		if (error instanceof InputValidatorError) {
			element.classList.add("error");
			createErrorTextBlock(element.parentElement, error.message);
		}
		return false;
	}
	removeErrorTextBlock(inspector.el);
	return true;
};

const inputs: Record<string, ui.Inspector.Options["inputs"]> = {
	"wf.Switch": {
		attrs: {
			"label/text": {
				type: "name_field",
				label: "Name",
				group: "general",
				index: 1,
			},
		},
	},
	"wf.Parallel": {
		attrs: {
			"label/text": {
				type: "name_field",
				label: "Name",
				group: "general",
				index: 1,
			},
		},
	},
	"wf.CallActivity": {
		attrs: {
			"label/text": {
				type: "name_field",
				label: "Name",
				group: "general",
				index: 1,
			},
			"data/args": {
				type: "callActivity_input_field",
				group: "input",
			},
			"data/result": {
				type: "output_field",
				group: "output",
			},
		},
	},
	"wf.Form": {
		attrs: {
			"label/text": {
				type: "name_field",
				label: "Name",
				group: "general",
				index: 1,
			},
			"data/args": {
				type: "form_input_field",
				group: "input",
			},
			"data/result": {
				type: "output_field",
				group: "output",
			},
		},
	},
	"wf.Activity": {
		attrs: {
			"label/text": {
				type: "name_field",
				label: "Name",
				group: "general",
				index: 1,
			},
			"type/text": {
				type: "type_field",
				group: "general",
				label: "Type",
				index: 2,
			},
			"data/args": {
				type: "input_field",
				group: "input",
				when: {
					ne: { "attrs/type/text": undefined },
				},
			},
			"data/result": {
				type: "output_field",
				group: "output",
			},
		},
	},
	"wf.End": {
		attrs: {
			"label/text": {
				type: "name_field",
				label: "Name",
				group: "general",
				index: 1,
			},
		},
	},
	"wf.Terminate": {
		attrs: {
			"label/text": {
				type: "name_field",
				label: "Name",
				group: "general",
				index: 1,
			},
		},
	},
	"wf.Start": {
		attrs: {
			"label/text": {
				type: "name_field",
				label: "Name",
				group: "general",
				index: 1,
			},
		},
	},
	"wf.Event": {
		attrs: {
			"label/text": {
				type: "name_field",
				label: "Name",
				group: "general",
				index: 1,
			},
			"border/borderStyle": {
				type: "event_type",
				group: "general",
				index: 2,
			},
			"type/text": {
				type: "type_field",
				group: "general",
				label: "Type",
				index: 3,
			},
			"data/args": {
				type: "input_field",
				group: "input",
				when: {
					ne: { "attrs/type/text": undefined },
				},
			},
			"data/result": {
				type: "output_field",
				group: "output",
			},
		},
	},
	"wf.Group": {
		attrs: {
			"label/text": {
				type: "name_field",
				label: "Name",
				group: "general",
				index: 1,
			},
		},
	},
	"wf.Flow": {
		attrs: {
			"line/flowType": {
				type: "flow_type",
				label: "Type",
				group: "Flow",
				index: 1,
			},
			"condition/order": {
				type: "flow_order",
				group: "Flow",
				when: {
					eq: { "attrs/line/flowType": "conditional" },
				},
			},
			"condition/tab": {
				type: "flow_condition",
				group: "Flow",
				resultPath: "data/result",
				when: {
					eq: { "attrs/line/flowType": "conditional" },
				},
			},
		},
	},
};

const renderFieldContent = (
	opts: Record<string, string>,
	path: string,
	_value: unknown,
	inspector: ui.Inspector
): HTMLElement => {
	const root = document.createElement("div");
	switch (opts.type) {
		case "name_field":
			mount(NameField, {
				target: root,
				props: {
					inspector,
					path,
				},
			});
			break;
		case "type_field":
			mount(TypeField, {
				target: root,
				props: {
					inspector,
				},
			});
			break;
		case "output_field":
			mount(OutputField, {
				target: root,
				props: {
					inspector,
				},
			});
			break;
		case "input_field":
			mount(InputField, {
				target: root,
				props: {
					inspector,
				},
			});
			break;
		case "event_type":
			mount(EventType, {
				target: root,
				props: {
					inspector,
					path,
				},
			});
			break;
		case "flow_order":
			mount(FlowOrderField, {
				target: root,
				props: {
					inspector,
				},
			});
			break;
		case "flow_type":
			mount(FlowType, {
				target: root,
				props: {
					inspector,
					path,
				},
			});
			break;
		case "flow_condition":
			mount(FlowConditional, {
				target: root,
				props: {
					inspector,
					path,
					resultPath: opts.resultPath,
				},
			});
			break;
		case "form_input_field":
			mount(FormInputField, {
				target: root,
				props: {
					inspector,
				},
			});
			break;
		case "callActivity_input_field":
			mount(CallActivityInputField, {
				target: root,
				props: {
					inspector,
				},
			});
			break;
	}
	return root;
};

export const inspectorConfig = {
	opts(cell: dia.Cell): ui.Inspector.Options {
		const type: string = cell.get("type");

		return {
			cell: cell,
			inputs: inputs[type],
			live: true,
			groups: {
				general: { label: type.slice(3), index: 1 },
				input: { label: "Parameters", index: 2 },
				output: { label: "Output", index: 3 },
				options: {
					index: 4,
					when: { equal: { "attrs/data/useDetailedActivityOptions": true } },
				},
				appearance: { index: 5 },
				defaults: { index: 6 },
			},
			validateInput,
			renderFieldContent,
		};
	},
	events: [
		{
			name: "change:attrs/custom/label/preset",
			callback: (inspector: ui.Inspector) => {
				return (value: string, el: HTMLElement) => {
					const refXEl: HTMLInputElement = <HTMLInputElement>(
						el.parentElement?.parentElement?.querySelector('[data-attribute="attrs/label/ref-x"]')
					);
					const refYEl: HTMLInputElement = <HTMLInputElement>(
						el.parentElement?.parentElement?.querySelector('[data-attribute="attrs/label/ref-y"]')
					);

					const refs = value.split(";");
					refXEl.value = refs[0];
					refYEl.value = refs[1];

					inspector.updateCell();
				};
			},
		},
	],
};
