<script lang="ts">
	import { type Activity } from "$lib/aoh/wfd/activitySDK/api";
	import Input from "../../components/ui/input/input.svelte";
	import Label from "../../components/ui/label/label.svelte";
	import SelectSearch from "../../components/ui/select/select-search.svelte";
	import { activityStore } from "../../stores/activities";
	import { eventStore } from "../../stores/events";
	import { workflowStore, getWorkflowById } from "../../stores/workflows";
	import { workflowData } from "../../stores/workflowData";

	let { activity }: { activity: Activity } = $props();
	let days = $state(activity.getParameter("Days") as number);
	let hours = $state(activity.getParameter("Hours") as number);
	let minutes = $state(activity.getParameter("Minutes") as number);
	let seconds = $state(activity.getParameter("Seconds") as number);

	function preventInvalidChars(e: KeyboardEvent) {
		const invalidKeys = ["e", "E", "+", "-", "."];
		if (invalidKeys.includes(e.key)) {
			e.preventDefault();
		}
	}
	const workflows: any = $workflowStore;

	// Get the currently selected workflow
	let selectedWorkflow = $derived($workflowData);
	let fullWorkflow = $derived(selectedWorkflow.id ? getWorkflowById(selectedWorkflow.id) : null);

	// You can now access:
	// selectedWorkflow.name - selected workflow name
	// selectedWorkflow.id - selected workflow ID
	// selectedWorkflow.occ_lock - optimistic concurrency control lock
	// fullWorkflow?.workflow_json - complete workflow JSON
	// fullWorkflow?.designer_json - designer JSON
	let currentStates = $derived(fullWorkflow?.workflow_json?.states);
	let currentStateNames = $derived(currentStates?.map((s: any) => s.name));
	$effect(() => {
		console.log({ currentStateNames });
	});
	const saveParam = (key: string, value: number) => {
		if (value < 0 || !Number.isInteger(value)) {
			value = Math.max(0, Math.floor(value));
		}
		activity.setParameter(key, value);
	};
</script>

<div class="flex space-x-2">
	<Label>Select Activity</Label>
	<SelectSearch
		listData={["s", "d"]}
		selectedItem={activity.getParameter("Data Source Name") as string}
		onSelect={(value) => {
			setTimeout(() => {
				activity.setParameter("Data Source Name", value);
			}, 200);
		}}
	/>
</div>
