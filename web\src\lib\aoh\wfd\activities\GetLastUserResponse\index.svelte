<script lang="ts">
	import { type Activity } from "$lib/aoh/wfd/activitySDK/api";
	import { dia } from "rappid/rappid";
	import Label from "../../components/ui/label/label.svelte";
	import SelectSearch from "../../components/ui/select/select-search.svelte";
	import Tooltip from "$lib/components/ui/tooltip/tooltip.svelte";

	let { activity, graph }: { activity: Activity; graph?: dia.Graph } = $props();

	// Get ALL current elements from the live graph (including unsaved ones)
	let liveGraphElements = $derived(graph ? graph.getElements() : []);

	let liveStateNames = $derived(
		liveGraphElements
			.filter(
				(el) =>
					el.attr("type/text") === "SendTeamNotiMOHIncidentReport" ||
					el.attr("type/text") === "SendTeamChannelNotiMOHIncidentReport"
			)
			.map((el) => el.attr("label/text") || `Unnamed_${el.id}`)
	);

	// Validation state
	let errorMessage = $state("");
	let selectedActivity = $state((activity.getParameter("ActivityName") as string) || "");

	// Validation function
	function validateTargetActivity(value: string) {
		if (!value || value.trim() === "" || value === "No states available") {
			errorMessage = "Target Activity is required";
			return false;
		}
		errorMessage = "";
		return true;
	}

	// Handle selection with validation
	function handleActivitySelect(value: string) {
		selectedActivity = value;
		if (validateTargetActivity(value)) {
			activity.setParameter("ActivityName", value);
		}
	}

	// Initial validation on component load
	$effect(() => {
		validateTargetActivity(selectedActivity);
	});
</script>

<div class="flex flex-col">
	<Label>Select Target Activity <span class="text-red-500">*</span></Label>
	<div class="flex">
		<SelectSearch
			listData={liveStateNames || ["No states available"]}
			selectedItem={selectedActivity}
			onSelect={handleActivitySelect}
		/>
		<Tooltip message="The target activity is the activity that you want to check the last responses." />
	</div>
	{#if errorMessage}
		<div class="mt-1 text-red-500 text-sm">{errorMessage}</div>
	{/if}
</div>
