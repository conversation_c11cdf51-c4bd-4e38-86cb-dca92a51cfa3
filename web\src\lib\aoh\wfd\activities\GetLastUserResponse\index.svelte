<script lang="ts">
	import { type Activity } from "$lib/aoh/wfd/activitySDK/api";
	import Input from "../../components/ui/input/input.svelte";
	import Label from "../../components/ui/label/label.svelte";
	import SelectSearch from "../../components/ui/select/select-search.svelte";
	import { activityStore } from "../../stores/activities";
	import { eventStore } from "../../stores/events";
	import { workflowStore } from "../../stores/workflows";

	let { activity }: { activity: Activity } = $props();
	let days = $state(activity.getParameter("Days") as number);
	let hours = $state(activity.getParameter("Hours") as number);
	let minutes = $state(activity.getParameter("Minutes") as number);
	let seconds = $state(activity.getParameter("Seconds") as number);

	function preventInvalidChars(e: KeyboardEvent) {
		const invalidKeys = ["e", "E", "+", "-", "."];
		if (invalidKeys.includes(e.key)) {
			e.preventDefault();
		}
	}
	const workflows: any = $workflowStore;
	const currentWorkflow = workflows.find((wf: any) => wf.id === $workflowStore.selectedWorkflowId);
	const states = currentWorkflow?.workflow_json?.states ?? [];
	$effect(() => {
		console.log({ states, currentWorkflow, activity, workflows });
	});
	const saveParam = (key: string, value: number) => {
		if (value < 0 || !Number.isInteger(value)) {
			value = Math.max(0, Math.floor(value));
		}
		activity.setParameter(key, value);
	};
</script>

<div class="flex space-x-2">
	<Label>Select Activity</Label>
	<SelectSearch
		listData={["s", "d"]}
		selectedItem={activity.getParameter("Data Source Name") as string}
		onSelect={(value) => {
			setTimeout(() => {
				activity.setParameter("Data Source Name", value);
			}, 200);
		}}
	/>
</div>
