<script lang="ts">
	import { type Activity } from "$lib/aoh/wfd/activitySDK/api";
	import { dia } from "rappid/rappid";
	import Label from "../../components/ui/label/label.svelte";
	import SelectSearch from "../../components/ui/select/select-search.svelte";
	import Tooltip from "$lib/components/ui/tooltip/tooltip.svelte";

	let { activity, graph }: { activity: Activity; graph?: dia.Graph } = $props();

	// Get ALL current elements from the live graph (including unsaved ones)
	let liveGraphElements = $derived(graph ? graph.getElements() : []);

	let liveStateNames = $derived(
		liveGraphElements
			.filter(
				(el) =>
					el.attr("type/text") === "SendTeamNotiMOHIncidentReport" ||
					el.attr("type/text") === "SendTeamChannelNotiMOHIncidentReport"
			)
			.map((el) => el.attr("label/text") || `Unnamed_${el.id}`)
	);
</script>

<div class="flex flex-col">
	<Label>Select Target Activity</Label>
	<Tooltip message="hello" />
	<div class="flex">
		<SelectSearch
			listData={liveStateNames || ["No states available"]}
			selectedItem={activity.getParameter("ActivityName") as string}
			onSelect={(value) => {
				activity.setParameter("ActivityName", value);
			}}
		/>
		<Tooltip message="The target activity is the activity that you want to check the last responses." />
	</div>
</div>
