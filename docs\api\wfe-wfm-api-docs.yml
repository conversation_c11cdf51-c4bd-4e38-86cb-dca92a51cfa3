openapi: 3.0.0
info:
  title: AOH - WFE
  version: 1.0.0
servers:
  - url: http://{{wfm_endpoint}}
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
security:
  - bearerAuth: []
paths:
  /v1/workflow_template/save:
    put:
      tags:
        - default
      summary: Workflow Template Save
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                name: testWorkflow
                workflow_json:
                  variables:
                    Activity_JSON: '{"key": "abc"}'
                    Activity1_String: '=Result_Activity.key'
                  specVersion: '2.0'
                  start: Activity
                  states:
                    - name: Activity
                      next: Activity1
                      activity:
                        id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                        type: SampleActivityJson
                        arguments:
                          - Activity_JSON
                        boundaryEvents: []
                        result: Result_Activity
                        options:
                          startToCloseTimeout: 300
                    - name: Activity1
                      next: End
                      activity:
                        id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                        type: SampleActivityString
                        arguments:
                          - Activity1_String
                        boundaryEvents: []
                        result: Result_Activity1
                        options:
                          startToCloseTimeout: 300
                    - name: End
                      end:
                        id: 9e4b803e-7f38-4550-917b-82a5d7d00bbb
                        terminate: false
                designer_json:
                  type: bpmn
                  cells:
                    - type: wf.Start
                      size:
                        width: 40
                        height: 40
                      position:
                        x: 190
                        'y': 230
                      angle: 0
                      z: 1
                      id: a6fc6b36-afe3-4d39-949b-eefd50233eab
                      attrs: {}
                    - type: wf.Activity
                      size:
                        width: 100
                        height: 80
                      markup:
                        - tagName: rect
                          selector: background
                        - tagName: image
                          selector: icon
                        - tagName: path
                          selector: border
                        - tagName: text
                          selector: label
                        - tagName: g
                          selector: markers
                        - tagName: text
                          selector: type
                          attributes:
                            fill: hsl(var(--primary))
                      position:
                        x: 300
                        'y': 210
                      angle: 0
                      z: 2
                      id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                      subscribers: {}
                      attrs:
                        label:
                          text: Activity
                        type:
                          text: SampleActivityJson
                        data:
                          result: Result_Activity
                          resultType: object
                          taskResult:
                            object: {}
                          typeOptions:
                            JSON: '{"key": "abc"}'
                    - type: wf.Flow
                      source:
                        id: a6fc6b36-afe3-4d39-949b-eefd50233eab
                      target:
                        id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                      z: 3
                      id: dfaf8a5e-9e5d-45d1-a09a-698438b7ef5e
                      labels: []
                      attrs: {}
                    - type: wf.Activity
                      size:
                        width: 100
                        height: 80
                      markup:
                        - tagName: rect
                          selector: background
                        - tagName: image
                          selector: icon
                        - tagName: path
                          selector: border
                        - tagName: text
                          selector: label
                        - tagName: g
                          selector: markers
                        - tagName: text
                          selector: type
                          attributes:
                            fill: hsl(var(--primary))
                      position:
                        x: 450
                        'y': 210
                      angle: 0
                      z: 6
                      id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                      subscribers: {}
                      attrs:
                        label:
                          text: Activity1
                        type:
                          text: SampleActivityString
                        data:
                          result: Result_Activity1
                          resultType: string
                          taskResult: string
                          typeOptions:
                            String: '=Result_Activity.key'
                            StringTmp: Result_Activity.key
                            StringValidate:
                              message: valid expression
                              sent_at: '2024-12-23T09:26:08Z'
                          activityVariables:
                            String: true
                    - type: wf.Flow
                      source:
                        id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                      target:
                        id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                      z: 7
                      id: 49ef04eb-56b4-467c-bd62-8c63e89b3c80
                      labels: []
                      attrs: {}
                    - type: wf.End
                      size:
                        width: 40
                        height: 40
                      position:
                        x: 610
                        'y': 230
                      angle: 0
                      z: 10
                      id: 9e4b803e-7f38-4550-917b-82a5d7d00bbb
                      attrs: {}
                    - type: wf.Flow
                      source:
                        id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                      target:
                        id: 9e4b803e-7f38-4550-917b-82a5d7d00bbb
                      z: 11
                      id: 330878ee-846b-4a71-a320-d854e92b7854
                      labels: []
                      attrs: {}
                  name: testWorkflow
      responses:
        '200':
          description: OK
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
            Date:
              schema:
                type: string
                example: Thu, 16 Jan 2025 04:43:18 GMT
            Transfer-Encoding:
              schema:
                type: string
                example: chunked
          content:
            application/json:
              schema:
                type: object
              example:
                data:
                  id: 910d11d0-6617-45c9-9f2f-00ce7f5b9ed3
                  name: testWorkflow
                  workflow_json:
                    start: Activity
                    states:
                      - name: Activity
                        next: Activity1
                        activity:
                          id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                          type: SampleActivityJson
                          result: Result_Activity
                          options:
                            startToCloseTimeout: 300
                          arguments:
                            - Activity_JSON
                          boundaryEvents: []
                      - name: Activity1
                        next: End
                        activity:
                          id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                          type: SampleActivityString
                          result: Result_Activity1
                          options:
                            startToCloseTimeout: 300
                          arguments:
                            - Activity1_String
                          boundaryEvents: []
                      - end:
                          id: 9e4b803e-7f38-4550-917b-82a5d7d00bbb
                          terminate: false
                        name: End
                    variables:
                      Activity_JSON: '{"key": "abc"}'
                      Activity1_String: '=Result_Activity.key'
                    specVersion: '2.0'
                  designer_json:
                    name: testWorkflow
                    type: bpmn
                    cells:
                      - z: 1
                        id: a6fc6b36-afe3-4d39-949b-eefd50233eab
                        size:
                          width: 40
                          height: 40
                        type: wf.Start
                        angle: 0
                        attrs: {}
                        position:
                          x: 190
                          'y': 230
                      - z: 2
                        id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                        size:
                          width: 100
                          height: 80
                        type: wf.Activity
                        angle: 0
                        attrs:
                          data:
                            result: Result_Activity
                            resultType: object
                            taskResult:
                              object: {}
                            typeOptions:
                              JSON: '{"key": "abc"}'
                          type:
                            text: SampleActivityJson
                          label:
                            text: Activity
                        markup:
                          - tagName: rect
                            selector: background
                          - tagName: image
                            selector: icon
                          - tagName: path
                            selector: border
                          - tagName: text
                            selector: label
                          - tagName: g
                            selector: markers
                          - tagName: text
                            selector: type
                            attributes:
                              fill: hsl(var(--primary))
                        position:
                          x: 300
                          'y': 210
                        subscribers: {}
                      - z: 3
                        id: dfaf8a5e-9e5d-45d1-a09a-698438b7ef5e
                        type: wf.Flow
                        attrs: {}
                        labels: []
                        source:
                          id: a6fc6b36-afe3-4d39-949b-eefd50233eab
                        target:
                          id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                      - z: 6
                        id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                        size:
                          width: 100
                          height: 80
                        type: wf.Activity
                        angle: 0
                        attrs:
                          data:
                            result: Result_Activity1
                            resultType: string
                            taskResult: string
                            typeOptions:
                              String: '=Result_Activity.key'
                              StringTmp: Result_Activity.key
                              StringValidate:
                                message: valid expression
                                sent_at: '2024-12-23T09:26:08Z'
                            activityVariables:
                              String: true
                          type:
                            text: SampleActivityString
                          label:
                            text: Activity1
                        markup:
                          - tagName: rect
                            selector: background
                          - tagName: image
                            selector: icon
                          - tagName: path
                            selector: border
                          - tagName: text
                            selector: label
                          - tagName: g
                            selector: markers
                          - tagName: text
                            selector: type
                            attributes:
                              fill: hsl(var(--primary))
                        position:
                          x: 450
                          'y': 210
                        subscribers: {}
                      - z: 7
                        id: 49ef04eb-56b4-467c-bd62-8c63e89b3c80
                        type: wf.Flow
                        attrs: {}
                        labels: []
                        source:
                          id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                        target:
                          id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                      - z: 10
                        id: 9e4b803e-7f38-4550-917b-82a5d7d00bbb
                        size:
                          width: 40
                          height: 40
                        type: wf.End
                        angle: 0
                        attrs: {}
                        position:
                          x: 610
                          'y': 230
                      - z: 11
                        id: 330878ee-846b-4a71-a320-d854e92b7854
                        type: wf.Flow
                        attrs: {}
                        labels: []
                        source:
                          id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                        target:
                          id: 9e4b803e-7f38-4550-917b-82a5d7d00bbb
                  editable: true
                  created_at: '2024-12-23T09:26:17.229735Z'
                  created_by: f67cb8f5-0645-444d-a4bd-c61aaf1b2db0
                  updated_at: '2025-01-16T04:43:18.983275Z'
                  updated_by: f67cb8f5-0645-444d-a4bd-c61aaf1b2db0
                  tenant_id: 430d2014-6e57-4e8a-ad75-484aa276a5cc
                  occ_lock: 1
                sent_at: '2025-01-16T04:43:18Z'
  /v1/workflow_template/publish:
    put:
      tags:
        - default
      summary: Workflow Template Publish
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                name: testWorkflow
                workflow_json:
                  variables:
                    Activity_JSON: '{"key": "abc"}'
                    Activity1_String: '=Result_Activity.key'
                  specVersion: '2.0'
                  start: Activity
                  states:
                    - name: Activity
                      next: Activity1
                      activity:
                        id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                        type: SampleActivityJson
                        arguments:
                          - Activity_JSON
                        boundaryEvents: []
                        result: Result_Activity
                        options:
                          startToCloseTimeout: 300
                    - name: Activity1
                      next: End
                      activity:
                        id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                        type: SampleActivityString
                        arguments:
                          - Activity1_String
                        boundaryEvents: []
                        result: Result_Activity1
                        options:
                          startToCloseTimeout: 300
                    - name: End
                      end:
                        id: 9e4b803e-7f38-4550-917b-82a5d7d00bbb
                        terminate: false
                designer_json:
                  type: bpmn
                  cells:
                    - type: wf.Start
                      size:
                        width: 40
                        height: 40
                      position:
                        x: 190
                        'y': 230
                      angle: 0
                      z: 1
                      id: a6fc6b36-afe3-4d39-949b-eefd50233eab
                      attrs: {}
                    - type: wf.Activity
                      size:
                        width: 100
                        height: 80
                      markup:
                        - tagName: rect
                          selector: background
                        - tagName: image
                          selector: icon
                        - tagName: path
                          selector: border
                        - tagName: text
                          selector: label
                        - tagName: g
                          selector: markers
                        - tagName: text
                          selector: type
                          attributes:
                            fill: hsl(var(--primary))
                      position:
                        x: 300
                        'y': 210
                      angle: 0
                      z: 2
                      id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                      subscribers: {}
                      attrs:
                        label:
                          text: Activity
                        type:
                          text: SampleActivityJson
                        data:
                          result: Result_Activity
                          resultType: object
                          taskResult:
                            object: {}
                          typeOptions:
                            JSON: '{"key": "abc"}'
                    - type: wf.Flow
                      source:
                        id: a6fc6b36-afe3-4d39-949b-eefd50233eab
                      target:
                        id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                      z: 3
                      id: dfaf8a5e-9e5d-45d1-a09a-698438b7ef5e
                      labels: []
                      attrs: {}
                    - type: wf.Activity
                      size:
                        width: 100
                        height: 80
                      markup:
                        - tagName: rect
                          selector: background
                        - tagName: image
                          selector: icon
                        - tagName: path
                          selector: border
                        - tagName: text
                          selector: label
                        - tagName: g
                          selector: markers
                        - tagName: text
                          selector: type
                          attributes:
                            fill: hsl(var(--primary))
                      position:
                        x: 450
                        'y': 210
                      angle: 0
                      z: 6
                      id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                      subscribers: {}
                      attrs:
                        label:
                          text: Activity1
                        type:
                          text: SampleActivityString
                        data:
                          result: Result_Activity1
                          resultType: string
                          taskResult: string
                          typeOptions:
                            String: '=Result_Activity.key'
                            StringTmp: Result_Activity.key
                            StringValidate:
                              message: valid expression
                              sent_at: '2024-12-23T09:26:08Z'
                          activityVariables:
                            String: true
                    - type: wf.Flow
                      source:
                        id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                      target:
                        id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                      z: 7
                      id: 49ef04eb-56b4-467c-bd62-8c63e89b3c80
                      labels: []
                      attrs: {}
                    - type: wf.End
                      size:
                        width: 40
                        height: 40
                      position:
                        x: 610
                        'y': 230
                      angle: 0
                      z: 10
                      id: 9e4b803e-7f38-4550-917b-82a5d7d00bbb
                      attrs: {}
                    - type: wf.Flow
                      source:
                        id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                      target:
                        id: 9e4b803e-7f38-4550-917b-82a5d7d00bbb
                      z: 11
                      id: 330878ee-846b-4a71-a320-d854e92b7854
                      labels: []
                      attrs: {}
                  name: testWorkflow
      responses:
        '200':
          description: OK
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
            Date:
              schema:
                type: string
                example: Thu, 16 Jan 2025 04:45:11 GMT
            Transfer-Encoding:
              schema:
                type: string
                example: chunked
          content:
            application/json:
              schema:
                type: object
              example:
                data:
                  id: 910d11d0-6617-45c9-9f2f-00ce7f5b9ed3
                  name: testWorkflow
                  workflow_json:
                    start: Activity
                    states:
                      - name: Activity
                        next: Activity1
                        activity:
                          id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                          type: SampleActivityJson
                          result: Result_Activity
                          options:
                            startToCloseTimeout: 300
                          arguments:
                            - Activity_JSON
                          boundaryEvents: []
                      - name: Activity1
                        next: End
                        activity:
                          id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                          type: SampleActivityString
                          result: Result_Activity1
                          options:
                            startToCloseTimeout: 300
                          arguments:
                            - Activity1_String
                          boundaryEvents: []
                      - end:
                          id: 9e4b803e-7f38-4550-917b-82a5d7d00bbb
                          terminate: false
                        name: End
                    variables:
                      Activity_JSON: '{"key": "abc"}'
                      Activity1_String: '=Result_Activity.key'
                    specVersion: '2.0'
                  designer_json:
                    name: testWorkflow
                    type: bpmn
                    cells:
                      - z: 1
                        id: a6fc6b36-afe3-4d39-949b-eefd50233eab
                        size:
                          width: 40
                          height: 40
                        type: wf.Start
                        angle: 0
                        attrs: {}
                        position:
                          x: 190
                          'y': 230
                      - z: 2
                        id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                        size:
                          width: 100
                          height: 80
                        type: wf.Activity
                        angle: 0
                        attrs:
                          data:
                            result: Result_Activity
                            resultType: object
                            taskResult:
                              object: {}
                            typeOptions:
                              JSON: '{"key": "abc"}'
                          type:
                            text: SampleActivityJson
                          label:
                            text: Activity
                        markup:
                          - tagName: rect
                            selector: background
                          - tagName: image
                            selector: icon
                          - tagName: path
                            selector: border
                          - tagName: text
                            selector: label
                          - tagName: g
                            selector: markers
                          - tagName: text
                            selector: type
                            attributes:
                              fill: hsl(var(--primary))
                        position:
                          x: 300
                          'y': 210
                        subscribers: {}
                      - z: 3
                        id: dfaf8a5e-9e5d-45d1-a09a-698438b7ef5e
                        type: wf.Flow
                        attrs: {}
                        labels: []
                        source:
                          id: a6fc6b36-afe3-4d39-949b-eefd50233eab
                        target:
                          id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                      - z: 6
                        id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                        size:
                          width: 100
                          height: 80
                        type: wf.Activity
                        angle: 0
                        attrs:
                          data:
                            result: Result_Activity1
                            resultType: string
                            taskResult: string
                            typeOptions:
                              String: '=Result_Activity.key'
                              StringTmp: Result_Activity.key
                              StringValidate:
                                message: valid expression
                                sent_at: '2024-12-23T09:26:08Z'
                            activityVariables:
                              String: true
                          type:
                            text: SampleActivityString
                          label:
                            text: Activity1
                        markup:
                          - tagName: rect
                            selector: background
                          - tagName: image
                            selector: icon
                          - tagName: path
                            selector: border
                          - tagName: text
                            selector: label
                          - tagName: g
                            selector: markers
                          - tagName: text
                            selector: type
                            attributes:
                              fill: hsl(var(--primary))
                        position:
                          x: 450
                          'y': 210
                        subscribers: {}
                      - z: 7
                        id: 49ef04eb-56b4-467c-bd62-8c63e89b3c80
                        type: wf.Flow
                        attrs: {}
                        labels: []
                        source:
                          id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                        target:
                          id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                      - z: 10
                        id: 9e4b803e-7f38-4550-917b-82a5d7d00bbb
                        size:
                          width: 40
                          height: 40
                        type: wf.End
                        angle: 0
                        attrs: {}
                        position:
                          x: 610
                          'y': 230
                      - z: 11
                        id: 330878ee-846b-4a71-a320-d854e92b7854
                        type: wf.Flow
                        attrs: {}
                        labels: []
                        source:
                          id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                        target:
                          id: 9e4b803e-7f38-4550-917b-82a5d7d00bbb
                  editable: false
                  created_at: '2024-12-23T09:26:17.229735Z'
                  created_by: f67cb8f5-0645-444d-a4bd-c61aaf1b2db0
                  updated_at: '2025-01-16T04:45:11.754052Z'
                  updated_by: f67cb8f5-0645-444d-a4bd-c61aaf1b2db0
                  tenant_id: 430d2014-6e57-4e8a-ad75-484aa276a5cc
                  occ_lock: 2
                sent_at: '2025-01-16T04:45:11Z'
  /v1/workflow_template/{template_id}:
    get:
      tags:
        - default
      summary: Workflow Template Get
      parameters:
        - name: template_id
          in: path
          schema:
            type: string
          required: true
          example: 910d11d0-6617-45c9-9f2f-00ce7f5b9ed3
      responses:
        '200':
          description: OK
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
            Date:
              schema:
                type: string
                example: Thu, 16 Jan 2025 04:45:42 GMT
            Transfer-Encoding:
              schema:
                type: string
                example: chunked
          content:
            application/json:
              schema:
                type: object
              example:
                data:
                  id: 910d11d0-6617-45c9-9f2f-00ce7f5b9ed3
                  name: testWorkflow
                  workflow_json:
                    start: Activity
                    states:
                      - name: Activity
                        next: Activity1
                        activity:
                          id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                          type: SampleActivityJson
                          result: Result_Activity
                          options:
                            startToCloseTimeout: 300
                          arguments:
                            - Activity_JSON
                          boundaryEvents: []
                      - name: Activity1
                        next: End
                        activity:
                          id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                          type: SampleActivityString
                          result: Result_Activity1
                          options:
                            startToCloseTimeout: 300
                          arguments:
                            - Activity1_String
                          boundaryEvents: []
                      - end:
                          id: 9e4b803e-7f38-4550-917b-82a5d7d00bbb
                          terminate: false
                        name: End
                    variables:
                      Activity_JSON: '{"key": "abc"}'
                      Activity1_String: '=Result_Activity.key'
                    specVersion: '2.0'
                  designer_json:
                    name: testWorkflow
                    type: bpmn
                    cells:
                      - z: 1
                        id: a6fc6b36-afe3-4d39-949b-eefd50233eab
                        size:
                          width: 40
                          height: 40
                        type: wf.Start
                        angle: 0
                        attrs: {}
                        position:
                          x: 190
                          'y': 230
                      - z: 2
                        id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                        size:
                          width: 100
                          height: 80
                        type: wf.Activity
                        angle: 0
                        attrs:
                          data:
                            result: Result_Activity
                            resultType: object
                            taskResult:
                              object: {}
                            typeOptions:
                              JSON: '{"key": "abc"}'
                          type:
                            text: SampleActivityJson
                          label:
                            text: Activity
                        markup:
                          - tagName: rect
                            selector: background
                          - tagName: image
                            selector: icon
                          - tagName: path
                            selector: border
                          - tagName: text
                            selector: label
                          - tagName: g
                            selector: markers
                          - tagName: text
                            selector: type
                            attributes:
                              fill: hsl(var(--primary))
                        position:
                          x: 300
                          'y': 210
                        subscribers: {}
                      - z: 3
                        id: dfaf8a5e-9e5d-45d1-a09a-698438b7ef5e
                        type: wf.Flow
                        attrs: {}
                        labels: []
                        source:
                          id: a6fc6b36-afe3-4d39-949b-eefd50233eab
                        target:
                          id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                      - z: 6
                        id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                        size:
                          width: 100
                          height: 80
                        type: wf.Activity
                        angle: 0
                        attrs:
                          data:
                            result: Result_Activity1
                            resultType: string
                            taskResult: string
                            typeOptions:
                              String: '=Result_Activity.key'
                              StringTmp: Result_Activity.key
                              StringValidate:
                                message: valid expression
                                sent_at: '2024-12-23T09:26:08Z'
                            activityVariables:
                              String: true
                          type:
                            text: SampleActivityString
                          label:
                            text: Activity1
                        markup:
                          - tagName: rect
                            selector: background
                          - tagName: image
                            selector: icon
                          - tagName: path
                            selector: border
                          - tagName: text
                            selector: label
                          - tagName: g
                            selector: markers
                          - tagName: text
                            selector: type
                            attributes:
                              fill: hsl(var(--primary))
                        position:
                          x: 450
                          'y': 210
                        subscribers: {}
                      - z: 7
                        id: 49ef04eb-56b4-467c-bd62-8c63e89b3c80
                        type: wf.Flow
                        attrs: {}
                        labels: []
                        source:
                          id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                        target:
                          id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                      - z: 10
                        id: 9e4b803e-7f38-4550-917b-82a5d7d00bbb
                        size:
                          width: 40
                          height: 40
                        type: wf.End
                        angle: 0
                        attrs: {}
                        position:
                          x: 610
                          'y': 230
                      - z: 11
                        id: 330878ee-846b-4a71-a320-d854e92b7854
                        type: wf.Flow
                        attrs: {}
                        labels: []
                        source:
                          id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                        target:
                          id: 9e4b803e-7f38-4550-917b-82a5d7d00bbb
                  editable: false
                  created_at: '2024-12-23T09:26:17.229735Z'
                  created_by: f67cb8f5-0645-444d-a4bd-c61aaf1b2db0
                  updated_at: '2025-01-16T04:45:11.754052Z'
                  updated_by: f67cb8f5-0645-444d-a4bd-c61aaf1b2db0
                  tenant_id: 430d2014-6e57-4e8a-ad75-484aa276a5cc
                  occ_lock: 2
                sent_at: '2025-01-16T04:45:42Z'
    delete:
      tags:
        - default
      summary: Workflow Template Delete
      parameters:
        - name: template_id
          in: path
          schema:
            type: string
          required: true
          example: af2c5220-cccd-468e-a7d7-d0885a42a8aa
      responses:
        '200':
          description: OK
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
            Date:
              schema:
                type: string
                example: Thu, 16 Jan 2025 04:47:31 GMT
            Content-Length:
              schema:
                type: integer
                example: '35'
          content:
            application/json:
              schema:
                type: object
              example:
                sent_at: '2025-01-16T04:47:31Z'
  /v1/workflow_template:
    get:
      tags:
        - default
      summary: Workflow Template List
      parameters:
        - name: page
          in: query
          schema:
            type: integer
          example: '1'
        - name: size
          in: query
          schema:
            type: integer
          example: '3'
        - name: sort
          in: query
          schema:
            type: string
          example: created_at,desc
      responses:
        '200':
          description: OK
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
            Date:
              schema:
                type: string
                example: Thu, 16 Jan 2025 04:49:56 GMT
            Transfer-Encoding:
              schema:
                type: string
                example: chunked
          content:
            application/json:
              schema:
                type: object
              example:
                data:
                  - id: 6d703ef6-dcb1-4f3d-99ec-290840bc4f74
                    name: testWorkflow
                    workflow_json:
                      start: Activity
                      states:
                        - name: Activity
                          next: Activity1
                          activity:
                            id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                            type: SampleActivityJson
                            result: Result_Activity
                            options:
                              startToCloseTimeout: 300
                            arguments:
                              - Activity_JSON
                            boundaryEvents: []
                        - name: Activity1
                          next: End
                          activity:
                            id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                            type: SampleActivityString
                            result: Result_Activity1
                            options:
                              startToCloseTimeout: 300
                            arguments:
                              - Activity1_String
                            boundaryEvents: []
                        - end:
                            id: 9e4b803e-7f38-4550-917b-82a5d7d00bbb
                            terminate: false
                          name: End
                      variables:
                        Activity_JSON: '{"key": "abc"}'
                        Activity1_String: '=Result_Activity.key'
                      specVersion: '2.0'
                    designer_json:
                      name: testWorkflow
                      type: bpmn
                      cells:
                        - z: 1
                          id: a6fc6b36-afe3-4d39-949b-eefd50233eab
                          size:
                            width: 40
                            height: 40
                          type: wf.Start
                          angle: 0
                          attrs: {}
                          position:
                            x: 190
                            'y': 230
                        - z: 2
                          id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                          size:
                            width: 100
                            height: 80
                          type: wf.Activity
                          angle: 0
                          attrs:
                            data:
                              result: Result_Activity
                              resultType: object
                              taskResult:
                                object: {}
                              typeOptions:
                                JSON: '{"key": "abc"}'
                            type:
                              text: SampleActivityJson
                            label:
                              text: Activity
                          markup:
                            - tagName: rect
                              selector: background
                            - tagName: image
                              selector: icon
                            - tagName: path
                              selector: border
                            - tagName: text
                              selector: label
                            - tagName: g
                              selector: markers
                            - tagName: text
                              selector: type
                              attributes:
                                fill: hsl(var(--primary))
                          position:
                            x: 300
                            'y': 210
                          subscribers: {}
                        - z: 3
                          id: dfaf8a5e-9e5d-45d1-a09a-698438b7ef5e
                          type: wf.Flow
                          attrs: {}
                          labels: []
                          source:
                            id: a6fc6b36-afe3-4d39-949b-eefd50233eab
                          target:
                            id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                        - z: 6
                          id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                          size:
                            width: 100
                            height: 80
                          type: wf.Activity
                          angle: 0
                          attrs:
                            data:
                              result: Result_Activity1
                              resultType: string
                              taskResult: string
                              typeOptions:
                                String: '=Result_Activity.key'
                                StringTmp: Result_Activity.key
                                StringValidate:
                                  message: valid expression
                                  sent_at: '2024-12-23T09:26:08Z'
                              activityVariables:
                                String: true
                            type:
                              text: SampleActivityString
                            label:
                              text: Activity1
                          markup:
                            - tagName: rect
                              selector: background
                            - tagName: image
                              selector: icon
                            - tagName: path
                              selector: border
                            - tagName: text
                              selector: label
                            - tagName: g
                              selector: markers
                            - tagName: text
                              selector: type
                              attributes:
                                fill: hsl(var(--primary))
                          position:
                            x: 450
                            'y': 210
                          subscribers: {}
                        - z: 7
                          id: 49ef04eb-56b4-467c-bd62-8c63e89b3c80
                          type: wf.Flow
                          attrs: {}
                          labels: []
                          source:
                            id: d06bfc1d-7541-44ee-97b8-b3131dd06a31
                          target:
                            id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                        - z: 10
                          id: 9e4b803e-7f38-4550-917b-82a5d7d00bbb
                          size:
                            width: 40
                            height: 40
                          type: wf.End
                          angle: 0
                          attrs: {}
                          position:
                            x: 610
                            'y': 230
                        - z: 11
                          id: 330878ee-846b-4a71-a320-d854e92b7854
                          type: wf.Flow
                          attrs: {}
                          labels: []
                          source:
                            id: 20a0ec07-f881-4bd2-bb58-a6efb474a18d
                          target:
                            id: 9e4b803e-7f38-4550-917b-82a5d7d00bbb
                    editable: true
                    created_at: '2025-01-16T04:49:26.14791Z'
                    created_by: f67cb8f5-0645-444d-a4bd-c61aaf1b2db0
                    updated_at: '2025-01-16T04:49:26.14791Z'
                    updated_by: f67cb8f5-0645-444d-a4bd-c61aaf1b2db0
                    tenant_id: 430d2014-6e57-4e8a-ad75-484aa276a5cc
                    occ_lock: 0
                  - id: be558d14-a461-43fc-a669-b5603a0efb5c
                    name: testing
                    workflow_json:
                      start: Activity
                      states:
                        - name: Activity
                          next: Terminate
                          activity:
                            id: 5a707703-9e35-4ac4-8284-c8b0e89a3ec1
                            type: Delay
                            result: Result_Activity
                            options:
                              startToCloseTimeout: 300
                            arguments:
                              - Activity_Second
                            boundaryEvents: []
                        - end:
                            id: b1a803c7-6c94-4c5a-a4b5-7ce1674701bb
                            terminate: true
                          name: Terminate
                      variables:
                        Activity_Second: 0
                      specVersion: '2.0'
                    designer_json:
                      name: testing123
                      type: bpmn
                      cells:
                        - z: 1
                          id: d451b758-39fb-4d11-bd0a-61f568ae2871
                          size:
                            width: 40
                            height: 40
                          type: wf.Start
                          angle: 0
                          attrs: {}
                          position:
                            x: 300
                            'y': 240
                        - z: 12
                          id: 5a707703-9e35-4ac4-8284-c8b0e89a3ec1
                          size:
                            width: 100
                            height: 80
                          type: wf.Activity
                          angle: 0
                          attrs:
                            data:
                              result: Result_Activity
                              resultType: 'null'
                              taskResult: null
                            type:
                              text: Delay
                            label:
                              text: Activity
                          markup:
                            - tagName: rect
                              selector: background
                            - tagName: image
                              selector: icon
                            - tagName: path
                              selector: border
                            - tagName: text
                              selector: label
                            - tagName: g
                              selector: markers
                            - tagName: text
                              selector: type
                              attributes:
                                fill: hsl(var(--primary))
                          position:
                            x: 370
                            'y': 160
                          subscribers: {}
                        - z: 13
                          id: bcebd192-f00e-4210-a429-9d2e77a5d435
                          type: wf.Flow
                          attrs: {}
                          labels: []
                          source:
                            id: d451b758-39fb-4d11-bd0a-61f568ae2871
                          target:
                            id: 5a707703-9e35-4ac4-8284-c8b0e89a3ec1
                        - z: 14
                          id: b1a803c7-6c94-4c5a-a4b5-7ce1674701bb
                          size:
                            width: 40
                            height: 40
                          type: wf.Terminate
                          angle: 0
                          attrs: {}
                          position:
                            x: 610
                            'y': 220
                        - z: 15
                          id: 4292477e-bc0b-479c-8a93-58c3412199b2
                          type: wf.Flow
                          attrs: {}
                          labels: []
                          source:
                            id: 5a707703-9e35-4ac4-8284-c8b0e89a3ec1
                          target:
                            id: b1a803c7-6c94-4c5a-a4b5-7ce1674701bb
                    editable: true
                    created_at: '2025-01-13T16:45:54.805859Z'
                    created_by: f67cb8f5-0645-444d-a4bd-c61aaf1b2db0
                    updated_at: '2025-01-13T16:57:52.519659Z'
                    updated_by: f67cb8f5-0645-444d-a4bd-c61aaf1b2db0
                    tenant_id: 430d2014-6e57-4e8a-ad75-484aa276a5cc
                    occ_lock: 5
                sent_at: '2025-01-16T04:49:56Z'
                page:
                  number: 1
                  size: 3
                  total_records: 2
                  count: 2
                  sort:
                    - created_at desc
  /v1/form_template/save:
    put:
      tags:
        - default
      summary: Form Save
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                name: AttendanceForm
                form_json:
                  components:
                    - label: Attendance List1
                      type: checklist
                      layout:
                        row: Row_1fxsjpo
                        columns: 7
                      id: Field_1p5m2f1
                      key: checklist_xmj6qe
                      valuesKey: http://hasura.dev2.ar2/api/rest/student/:incidentid
                    - action: submit
                      label: Submit
                      type: button
                      layout:
                        row: Row_1ryef5k
                        columns: 2
                      id: Field_110toor
                      properties: {}
                  type: default
                  id: Form_1r7eh7h
                  exporter:
                    name: form-js (https://demo.bpmn.io)
                    version: 1.4.0
                  schemaVersion: 12
      responses:
        '200':
          description: OK
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
            Date:
              schema:
                type: string
                example: Thu, 16 Jan 2025 04:48:04 GMT
            Content-Length:
              schema:
                type: integer
                example: '894'
          content:
            application/json:
              schema:
                type: object
              example:
                data:
                  id: 38afe467-b147-4f8e-b4d7-55f7a3712b34
                  name: AttendanceForm
                  form_json:
                    id: Form_1r7eh7h
                    type: default
                    exporter:
                      name: form-js (https://demo.bpmn.io)
                      version: 1.4.0
                    components:
                      - id: Field_1p5m2f1
                        key: checklist_xmj6qe
                        type: checklist
                        label: Attendance List1
                        layout:
                          row: Row_1fxsjpo
                          columns: 7
                        valuesKey: http://hasura.dev2.ar2/api/rest/student/:incidentid
                      - id: Field_110toor
                        type: button
                        label: Submit
                        action: submit
                        layout:
                          row: Row_1ryef5k
                          columns: 2
                        properties: {}
                    schemaVersion: 12
                  component_keys:
                    - checklist_xmj6qe
                  created_at: '2025-01-16T04:48:04.28452Z'
                  created_by: f67cb8f5-0645-444d-a4bd-c61aaf1b2db0
                  updated_at: '2025-01-16T04:48:04.28452Z'
                  updated_by: f67cb8f5-0645-444d-a4bd-c61aaf1b2db0
                  tenant_id: 430d2014-6e57-4e8a-ad75-484aa276a5cc
                  occ_lock: 0
                sent_at: '2025-01-16T04:48:04Z'
  /v1/form_template/{template_id}:
    get:
      tags:
        - default
      summary: Form Get
      parameters:
        - name: template_id
          in: path
          schema:
            type: string
          required: true
          example: 38afe467-b147-4f8e-b4d7-55f7a3712b34
      responses:
        '200':
          description: OK
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
            Date:
              schema:
                type: string
                example: Thu, 16 Jan 2025 04:48:34 GMT
            Content-Length:
              schema:
                type: integer
                example: '894'
          content:
            application/json:
              schema:
                type: object
              example:
                data:
                  id: 38afe467-b147-4f8e-b4d7-55f7a3712b34
                  name: AttendanceForm
                  form_json:
                    id: Form_1r7eh7h
                    type: default
                    exporter:
                      name: form-js (https://demo.bpmn.io)
                      version: 1.4.0
                    components:
                      - id: Field_1p5m2f1
                        key: checklist_xmj6qe
                        type: checklist
                        label: Attendance List1
                        layout:
                          row: Row_1fxsjpo
                          columns: 7
                        valuesKey: http://hasura.dev2.ar2/api/rest/student/:incidentid
                      - id: Field_110toor
                        type: button
                        label: Submit
                        action: submit
                        layout:
                          row: Row_1ryef5k
                          columns: 2
                        properties: {}
                    schemaVersion: 12
                  component_keys:
                    - checklist_xmj6qe
                  created_at: '2025-01-16T04:48:04.28452Z'
                  created_by: f67cb8f5-0645-444d-a4bd-c61aaf1b2db0
                  updated_at: '2025-01-16T04:48:04.28452Z'
                  updated_by: f67cb8f5-0645-444d-a4bd-c61aaf1b2db0
                  tenant_id: 430d2014-6e57-4e8a-ad75-484aa276a5cc
                  occ_lock: 0
                sent_at: '2025-01-16T04:48:34Z'
    delete:
      tags:
        - default
      summary: Form Delete
      parameters:
        - name: template_id
          in: path
          schema:
            type: string
          required: true
          example: 38afe467-b147-4f8e-b4d7-55f7a3712b34
      responses:
        '200':
          description: OK
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
            Date:
              schema:
                type: string
                example: Thu, 16 Jan 2025 04:50:22 GMT
            Content-Length:
              schema:
                type: integer
                example: '35'
          content:
            application/json:
              schema:
                type: object
              example:
                sent_at: '2025-01-16T04:50:22Z'
  /v1/form_template:
    get:
      tags:
        - default
      summary: Form List
      parameters:
        - name: page
          in: query
          schema:
            type: integer
          example: '1'
        - name: size
          in: query
          schema:
            type: integer
          example: '3'
        - name: sort
          in: query
          schema:
            type: string
          example: created_at,desc
      responses:
        '200':
          description: OK
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
            Date:
              schema:
                type: string
                example: Thu, 16 Jan 2025 04:48:52 GMT
            Transfer-Encoding:
              schema:
                type: string
                example: chunked
          content:
            application/json:
              schema:
                type: object
              example:
                data:
                  - id: 38afe467-b147-4f8e-b4d7-55f7a3712b34
                    name: AttendanceForm
                    form_json:
                      id: Form_1r7eh7h
                      type: default
                      exporter:
                        name: form-js (https://demo.bpmn.io)
                        version: 1.4.0
                      components:
                        - id: Field_1p5m2f1
                          key: checklist_xmj6qe
                          type: checklist
                          label: Attendance List1
                          layout:
                            row: Row_1fxsjpo
                            columns: 7
                          valuesKey: http://hasura.dev2.ar2/api/rest/student/:incidentid
                        - id: Field_110toor
                          type: button
                          label: Submit
                          action: submit
                          layout:
                            row: Row_1ryef5k
                            columns: 2
                          properties: {}
                      schemaVersion: 12
                    component_keys:
                      - checklist_xmj6qe
                    created_at: '2025-01-16T04:48:04.28452Z'
                    created_by: f67cb8f5-0645-444d-a4bd-c61aaf1b2db0
                    updated_at: '2025-01-16T04:48:04.28452Z'
                    updated_by: f67cb8f5-0645-444d-a4bd-c61aaf1b2db0
                    tenant_id: 430d2014-6e57-4e8a-ad75-484aa276a5cc
                    occ_lock: 0
                  - id: b5628c8b-14ed-4eed-b687-9779b8155a91
                    name: FormTemplate1
                    form_json:
                      id: IncidentApprovalForm
                      type: default
                      exporter:
                        name: form-js (https://demo.bpmn.io)
                        version: 1.4.0
                      components:
                        - id: Field_1qapqec
                          key: textfield_5oi7mh
                          type: textfield
                          label: Reviewer Name
                          layout:
                            row: Row_0lda7il
                            columns: 8
                          validate:
                            required: true
                        - id: Field_1qbm0ol
                          key: textfield_7avnmt
                          type: textfield
                          label: Reviewer Title
                          layout:
                            row: Row_0lda7il
                            columns: null
                          validate:
                            required: true
                        - id: Field_18mzv5y
                          key: textarea_dvllf8
                          type: textarea
                          label: Reviewer's Assessment
                          layout:
                            row: Row_0fs6p7i
                            columns: null
                          validate:
                            required: true
                          defaultValue: >-
                            [Specify any follow-up actions that need to be taken
                            based on the review.]
                        - id: Field_1a6kuma
                          key: datetime_tr7gyn
                          type: datetime
                          label: Date time
                          layout:
                            row: Row_00bhde4
                            columns: null
                          subtype: date
                          validate:
                            required: true
                          dateLabel: Date
                        - id: Field_1052nm0
                          key: radio_k92er
                          type: radio
                          label: Review Outcome
                          layout:
                            row: Row_10b4ivc
                            columns: null
                          values:
                            - label: Approved
                              value: Approved
                            - label: Rejected
                              value: Rejected
                          validate:
                            required: true
                        - id: Field_1t2i4co
                          key: textarea_7rzp3q
                          type: textarea
                          label: Comments (if any)
                          layout:
                            row: Row_15a50y4
                            columns: null
                        - id: Field_1jpxeqk
                          type: button
                          label: Submit
                          action: submit
                          layout:
                            row: Row_1way9ch
                            columns: null
                      schemaVersion: 16
                    component_keys:
                      - textfield_5oi7mh
                      - textfield_7avnmt
                      - textarea_dvllf8
                      - datetime_tr7gyn
                      - radio_k92er
                      - textarea_7rzp3q
                    created_at: '2025-01-13T08:29:29.576008Z'
                    created_by: '2023-11-09T05:28:01.753856+00:00'
                    updated_at: '2025-01-13T08:34:31.452414Z'
                    updated_by: '2023-11-09T05:28:01.753856+00:00'
                    tenant_id: 430d2014-6e57-4e8a-ad75-484aa276a5cc
                    occ_lock: 2
                sent_at: '2025-01-16T04:48:52Z'
                page:
                  number: 1
                  size: 3
                  total_records: 2
                  count: 2
                  sort:
                    - created_at desc
  /v1/service_activity:
    get:
      tags:
        - default
      summary: Service Activity List
      parameters:
        - name: page
          in: query
          schema:
            type: integer
          example: '1'
        - name: size
          in: query
          schema:
            type: integer
          example: '3'
      responses:
        '200':
          description: OK
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
            Date:
              schema:
                type: string
                example: Thu, 16 Jan 2025 04:50:38 GMT
            Content-Length:
              schema:
                type: integer
                example: '814'
          content:
            application/json:
              schema:
                type: object
              example:
                data:
                  - id: 179ad8ad-bb0f-4209-a5ed-28c90fdd9dc4
                    service_name: wfw
                    activity_type: SendEmail
                    activity_icon: none
                    activity_param:
                      - SendTo: string
                      - Subject: string
                      - Message: value
                    activity_result: null
                    timeout_in_second: 300
                  - id: 1d02ca97-8537-4d54-ada4-653991f08171
                    service_name: wfw
                    activity_type: HttpCall
                    activity_icon: none
                    activity_param:
                      - Url: string
                      - Method: string
                      - Body: {}
                    activity_result:
                      object: {}
                    timeout_in_second: 300
                  - id: e2fa732e-e86b-44b6-b974-2de1402fb957
                    service_name: wfw
                    activity_type: SummarizeChatGPT
                    activity_icon: none
                    activity_param:
                      - Prompt: string
                    activity_result: string
                    timeout_in_second: 300
                sent_at: '2025-01-16T04:50:38Z'
                page:
                  number: 1
                  size: 3
                  total_records: 8
                  count: 3
                  sort:
                    - ''
  /v1/service_event:
    get:
      tags:
        - default
      summary: Service Event List
      parameters:
        - name: page
          in: query
          schema:
            type: integer
          example: '1'
        - name: size
          in: query
          schema:
            type: integer
          example: '3'
      responses:
        '200':
          description: OK
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
            Date:
              schema:
                type: string
                example: Thu, 16 Jan 2025 04:51:17 GMT
            Content-Length:
              schema:
                type: integer
                example: '281'
          content:
            application/json:
              schema:
                type: object
              example:
                data:
                  - id: e393dcf5-4b6f-414b-9bba-f6741a83ea27
                    service_name: wfw
                    event_type: Timer
                    event_icon: timer1
                    event_param:
                      - Duration: string
                    event_result: null
                sent_at: '2025-01-16T04:51:17Z'
                page:
                  number: 1
                  size: 3
                  total_records: 1
                  count: 1
                  sort:
                    - ''
  /v1/validate/expression:
    post:
      tags:
        - default
      summary: Validate Expression
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                expression: foo && bar.baz == 'hello'
                variables:
                  foo: 100
                  bar:
                    baz: abc
      responses:
        '200':
          description: OK
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
            Date:
              schema:
                type: string
                example: Thu, 16 Jan 2025 04:54:24 GMT
            Content-Length:
              schema:
                type: integer
                example: '64'
          content:
            application/json:
              schema:
                type: object
              example:
                message: valid expression
                sent_at: '2025-01-16T04:54:24Z'
  /v1/validate/condition:
    post:
      tags:
        - default
      summary: Validate Condition
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                expression: foo * bar.baz
                variables:
                  foo: 100
                  bar:
                    baz: 0.1
      responses:
        '200':
          description: OK
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
            Date:
              schema:
                type: string
                example: Thu, 16 Jan 2025 04:55:00 GMT
            Content-Length:
              schema:
                type: integer
                example: '63'
          content:
            application/json:
              schema:
                type: object
              example:
                message: valid condition
                sent_at: '2025-01-16T04:55:00Z'
  /v1/workflow:
    post:
      tags:
        - default
      summary: Workflow Start
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                template_id: 9b04440f-0e69-4082-a502-e5f3bb1d0777
                metadata:
                  trigger_rule_id: 123abc
      responses:
        '200':
          description: OK
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
            Date:
              schema:
                type: string
                example: Thu, 16 Jan 2025 04:55:35 GMT
            Content-Length:
              schema:
                type: integer
                example: '97'
          content:
            application/json:
              schema:
                type: object
              example:
                data:
                  workflow_id: 8ed5f2cd-d992-4677-a109-9aa9b8b235a6
                sent_at: '2025-01-16T04:55:35Z'
    get:
      tags:
        - default
      summary: Workflow Execution List
      parameters:
        - name: page
          in: query
          schema:
            type: integer
          example: '1'
        - name: size
          in: query
          schema:
            type: integer
          example: '3'
        - name: sort
          in: query
          schema:
            type: string
          example: start_time,asc
      responses:
        '200':
          description: OK
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
            Date:
              schema:
                type: string
                example: Thu, 16 Jan 2025 07:53:30 GMT
            Content-Length:
              schema:
                type: integer
                example: '421'
          content:
            application/json:
              schema:
                type: object
              example:
                data:
                  - workflow_id: c9be33c9-d9b0-478b-bc11-27a07bc6d19a
                    start_time: '2025-01-16T07:53:04.617039Z'
                  - workflow_id: 7bb93918-bfff-48ae-ab37-ed3b61a295c7
                    start_time: '2025-01-16T07:53:05.954141Z'
                  - workflow_id: 29efa88b-6889-4be1-bd24-3fec807bbdfe
                    start_time: '2025-01-16T07:53:06.828039Z'
                sent_at: '2025-01-16T07:53:30Z'
                page:
                  number: 1
                  size: 3
                  total_records: 3
                  count: 3
                  sort:
                    - start_time asc
  /v1/workflow/{workflow_id}:
    get:
      tags:
        - default
      summary: Workflow History
      parameters:
        - name: page
          in: query
          schema:
            type: integer
          example: '1'
        - name: size
          in: query
          schema:
            type: integer
          example: '3'
        - name: sort
          in: query
          schema:
            type: string
          example: timestamp,desc
        - name: workflow_id
          in: path
          schema:
            type: string
          required: true
          example: 92ac7977-29f9-4729-af21-1a588b3a3df9
      responses:
        '200':
          description: OK
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
            Date:
              schema:
                type: string
                example: Thu, 16 Jan 2025 06:28:00 GMT
            Content-Length:
              schema:
                type: integer
                example: '674'
          content:
            application/json:
              schema:
                type: object
              example:
                data:
                  - event_type: WorkflowExecutionCompleted
                    timestamp: '2025-01-16T05:19:07.25089374Z'
                    attributes:
                      result:
                        - Activity1_String: '=Result_Activity.key'
                          Activity_JSON: '{"key": "abc"}'
                          Result_Activity:
                            key: abc
                          Result_Activity1: abc
                  - event_type: ActivityTaskCompleted
                    timestamp: '2025-01-16T05:19:07.250891164Z'
                    task_name: End
                    task_type: End
                  - event_type: ActivityTaskCompleted
                    timestamp: '2025-01-16T05:19:07.244530221Z'
                    task_name: Activity1
                    task_type: SampleActivityString
                    attributes:
                      result:
                        - abc
                sent_at: '2025-01-16T06:28:00Z'
                page:
                  number: 1
                  size: 3
                  total_records: 9
                  count: 3
                  sort:
                    - timestamp desc
    delete:
      tags:
        - default
      summary: Workflow Terminate
      parameters:
        - name: workflow_id
          in: path
          schema:
            type: string
          required: true
          example: 8ed5f2cd-d992-4677-a109-9aa9b8b235a6
      responses:
        '200':
          description: OK
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
            Date:
              schema:
                type: string
                example: Thu, 16 Jan 2025 05:00:44 GMT
            Content-Length:
              schema:
                type: integer
                example: '35'
          content:
            application/json:
              schema:
                type: object
              example:
                sent_at: '2025-01-16T05:00:44Z'
  /v1/workflow/{workflow_id}/activity_name/{activity_name}:
    post:
      tags:
        - default
      summary: Workflow Signal
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                data: 1. Activity_result == 1
      parameters:
        - name: workflow_id
          in: path
          schema:
            type: string
          required: true
          example: 85547bef-e469-4014-a428-6ee9aac31194
        - name: activity_name
          in: path
          schema:
            type: string
          required: true
          example: Form
      responses:
        '200':
          description: OK
          headers:
            Access-Control-Allow-Credentials:
              schema:
                type: boolean
                example: 'true'
            Access-Control-Allow-Origin:
              schema:
                type: string
                example: '*'
            Content-Length:
              schema:
                type: integer
                example: '105'
            Content-Type:
              schema:
                type: string
                example: application/json; charset=utf-8
            Date:
              schema:
                type: string
                example: Fri, 20 Oct 2023 08:26:39 GMT
            Vary:
              schema:
                type: string
                example: Origin
          content:
            application/json:
              schema:
                type: object
              example:
                data:
                  - field_0hm6j8d:
                      - checked1
                      - checked2
                      - checked3
                sent_at: '2023-10-20T08:26:39Z'
  /v1/livez:
    get:
      tags:
        - default
      summary: Liveness
      responses:
        '200':
          description: OK
          headers:
            Date:
              schema:
                type: string
                example: Thu, 16 Jan 2025 06:29:59 GMT
            Content-Length:
              schema:
                type: integer
                example: '0'
          content:
            text/plain:
              schema:
                type: string
              example: null
  /v1/readyz:
    get:
      tags:
        - default
      summary: Readiness
      responses:
        '200':
          description: OK
          headers:
            Date:
              schema:
                type: string
                example: Thu, 16 Jan 2025 06:30:15 GMT
            Content-Length:
              schema:
                type: integer
                example: '0'
          content:
            text/plain:
              schema:
                type: string
              example: null
