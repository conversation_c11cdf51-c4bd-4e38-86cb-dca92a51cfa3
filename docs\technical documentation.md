# Workflow Engine Technical Documentation

## Table of Contents

-   [Introduction](#introduction)
    -   [Purpose](#purpose)
    -   [Scope](#scope)
    -   [Audience](#scope)
-   [System Architecture](#system-architecture)
    -   [Overview](#overview)
    -   [Workflow Execution Sequence](#workflow-execution-sequence)
-   [Workflow Domain Specific Language (DSL)](#workflow-domain-specific-language-dsl)
    -   [Workflow Definition](#workflow-definition)
    -   [Workflow Data](#workflow-data)
        -   [Input Data](#input-data)
        -   [Output Data](#output-data)
        -   [Data Flow](#data-flow)
    -   [Workflow Start](#workflow-start)
    -   [Workflow States](#workflow-states)
        -   [End](#end)
        -   [Activity](#activity)
            -   [Options](#options)
        -   [Parallel](#parallel)
            -   [Branches](#branches)
        -   [Switch](#switch)
            -   [Cases](#cases)
            -   [Conditions](#conditions)
            -   [Default](#default)
        -   [Event](#event)
        -   [Form](#form)
        -   [CallActivity](#callactivity)
-   [Database Schema](#database-schema)
    -   [Workflow Template](#workflow-template)
    -   [Workflow State](#workflow-state)
    -   [Service Task](#service-task)
    -   [Service Event](#service-event)
    -   [Form Template](#form-template)

## Introduction

### Purpose

Workflow Engine is an eSOP system developed for AOH projects.

### Scope

This documetation covers the technical details and documentation for the development and maintenance of the Workflow Engine.

### Audience

This Documentation is intended for developers who need to understand and work with the Workflow Engine.

## System Architecture

### Overview

The Workflow Engine follows a microservice architecture, comprising the following key components:

-   **Workflow Engine**: Core component for executing workflows.

-   **Workflow Worker**: Worker executes Activities to progress workflows.

-   **Workflow Manager**: HTTP server to communicate with external systems.

-   **Workflow Designer**: Web application for designing workflows.

### Workflow Execution Sequence

This sequence diagram demonstrates the execution of workflow sequence and the relationship between components.

```mermaid
sequenceDiagram
    participant WD as UI-WFDesigner
    participant UI
    participant WFM
    participant DB
    participant WFE as WFE
    participant WFW

    WD ->> WFM: export (wfName, jsonDSL)
    WFM ->> DB: store jsonDSL
    WFM -->> WD: templateId
    UI ->> WFM: start workflow<br/>(templateId)
    WFM ->> DB: get jsonDSL<br/>(templateId)
    WFM ->> WFE: execute workflow (jsonDSL)
    WFE -->> WFM: workflow execution started
    WFM -->> UI: workflow started<br/>(wfId)
    UI ->> DB: subscribe to workflow event (wfId)
    loop workflow job
    opt activity execution
        WFE ->> DB: update status<br/>(activity start)
        WFE ->> WFW: execute activities
        WFE ->> DB: update status<br/>(activity completed)
    end
    opt form execution
        WFE ->> DB: update status<br/>(form start, formJSON,<br/> signal name)
        Note over WFE: wait for user input signal
        UI ->> DB: get formJSON to render
        DB -->> UI: formJSON
        UI ->> WFE: submit form (input, signal name)
        WFE ->> DB: update status<br/>(form completed, input)
    end
    opt switch recovery
        Note over WFE: none of the switch<br/>conditions fulfilled
        WFE ->> DB: update status<br/>(recover start)
        Note over WFE: wait for recover signal
        UI ->> DB: get recoverable conditions
        DB -->> UI: conditions
        UI ->> WFE: recover (condition, signal name)
        WFE ->> DB: update status<br/>(recover completed)
    end
    end
    Note over WFE: workflow completed
    WFE ->> DB: update status<br/>(workflow completed)
```

## Workflow Domain Specific Language (DSL)

We use the workflow domain specific language (DSL) to express and define workflows. Workflow DSL represent a series of interconnected activities that need to be executed in a particular order.

### Workflow Definition

We define our workflow in JSON file. Although our workflow engine can interpret both JSON and YAML files interchangeably, we decided to use JSON to store in DB since searching and querying YAML data within a database can be challenging. For easier readibility, this technical document describe workflows in both JSON and YAML formats.

### Workflow Data

Workflow data refers to the variables and parameters that are used within a workflow to manipulate the execution of the activites or tasks in a specific sequence to achieve a desired outcome.

### Input Data

The initial data input into a workflow instance. Must be in key/value pairs defined inside `variables` field of the workflow. `Key` is the name of the variable and `Value` is the variable value in `any` type.

<table>
<tr>
    <th>JSON</th>
    <th>YAML</th>
</tr>
<tr>
<td valign="top">

```json
{
    "variables": {
        "personName": "Alice"
    }
}
```

</td>
<td valign="top">

```yaml
variables:
    personName: Alice
```

</td>
</tr>
</table>

### Output Data

Each activity execution within the workflow produce a data output. Output data are assigned to the variable name from `result` value and they are typically found within activities.

<table>
<tr>
    <th>JSON</th>
    <th>YAML</th>
</tr>
<tr>
<td valign="top">

```json
{
    "name": "SayHi",
    "activity": {
        "type": "Greeting",
        "result": "SayHi_result"
    }
}
```

</td>
<td valign="top">

```yaml
name: SayHi
activity:
    type: Greeting
    result: SayHi_result
```

</td>
</tr>
</table>

### Data Flow

Data can be passed into an activity within activity `arguments` array. Result of the previous activity also can be passed in similar way to pipe the output of one activity into next activity as an input.

Example below pass the initial input data `[1,2]` into `SumActivity` and result of `SumActivity` `1+2=3`is pass into `MuliplyActivity` as an input.

```mermaid
flowchart TD
    S((start))--"input[1,2]"-->
    SumActivity--"input[3,2]"-->MultiplyActivity-->E((end))
```

<table>
<tr>
    <th>JSON</th>
    <th>YAML</th>
</tr>
<tr>
<td valign="top">

```json
{
    "variables": {
        "number1": 1,
        "number2": 2
    },
    "start": "SumActivity",
    "states": [
        {
            "name": "SumActivity",
            "next": "Multiply Numbers",
            "activity": {
                "type": "Sum",
                "result": "SumActivity_result",
                "arguments": ["number1", "number2"]
            }
        },
        {
            "name": "MultiplyActivity",
            "next": "end",
            "activity": {
                "type": "Multiply",
                "result": "MultiplyActivity_result",
                "arguments": ["SumActivity_result", "number2"]
            }
        },
        {
            "name": "end",
            "end": {
                "terminate": false
            }
        }
    ]
}
```

</td>
<td valign="top">

```yaml
variables:
  number1: 1
  number2: 2
start: SumActivity
states:
  - name: SumActivity
    next: Multiply Numbers
    activity:
      type: Sum
      result: SumActivity_result
      arguments:
        - number1
        - number2
  - name: MultiplyActivity
    next: end
    activity:
      type: Multiply
      result: MultiplyActivity_result
      arguments:
        - SumActivity_result
        - number2
  - name: end
    end:
      terminate: false
```

</td>
</tr>
</table>

### Workflow Start

Every workflow instance need a name of the workflow starting state. Without a starting state, workflow will be completed without executing any states.

| Field | Description                             | Type   | Required |
| ----- | --------------------------------------- | ------ | -------- |
| start | name of the state to start the workflow | string | yes      |

### Workflow States

Workflow states define the control flow logic of the workflow execution. They are essential for tracking the state of tasks, enabling decision-making, and determining the next set of actions to be taken.

Workflow States are a collection of individual states. State has below fields.

| Field        | Description                                              | Type    | Required |
|--------------|----------------------------------------------------------|---------|----------|
| name         | label or name of the state. Must be unique in a workflow | string  | yes      |
| next         | name of the next state for workflow to proceed           | string  | no       |
| end          | [end](#end) state                                        | object  | no       |
| activity     | [activity](#activity) state                              | object  | no       |
| event        | [event](#event) state                                    | object  | no       |
| parallel     | [parallel](#parallel) state                              | object  | no       |
| switch       | [switch](#switch) state                                  | object  | no       |
| form         | [form](#form) state                                      | object  | no       |
| callActivity | [callActivity](#callactivity) state                      | object  | no       |

<table>
<tr>
    <th>JSON</th>
    <th>YAML</th>
</tr>
<tr>
<td valign="top">

```json
{
    "states": [
        {
            "name": "State1",
            "next": "State2"
        }
    ]
}
```

</td>
<td valign="top">

```yaml
states:
    - name: State1
      next: State2
```

</td>
</tr>
</table>

#### End

End state ends the workflow execution path. If terminate is true, it will terminate all the running processes and complete the workflow.

| Field     | Description                              | Type     | Required |
|-----------|------------------------------------------|----------|----------|
| terminate | default is `false`                       | boolean  | yes      |

#### Activity

Activities represent the building blocks of a workflow, and they encompass various operations, actions, or steps that contribute to achieving the overall goal of the business process. Each activity typically performs a specific function and may interact with external systems, users, or resources.

| Field          | Description                                 | Type     | Required |
|----------------|---------------------------------------------|----------|----------|
| type           | type of the activity                        | string   | yes      |
| arguments      | for passing of input data into the activity | []string | yes      |
| boundaryEvents | boundary events attached to the activity    | []string | yes      |
| result         | output data of the activity execution       | any      | yes      |
| options        | activity execution option                   | object   | no       |

```mermaid
flowchart TD
    S((start))-->Greeting-->E((end))
```

<table>
<tr>
    <th>JSON</th>
    <th>YAML</th>
</tr>
<tr>
<td valign="top">

```json
{
    "variables": {
        "name": "John"
    },
    "start": "Greeting",
    "states": [
        {
            "name": "Greeting",
            "next": "end",
            "activity": {
                "type": "SayHello",
                "arguments": ["name"],
                "result": "Result_SayHello",
                "boundaryEvents": []
            }
        },
        {
            "name": "end",
            "end": {
                "terminate": false
            }
        }
    ]
}
```

</td>
<td valign="top">

```yaml
variables:
  name: John
start: Greeting
states:
  - name: Greeting
    next: end
    activity:
      type: SayHello
      arguments:
        - name
      result: Result_SayHello
      boundaryEvents: []
  - name: end
    end:
      terminate: false
```

</td>
</tr>
</table>

##### Options

Activity Options are the timeout policy to provide fine controls to optimize the activity executions as well as the registration of the activity by task queue.

Activity must have Start-To-Close or Schedule-To-Close timeout set. If you are setting Start-To-Close timeout, make sure that Start-To-Close timeout is always set to be longer than the maximum possible time for the Activity Execution to complete.

| Field                  | Description                                                                                      | Type   | Required |
|------------------------| ------------------------------------------------------------------------------------------------ | ------ | -------- |
| taskQueue              | workflow will only execute the activites registered in same task queue                           | string | no       |
| heartbeatTimeout       | heartbeat to check activity liveness, workflow will timeout if there is no heartbeat in activity | number | no       |
| scheduleToCloseTimeout | timeout duration from activity schedule to activity close                                        | number | no       |
| scheduleToStartTimeout | timeout duration from activity schedule to activity start                                        | number | no       |
| startToCloseTimeout    | timeout duration from activity start to close                                                    | number | no       |
| waitForCancellation    | no activity timeout wait until cancellation                                                      | bool   | no       |

#### Parallel

Parallel state enable the concurrent execution of multiple tasks within the workflow. It defines a collection of branches that are executed in parallel. Parallel state waits all of the branches to be completed first before moving on to the next state.

| Field    | Description                                     | Type     | Required |
|----------| ----------------------------------------------- | -------- | -------- |
| branches | an array of branches to be executed in parallel | []object | yes      |

##### Branches

Each branch defines the next state to be executed.

| Field     | Description                                                                                                 | Type    | Required |
|-----------|-------------------------------------------------------------------------------------------------------------| ------- | -------- |
| next      | name of the next state for workflow to proceed                                                              | string  | no       |

```mermaid
flowchart TD
    S((start))-->p{"parallel"}
    p-->ShortDelayBranch-->pj{"parallel"}
    p--->LongDelayBranch-->pj-->E((end))
```

<table>
<tr>
    <th>JSON</th>
    <th>YAML</th>
</tr>
<tr>
<td valign="top">

```json
{
    "variables": {
        "delay_in_second": 10,
        "delay_in_minute": 15
    },
    "start": "parallel",
    "states": [
        {
            "name": "parallel",
            "next": "end",
            "parallel": {
                "branches": [
                    {
                        "next": "ShortDelayBranch"
                    },
                    {
                        "next": "LongDelayBranch"
                    }
                ]
            }
        },
        {
            "name": "ShortDelayBranch",
            "activity": {
                "type": "delay",
                "arguments": ["delay_in_second"],
                "result": "Result_ShortDelayBranch",
                "boundaryEvents": []
            }
        },
        {
            "name": "LongDelayBranch",
            "activity": {
                "type": "delay",
                "arguments": ["delay_in_minute"],
                "result": "Result_LongDelayBranch",
                "boundaryEvents": []
            }
        },
        {
            "name": "end",
            "end": {
                "terminate": false
            }
        }
    ]
}
```

</td>
<td valign="top">

```yaml
variables:
  delay_in_second: 10
  delay_in_minute: 15
start: parallel1
states:
  - name: parallel
    next: end
    parallel:
      branches:
        - next: ShortDelayBranch
        - next: LongDelayBranch
  - name: ShortDelayBranch
    activity:
      type: delay
      arguments:
        - delay_in_second
      result: Result_ShortDelayBranch
      boundaryEvents: []
  - name: LongDelayBranch
    activity:
      type: delay
      arguments:
        - delay_in_minute
      result: Result_LongDelayBranch
      boundaryEvents: []
  - name: end
    end:
      terminate: false
```

</td>
</tr>
</table>

#### Switch

Switch state involves evaluating cases within the workflow. Switch state is exclusive meaning that a switch can define one or the other condition type, but not both. Based on the outcome the workflow can flow into different path.

If multiple defined conditions are evaluated to true, conditions defined first take precedence over conditions defined later.

| Field   | Description                                                    | Type     | Required |
| ------- | -------------------------------------------------------------- | -------- | -------- |
| Cases   | collection of cases to be evaluated                            | []object | yes      |
| Default | name of the state to be executed if the condition is fulfilled | object   | no       |

##### Case

Case contains conditional expressions and as well as the name of the `start` state to be proceeded if one of the condition is fulfilled.

| Field       | Description                                                                                                 | Type    | Required |
|-------------|-------------------------------------------------------------------------------------------------------------| ------- |----------|
| name        | name of the case. this define the order of cases to be evaluated in lexical order                           | string  | yes      |
| next        | name of the next state for workflow to proceed                                                              | string  | no       |
| conditional | condition to be evalutated                                                                                  | string  | yes      |

##### Conditions

Condition defines `input`, `operator` and expected `value`.

Enumerations for operator are:

-   EQ (equal)

-   NEQ (not equal)

-   MT (more than)

-   MTE (more than or equal)

-   LT (less than)

-   LTE (less than or equal)

| Field    | Description                                                | Type   | Required |
|----------| ---------------------------------------------------------- | ------ | -------- |
| input    | variable name of the input                                 | string | yes      |
| operator | comparison operator for evaluation. they should be in enum | string | yes      |
| value    | value to be evaluated with input                           | any    | yes      |

##### Default

If `Default` is defined, workflow will flow into default path if none of the condition is fulfilled.

| Field     | Description                                                                                                 | Type    | Required |
|-----------|-------------------------------------------------------------------------------------------------------------| ------- | -------- |
| next      | name of the next state for workflow to proceed                                                              | string  | no       |

```mermaid
flowchart TD
    S((start))-->NumberInput-->s{"switch"}
    s--"1. NumberInput > 10"-->Greater-->E
    s--"2. NumberInput < 10"--->Lesser-->E((end))
    s--"default"-->equal-->E
```

<table>
<tr>
    <th>JSON</th>
    <th>YAML</th>
</tr>
<tr>
<td valign="top">

```json
{
    "variables": {
        "input": 10
    },
    "start": "NumberInput",
    "states": [
        {
            "name": "NumberInput",
            "next": "switch",
            "activity": {
              "type": "number",
              "arguments": ["input"],
              "result": "Result_NumberInput",
              "boundaryEvents": []
            }
        },
        {
            "name": "switch",
            "switch": {
                "cases": [
                    {
                        "name": "1. NumberInput > 10",
                        "next": "Greater",
                        "condition": {
                            "input": "NumberInput",
                            "operator": "MT",
                            "value": 10
                        }
                    },
                    {
                        "name": "2. NumberInput < 10",
                        "next": "Lesser",
                        "condition": {
                            "input": "NumberInput",
                            "operator": "LT",
                            "value": 10
                        }
                    }
                ],
                "default": {
                    "next": "Equal"
                }
            }
        },
        {
            "name": "Greater",
            "next": "end",
            "activity": {
                "type": "GreaterActivity",
                "arguments": ["Result_NumberInput"],
                "result": "Result_Greater",
                "boundaryEvents": []
            }
        },
        {
            "name": "Lesser",
            "next": "end",
            "activity": {
                "type": "LesserActivity",
                "arguments": ["Result_NumberInput"],
                "result": "Result_Lesser",
                "boundaryEvents": []
            }
        },
        {
            "name": "Equal",
            "next": "end",
            "activity": {
                "type": "EqualActivity",
                "arguments": ["Result_NumberInput"],
                "result": "Result_Equal",
                "boundaryEvents": []
            }
        },
        {
            "name": "end",
            "end": {
                "terminate": false
            }
        }
    ]
}
```

</td>
<td valign="top">

```yaml
variables:
  input: 10
start: NumberInput
states:
  - name: NumberInput
    next: switch
    activity:
      type: number
      arguments:
        - input
      result: Result_NumberInput
      boundaryEvents: []
  - name: switch
    switch:
      cases:
        - name: 1. NumberInput > 10
          next: Greater
          condition:
            input: NumberInput
            operator: MT
            value: 10
        - name: 2. NumberInput < 10
          next: Lesser
          condition:
            input: NumberInput
            operator: LT
            value: 10
      default:
        next: Equal
  - name: Greater
    next: end
    activity:
      type: GreaterActivity
      arguments:
        - Result_NumberInput
      result: Result_Greater
      boundaryEvents: []
  - name: Lesser
    next: end
    activity:
      type: LesserActivity
      arguments:
        - Result_NumberInput
      result: Result_Lesser
      boundaryEvents: []
  - name: Equal
    next: end
    activity:
      type: EqualActivity
      arguments:
        - Result_NumberInput
      result: Result_Equal
      boundaryEvents: []
  - name: end
    end:
      terminate: false
```

</td>
</tr>
</table>

#### Form

Form state is similar to the activity state except that it waits for user input to proceed the workflow.

| Field          | Description                                                                         | Type     | Required |
|----------------|-------------------------------------------------------------------------------------|----------|----------|
| type           | type of the form                                                                    | string   | yes      |
| arguments      | for passing of data into form. it first argument must store the json formatted form | []string | yes      |
| result         | submitted form data. can be further use as input in subsequent states               | any      | yes      |

```mermaid
flowchart TD
    S((start))-->F("Absence Form <br/>(wait for submission)")-->E((end))
```

<table>
<tr>
    <th>JSON</th>
    <th>YAML</th>
</tr>
<tr>
<td valign="top">

```json
{
    "variables": {
        "formJSON": {},
        "endpoint": "http://example.com/save/form"
    },
    "start": "Absence Form",
    "states": [
        {
            "name": "Absence Form",
            "next": "end",
            "form": {
                "type": "AbsenceFormType",
                "arguments": ["formJSON", "endpoint"],
                "result": "Result_AbsenceForm"
            }
        },
        {
            "name": "end",
            "end": {
                "terminate": false
            }
        }
    ]
}
```

</td>
<td valign="top">

```yaml
variables:
  formJSON: {}
  endpoint: "http://example.com/save/form"
start: Absence Form
states:
  - name: Absence Form
    next: end
    form:
      type: AbsenceFormType
      arguments:
        - formJSON
        - endpoint
      result: Result_AbsenceForm
  - name: end
    end:
      terminate: false
```

</td>
</tr>
</table>

#### Event

Event state waits for a defined event to occur before proceeding to next states.

Events can be embedded into the activity. When embedded, they can be either interrupting or non-interrupting event.
Interrupting event will cancel the host activity and other events attached to the host while non-interrupting will proceed to next state without cancelling other states.

| Field        | Description                              | Type     | Required |
|--------------|------------------------------------------|----------|----------|
| type         | type of the event                        | string   | yes      |
| arguments    | for passing of input data into the event | []string | yes      |
| result       | output data of the event                 | any      | yes      |
| interrupting | default is `false`                       | boolean  | yes      |

```mermaid
flowchart LR
    subgraph CancelByEvent
        F((("Timer")))
    end
    S((start))-->CancelByEvent-->Skip-->E((end))
    F((("Timer")))-->T((terminate))
```

<table>
<tr>
    <th>JSON</th>
    <th>YAML</th>
</tr>
<tr>
<td valign="top">

```json
{
    "variables": {
        "wait": 10
    },
    "start": "CancelByEvent",
    "states": [
        {
            "name": "CancelByEvent",
            "next": "Skip",
            "activity": {
                "type": "CancelByEventType",
                "arguments": [],
                "result": "",
                "boundaryEvents": ["Timer"]
            }
        },
        {
            "name": "Skip",
            "next": "end",
            "activity": {
                "type": "SkipType",
                "arguments": [],
                "result": "",
                "boundaryEvents": []
            }
        },
        {
            "name": "Timer",
            "next": "terminate",
            "event": {
                "type": "TimerType",
                "arguments": ["wait"],
                "result": "Result_TimerType",
                "interrupting": true
            }
        },
        {
            "name": "end",
            "end": {
                "terminate": false
            }
        },
        {
            "name": "terminate",
            "end": {
                "terminate": true
            }
        }
    ]
}
```

</td>
<td valign="top">

```yaml
variables:
  wait: 10
start: CancelByEvent
states:
  - name: CancelByEvent
    next: Skip
    activity:
      type: CancelByEventType
      arguments: []
      result: ''
      boundaryEvents:
        - Timer
  - name: Skip
    next: end
    activity:
      type: SkipType
      arguments: []
      result: ''
      boundaryEvents: []
  - name: Timer
    next: terminate
    event:
      type: TimerType
      arguments:
        - wait
      result: Result_TimerType
  - name: end
    end:
      terminate: false
  - name: terminate
    end:
      terminate: true
```

</td>
</tr>
</table>

#### CallActivity

CallActivity state spawns child workflow as part of its workflow execution.

| Field          | Description                                                                                | Type     | Required |
|----------------|--------------------------------------------------------------------------------------------|----------|----------|
| arguments      | for passing of dsl workflow. its first argument must store the workflow dsl in json format | []string | yes      |
| boundaryEvents | list of events attached to a CallActivity                                                  | []string | yes      |
| result         | CallActivity output. can be further use as input in subsequent states                      | any      | yes      |

```mermaid
flowchart TD
    S((start))-->F("Execute Another Workflow <br/>(spawn child workflow)")-->E((end))
```

<table>
<tr>
    <th>JSON</th>
    <th>YAML</th>
</tr>
<tr>
<td valign="top">

```json
{
    "variables": {
        "workflowJSON": {
            "variables": {
                "name": "John"
            },
            "start": "Greeting",
            "states": [
                {
                    "name": "Greeting",
                    "next": "end",
                    "activity": {
                        "type": "SayHello",
                        "arguments": ["name"],
                        "result": "Result_SayHello",
                        "boundaryEvents": []
                    }
                },
                {
                    "name": "end",
                    "end": {
                        "terminate": false
                    }
                }
            ]
        }
    },
    "start": "Execute Child Workflow",
    "states": [
        {
            "name": "Execute Child Workflow",
            "next": "end",
            "callActivity": {
                "arguments": ["workflowJSON"],
                "result": "Result_ExecuteChildWorkflow"
            }
        },
        {
            "name": "end",
            "end": {
                "terminate": false
            }
        }
    ]
}
```

</td>
<td valign="top">

```yaml
variables:
  workflowJSON:
    variables:
      name: John
    start: Greeting
    states:
      - name: Greeting
        next: end
        activity:
          type: SayHello
          arguments:
            - name
          result: Result_SayHello
          boundaryEvents: []
      - name: end
        end:
          terminate: false
start: Execute Child Workflow
states:
  - name: Execute Child Workflow
    next: end
    callActivity:
      arguments:
        - workflowJSON
      result: Result_ExecuteChildWorkflow
  - name: end
    end:
      terminate: false
```

</td>
</tr>
</table>

## Database Schema

```mermaid
erDiagram
    incident ||--opt{ workflow_template: "use workflow template"
    incident ||--opt{ workflow_state: "has workflow states"
    incident {
        uuid workflow_id
        uuid workflow_template_id
    }
    workflow_template {
        uuid id
        timestamptz created_at
        timestamptz updated_at
        text name
        jsonb schema_json
        jsonb designer_json
        bool editable
    }
    workflow_state {
        uuid id
        timestamptz created_at
        uuid workflow_id
        uuid workflow_run_id
        text activity_name
        text activity_type
        text event
        jsonb data
        text owned_by
}
```

Incident is an external system where workflows are executed. Incident trigger the workflow by using `workflow_template` and it tracks the status of workflow by `workflow_state` event.

### Workflow Template

Catalog of avaiable workflow templates.

**table_name**: workflow_template
**primary_key**: id
**unique_keys**: name

| column_name   | is_nullable | data_type                | col_description                                                 |
| ------------- | ----------- | ------------------------ | --------------------------------------------------------------- |
| id            | NO          | uuid                     | universally unique identifier for the workflow template         |
| created_at    | NO          | timestamp with time zone | timestamp at the time the row is created at                     |
| updated_at    | NO          | timestamp with time zone | timestamp at the time the row is updated at                     |
| name          | NO          | text                     | human readable workflow template name                           |
| schema_json   | NO          | jsonb                    | workflow doman specific languagne use in execution of workflows |
| designer_json | NO          | jsonb                    | joint-js compatible json to render workflow in frontend         |
| editable      | YES         | boolean                  | to check workflow template is editable                          |

### Workflow State

Keeps state of all workflow instances.

**table_name**: workflow_state
**primary_key**: id
**foreign_key**: enum_event.id

| column_name     | is_nullable | data_type                | col_description                                           |
| --------------- | ----------- | ------------------------ | --------------------------------------------------------- |
| id              | NO          | uuid                     | universally unique identifier for the workflow state      |
| created_at      | NO          | timestamp with time zone | timestamp at the time the row is created at               |
| updated_at      | NO          | timestamp with time zone | timestamp at the time the row is updated at               |
| workflow_id     | NO          | uuid                     | id of the workflow generated by workflow engine           |
| workflow_run_id | NO          | uuid                     | workflow running instance id generated by workflow engine |
| activity_name   | YES         | text                     | name of the activity or task that is executed in workflow |
| activity_type   | YES         | text                     | type of the activity or task that is executed in workflow |
| event           | NO          | text                     | workflow history event                                    |
| data            | YES         | jsonb                    | workflow input, output and error data                     |
| owned_by        | NO          | text                     | owner of the workflow responsible for the workflow state  |

`enum_event` table is an enumeration for column named `event` in `workflow_state` table.

| id                               | comment                                                                                                                                                                        |
|----------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| WorkflowExecutionStarted         | always the first event in a workflow execution event history                                                                                                                   |
| WorkflowExecutionCompleted       | indicates that the workflow execution has successfully completed                                                                                                               |
| WorkflowExecutionFailed          | indicates that the workflow execution has unsuccessfully completed with error                                                                                                  |
| WorkflowExecutionTimedOut        | indicates that the workflow execution has timed out due to the workflow having not been completed within timeout settings                                                      |
| WorkflowTaskScheduled            | indicates that the workflow task has been scheduled                                                                                                                            |
| WorkflowTaskStarted              | indicates that the workflow task has started                                                                                                                                   |
| WorkflowTaskTimedOut             | indicates that the workflow task has timed out due to the workflow task having not been completed within timeout settings                                                      |
| WorkflowTaskCompleted            | indicates that the workflow task has completed                                                                                                                                 |
| WorkflowTaskFailed               | indicates that the workflow task encountered a failure                                                                                                                         |
| ActivityTaskScheduled            | indicates that the activity task has scheduled                                                                                                                                 |
| ActivityTaskStarted              | indicates that the activity task execution has started. this event is not written to the history until the terminal event (ActivityTaskCompleted or ActivityTaskFailed) occurs |
| ActivityTaskCompleted            | indicates that the activity task has completed                                                                                                                                 |
| ActivityTaskFailed               | indicates that the activity task has failed with activity execution error                                                                                                      |
| ActivityTaskTimedOut             | indicates that the activity has timed out due to one of the timeout settings: Schedule-to-Close timeout and Schedule-to-Start timeout                                          |
| ActivityTaskCancelRequested      | indicates that a request to cancel the activity has occurred                                                                                                                   |
| ActivityTaskCanceled             | indicates that the activity has been canceled                                                                                                                                  |
| WorkflowExecutionCancelRequested | indicates that a request has been made to cancel the workflow execution                                                                                                        |
| WorkflowExecutionCanceled        | indicates that workflow execution has been canceled                                                                                                                            |
| WorkflowExecutionTerminated      | indicates that the workflow execution has been forcefully terminated by api call                                                                                               |
| FormTaskScheduled                | indicates that the form task has scheduled                                                                                                                                     |
| FormTaskStarted                  | indicates that the form task has started. this event waits for form submission                                                                                                 |
| FormTaskCompleted                | indicates that the form task has completed                                                                                                                                     |
| FormTaskFailed                   | indicates that the form task has failed with form task error                                                                                                                   |
| FormTaskCanceled                 | indicates that form task has been canceled                                                                                                                                     |
| RecoverTaskScheduled             | indicates that the recover task has been scheduled                                                                                                                             |
| RecoverTaskStarted               | indicates that the recover task has started. this event wait for user intervention to recover workflow state                                                                   |
| RecoverTaskCompleted             | indicates that the recover task has completed                                                                                                                                  |
| RecoverTaskFailed                | indicates that the recover task has failed with error                                                                                                                          |
| RecoverTaskCanceled              | indicates that the recover task has been canceled                                                                                                                              |
| CallActivityTaskScheduled        | indicates that the callActivity task has been scheduled                                                                                                                        |
| CallActivityTaskStarted          | indicates that the callActivity task has started. this event wait for user intervention to recover workflow state                                                              |
| CallActivityTaskCompleted        | indicates that the callActivity task has completed                                                                                                                             |
| CallActivityTaskFailed           | indicates that the callActivity task has failed with error                                                                                                                     |
| CallActivityTaskCanceled         | indicates that the callActivity task has been canceled                                                                                                                         |

### Service Task

Catalog for tasks which are exposed by respective services

**table_name**: service_task
**primary_key**: id
**unique_keys**: task_type

| column_name       | is_nullable | data_type                | col_description                                            |
| ----------------- | ----------- | ------------------------ | ---------------------------------------------------------- |
| id                | NO          | uuid                     | universally unique identifier for the service task         |
| created_at        | NO          | timestamp with time zone | timestamp at the time the row is created at                |
| updated_at        | NO          | timestamp with time zone | timestamp at the time the row is updated at                |
| service_name      | NO          | text                     | name of the backend service responsible for the task       |
| task_category     | NO          | text                     | category of the task/activity                              |
| task_type         | NO          | text                     | type of the task/activity                                  |
| task_param        | YES         | jsonb                    | list of input parameters for the task/activity             |
| ui_source_code    | YES         | text                     | name of the ui source code                                 |
| task_result       | YES         | jsonb                    | example task output that indicates output data type        |
| timeout_in_second | NO          | numeric                  | timeout for activity/task. default is set to 300 in second |

### Service Event

Catalog for events which are exposed by respective services

**table_name**: service_event
**primary_key**: id
**unique_keys**: event_type

| column_name  | is_nullable | data_type                | col_description                                                             |
|--------------|-------------|--------------------------|-----------------------------------------------------------------------------|
| id           | NO          | uuid                     | universally unique identifier for the service task                          |
| created_at   | NO          | timestamp with time zone | timestamp at the time the row is created at                                 |
| updated_at   | NO          | timestamp with time zone | timestamp at the time the row is updated at                                 |
| service_name | NO          | text                     | name of the backend service responsible for the task                        |
| event_type   | NO          | text                     | type of the event                                                           |
| event_param  | YES         | jsonb                    | list of input parameters for the event                                      |
| event_result | YES         | jsonb                    | example event output that indicates output data type                        |
| event_icon   | NO          | text                     | icon of the event. refer to enum_service_event_icon for supported icon list |

`enum_service_event_icon` table is an enumeration for `event_icon`.

| id                   | comment |
|----------------------|---------|
| none                 | NULL    |
| message1             | NULL    |
| message2             | NULL    |
| timer1               | NULL    |
| conditional1         | NULL    |
| link1                | NULL    |
| link2                | NULL    |
| signal1              | NULL    |
| signal2              | NULL    |
| error1               | NULL    |
| error2               | NULL    |
| escalation1          | NULL    |
| escalation2          | NULL    |
| termination1         | NULL    |
| termination2         | NULL    |
| compensation1        | NULL    |
| compensation2        | NULL    |
| cancel1              | NULL    |
| cancel2              | NULL    |
| multiple1            | NULL    |
| multiple2            | NULL    |
| parallel1            | NULL    |
| parallel2            | NULL    |

### Form Template

Catalog for form templates that are used in workflows.

**table_name**: id
**unique_keys**: name

| column_name    | is_nullable | data_type                | col_description                                                                    |
| -------------- | ----------- | ------------------------ | ---------------------------------------------------------------------------------- |
| id             | NO          | uuid                     | The universally unique identifier for the form template entity.                    |
| name           | NO          | text                     | A human-readable name for the form template (e.g. Critical Incident Form).         |
| form_json      | NO          | jsonb                    | The form-js form schema, which is a form template that can be re-used many places. |
| component_keys | YES         | jsonb                    | An object that contains key value pairs to input elements in a form.               |
| created_at     | NO          | timestamp with time zone | timestamp at the time the row is created at                                        |
| updated_at     | NO          | timestamp with time zone | timestamp at the time the row is updated at                                        |
