package postgresql

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/mssfoobar/app/wfe/internal/model/sqlplugin"
)

const (
	createWorkflowTemplateQuery = `INSERT INTO workflow_template (id, name, workflow_json, designer_json, editable, 
								created_by, updated_by, tenant_id) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`

	updateWorkflowTemplateQuery = `UPDATE workflow_template SET name = $1, workflow_json = $2, designer_json = $3,
								editable = $4, updated_by = $5, occ_lock = $6 WHERE id = $7 AND tenant_id = $8`

	getWorkflowTemplateQuery       = `SELECT * FROM workflow_template WHERE id = $1 AND tenant_id = $2`
	getWorkflowTemplateByNameQuery = `SELECT * FROM workflow_template WHERE name = $1 AND tenant_id = $2`

	deleteWorkflowTemplateByIdQuery = `DELETE FROM workflow_template WHERE id = $1 AND tenant_id = $2`

	listWorkflowTemplate        = `SELECT * FROM workflow_template WHERE tenant_id = $1 LIMIT $2 OFFSET $3`
	listWorkflowTemplateOrderBy = `SELECT * FROM workflow_template WHERE tenant_id = $1 ORDER BY %s LIMIT $2 OFFSET $3`

	getTotalCountWorkflowTemplate = `SELECT COUNT(*) FROM workflow_template WHERE tenant_id = $1`
)

func (pdb *db) InsertIntoWorkflowTemplate(
	ctx context.Context,
	rows *sqlplugin.WorkflowTemplateRow,
) (sql.Result, error) {
	return pdb.conn.ExecContext(
		ctx,
		createWorkflowTemplateQuery,
		rows.Id,
		rows.Name,
		rows.WorkflowJson,
		rows.DesignerJson,
		rows.Editable,
		rows.CreatedBy,
		rows.UpdatedBy,
		rows.TenantId,
	)
}

func (pdb *db) UpdateWorkflowTemplate(
	ctx context.Context,
	rows *sqlplugin.WorkflowTemplateRow,
) (sql.Result, error) {
	return pdb.conn.ExecContext(
		ctx,
		updateWorkflowTemplateQuery,
		rows.Name,
		rows.WorkflowJson,
		rows.DesignerJson,
		rows.Editable,
		rows.UpdatedBy,
		rows.OccLock,
		rows.Id,
		rows.TenantId,
	)
}

func (pdb *db) SelectFromWorkflowTemplate(
	ctx context.Context,
	filter sqlplugin.WorkflowTemplateFilter,
) (*sqlplugin.WorkflowTemplateRow, error) {
	switch {
	case filter.Id != nil:
		return pdb.selectFromWorkflowTemplate(ctx, filter)
	case filter.Name != nil:
		return pdb.selectFromWorkflowTemplateByName(ctx, filter)
	default:
		return nil, fmt.Errorf("missing filter argument")
	}
}

func (pdb *db) selectFromWorkflowTemplate(
	ctx context.Context,
	filter sqlplugin.WorkflowTemplateFilter,
) (*sqlplugin.WorkflowTemplateRow, error) {
	var row sqlplugin.WorkflowTemplateRow
	err := pdb.conn.GetContext(ctx, &row, getWorkflowTemplateQuery, filter.Id, filter.TenantId)
	if err != nil {
		return nil, err
	}
	return &row, nil
}

func (pdb *db) selectFromWorkflowTemplateByName(
	ctx context.Context,
	filter sqlplugin.WorkflowTemplateFilter,
) (*sqlplugin.WorkflowTemplateRow, error) {
	var row sqlplugin.WorkflowTemplateRow
	err := pdb.conn.GetContext(ctx, &row, getWorkflowTemplateByNameQuery, filter.Name, filter.TenantId)
	if err != nil {
		return nil, err
	}
	return &row, nil
}

func (pdb *db) ListFromWorkflowTemplate(
	ctx context.Context,
	filter sqlplugin.WorkflowTemplatePaginateFilter,
) ([]sqlplugin.WorkflowTemplateRow, error) {
	switch {
	case filter.OrderBy != "":
		return pdb.listFromWorkflowTemplateOrderBy(ctx, filter)
	default:
		return pdb.listFromWorkflowTemplate(ctx, filter)
	}
}

func (pdb *db) listFromWorkflowTemplate(
	ctx context.Context,
	filter sqlplugin.WorkflowTemplatePaginateFilter,
) ([]sqlplugin.WorkflowTemplateRow, error) {
	var rows []sqlplugin.WorkflowTemplateRow
	err := pdb.conn.SelectContext(ctx,
		&rows,
		listWorkflowTemplate,
		filter.TenantId,
		filter.Limit,
		filter.Offset,
	)
	return rows, err
}

func (pdb *db) listFromWorkflowTemplateOrderBy(
	ctx context.Context,
	filter sqlplugin.WorkflowTemplatePaginateFilter,
) ([]sqlplugin.WorkflowTemplateRow, error) {
	var rows []sqlplugin.WorkflowTemplateRow
	err := pdb.conn.SelectContext(ctx,
		&rows,
		fmt.Sprintf(listWorkflowTemplateOrderBy, filter.OrderBy),
		filter.TenantId,
		filter.Limit,
		filter.Offset,
	)
	return rows, err
}

func (pdb *db) DeleteFromWorkflowTemplate(
	ctx context.Context,
	filter sqlplugin.WorkflowTemplateFilter,
) (sql.Result, error) {
	return pdb.conn.ExecContext(ctx, deleteWorkflowTemplateByIdQuery, filter.Id, filter.TenantId)
}

func (pdb *db) CountFromWorkflowTemplate(
	ctx context.Context,
	filter sqlplugin.WorkflowTemplateCountFilter,
) (int, error) {
	var total int
	err := pdb.conn.GetContext(ctx, &total, getTotalCountWorkflowTemplate, filter.TenantId)
	return total, err
}
