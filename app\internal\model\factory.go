package model

import (
	aohlog "github.com/mssfoobar/aoh-golib/logger"
	"github.com/mssfoobar/app/wfe/internal/config"
	"github.com/mssfoobar/app/wfe/internal/model/sqlplugin"
	"go.uber.org/zap"
)

type Factory struct {
	mainDBConn DbConn
}

type DbConn struct {
	sqlplugin.DB
}

// NewFactory creates a new instance of Factory, which is a factory of database store objects
func NewFactory(cfg config.SQL) (*Factory, error) {
	conn, err := NewSQLDB(&cfg)
	if err != nil {
		return nil, err
	}

	return &Factory{mainDBConn: DbConn{
		DB: conn,
	}}, err
}

// NewWorkflowTemplateStore returns a new notification template store
func (f *Factory) NewWorkflowTemplateStore() WorkflowTemplateStore {
	return newSqlWorkflowTemplate(f.mainDBConn)
}

// NewFormTemplateStore returns a new notification template store
func (f *Factory) NewFormTemplateStore() FormTemplateStore {
	return newSqlFormTemplate(f.mainDBConn)
}

// NewServiceActivityStore returns a new service activity store
func (f *Factory) NewServiceActivityStore() ServiceActivityStore {
	return newSqlServiceActivity(f.mainDBConn)
}

// NewServiceEventStore returns a new service activity store
func (f *Factory) NewServiceEventStore() ServiceEventStore {
	return newSqlServiceEvent(f.mainDBConn)
}

func (f *Factory) Close() {
	if err := f.mainDBConn.Close(); err != nil {
		aohlog.Error("failed to close db connection", zap.Error(err))
	}
}
