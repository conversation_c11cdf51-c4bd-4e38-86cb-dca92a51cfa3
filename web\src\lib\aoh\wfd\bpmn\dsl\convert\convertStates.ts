import type { dia } from "rappid/rappid";
import { RESULT_REGEX, TAB_VALUE } from "$lib/aoh/wfd/components/inspector/constant";
import * as shapes from "$lib/aoh/wfd/bpmn/shapes";
import { GraphError } from "$lib/aoh/wfd/bpmn/utils/error";
import { Queue } from "$lib/aoh/wfd/bpmn/utils/type";
import type * as schema from "$lib/aoh/wfd/bpmn/dsl/schema";
import { getActivityParams, getActivityTimeout } from "$lib/aoh/wfd/stores/activities";
import { getEventParams } from "$lib/aoh/wfd/stores/events";

type VarStorage = schema.Workflow["workflow_json"]["variables"];
type InQueueState = {
	state: State;
	start: dia.Element;
	prevTransition?: Transitionable;
};
type AvailableState = "sequence" | "parallel" | "activity" | "switch" | "event" | "end";

type StateTransition = {
	next?: string;
};

type StateContext = {
	vars: VarStorage;
	taskQueue: Queue<InQueueState>;
	createState: ReturnType<typeof getCreateState>;
	visitedElements: Record<string, boolean>;
	element?: dia.Element;
	prevState?: State;
	parent?: State;
};

type Metadata = {
	specVersion: string;
	name: string;
};

interface Transitionable {
	/**
	 * Set the transition of transitionable object
	 * @param transition - transition object to set.
	 */
	setTransition(transition: StateTransition): void;
}

interface State extends Transitionable {
	/**
	 * Turn state object into a serializable format
	 * @returns serializable object that follow type generated from schema
	 */
	serialize(): schema.Statement[];
	/**
	 * Begin traversing and getting all elements belong to state
	 * @param el - State starting point.
	 * @param prevTransition - transition of previous state
	 */
	start(el: dia.Element, prevTransition?: Transitionable): void;
	/**
	 * Get parent state
	 * @return parent state if available
	 */
	get parent(): State | undefined;

	get name(): string | undefined;
}

// Form & CallActivity shapes are also handled by Activity state
class ActivityState implements State {
	private readonly ctx: StateContext;
	private readonly category: "FORM" | "ACTIVITY" | "CALL_ACTIVITY";

	private readonly metadata: {
		id: string;
		name: string;
		result: string;
		type: string;
		options?: schema.ActivityInvocation["options"];
	};
	private readonly arguments: string[] = [];
	private transition: StateTransition = { next: undefined };
	private readonly boundaryEvents: string[] = [];

	get name() {
		return this.metadata.name;
	}

	get parent() {
		return this.ctx.parent;
	}

	constructor(ctx: StateContext) {
		if (!(ctx.element instanceof shapes.ActivityBase)) {
			throw GraphError.createMsgError("Element is not an <Activity>");
		}
		switch (ctx.element.get("type")) {
			case shapes.Form.type:
				this.category = "FORM";
				break;
			case shapes.CallActivity.type:
				this.category = "CALL_ACTIVITY";
				break;
			default:
				this.category = "ACTIVITY";
		}
		this.ctx = ctx;
		ctx.visitedElements[ctx.element.id] = true;

		this.metadata = {
			id: ctx.element.id as string,
			name: ctx.element.attr("label/text"),
			result: `Result_${ctx.element?.attr("label/text").replace(RESULT_REGEX, "")}`,
			type: ctx.element.attr("type/text"),
		};

		if (!this.metadata.type) {
			throw GraphError.createCellError("<Activity> must have a type defined", ctx.element);
		}

		// check for embedded cell and push into boundaryEvents list
		const cells = ctx.element.getEmbeddedCells() || [];
		cells.forEach((element) => {
			this.boundaryEvents.push(element.attr("label/text"));
		});

		// Activity options
		const typeOptions = ctx.element.attr("data/typeOptions") as Record<string, unknown>;
		const taskType = ctx.element.attr("type/text") as string;

		// Get parameters by activity type
		let params: Record<string, unknown>[] = [];
		switch (this.category) {
			case "FORM":
				params.push({ FormJSON: {} });
				break;
			case "CALL_ACTIVITY":
				params.push({ WorkflowJSON: {} });
				break;
			default:
				params.push(...getActivityParams(taskType));
		}

		// Add additional bound form data into typeFields
		params = JSON.parse(JSON.stringify(params));
		// if (this.category === "FORM") {
		// 	const bindData = ctx.element?.attr("data/bindFields") as FormComponent[];
		// 	bindData?.forEach((component) => {
		// 		const obj: Record<string, any> = {};
		// 		let value: any = {};
		// 		switch (component.type) {
		// 			case FormComponentType.NUMBER: {
		// 				value = 0;
		// 				break;
		// 			}
		// 			case FormComponentType.CHECKBOX: {
		// 				value = false;
		// 				break;
		// 			}
		// 			case FormComponentType.TEXTFIELD:
		// 			case FormComponentType.TEXTAREA:
		// 			case FormComponentType.DATETIME: {
		// 				value = "";
		// 				break;
		// 			}
		// 			default: {
		// 				value = [];
		// 				break;
		// 			}
		// 		}
		// 		if (component.key) {
		// 			obj[component.key] = value;
		// 		}
		// 		typeFields.push(obj);
		// 	});
		// }

		// Set type specific activity options
		if (this.category === "ACTIVITY") {
			const startToCloseTimeout = getActivityTimeout(taskType);
			if (startToCloseTimeout !== undefined) {
				this.metadata.options = { startToCloseTimeout };
			}
		}

		// Construct arg names
		const argName = (opt: string) => this.metadata.name + "_" + opt;

		// Add arg to var list
		for (const param of params) {
			// Object keys from api
			const key = Object.keys(param)[0];
			const value = Object.values(param)[0];
			this.arguments.push(argName(key));
			this.ctx.vars[argName(key)] = typeOptions?.[key] ? typeOptions[key] : value;
		}
	}

	setTransition(transition: StateTransition): void {
		this.transition = transition;
	}

	start() {
		throw GraphError.createMsgError("Cannot start an <Activity>");
	}

	serialize(): schema.Statement[] {
		const statement: schema.Statement = {
			name: this.metadata.name,
			next: this.transition?.next,
		};

		switch (this.category) {
			case "FORM":
				statement.form = {
					id: this.metadata.id,
					type: this.metadata.type,
					arguments: this.arguments,
					boundaryEvents: this.boundaryEvents,
					result: this.metadata.result,
				};
				break;
			case "CALL_ACTIVITY":
				statement.callActivity = {
					id: this.metadata.id,
					type: this.metadata.type,
					arguments: this.arguments,
					boundaryEvents: this.boundaryEvents,
					result: this.metadata.result,
				};
				break;
			default:
				statement.activity = {
					id: this.metadata.id,
					type: this.metadata.type,
					arguments: this.arguments,
					boundaryEvents: this.boundaryEvents,
					result: this.metadata.result,
					options: this.metadata.options,
				};
		}

		return [statement];
	}
}

// Event State
class EventState implements State {
	private readonly ctx: StateContext;
	private readonly category: "EVENT";

	private readonly metadata: {
		id: string;
		name: string;
		result: string;
		type: string;
	};
	private readonly arguments: string[] = [];
	private transition: StateTransition = { next: undefined };
	private readonly interrupting: boolean = true;

	get name() {
		return this.metadata.name;
	}

	get parent() {
		return this.ctx.parent;
	}

	constructor(ctx: StateContext) {
		if (!(ctx.element instanceof shapes.Event)) {
			throw GraphError.createMsgError("Element is not an <Event>");
		}
		this.category = "EVENT";
		this.ctx = ctx;
		ctx.visitedElements[ctx.element.id] = true;

		this.metadata = {
			id: ctx.element.id as string,
			name: ctx.element.attr("label/text"),
			result: `Result_${ctx.element.attr("label/text").replace(RESULT_REGEX, "")}`,
			type: ctx.element.attr("type/text"),
		};
		if (!this.metadata.type) {
			throw GraphError.createCellError("<Event> must have a type defined", ctx.element);
		}

		// check for non-interrupting event
		if (ctx.element.attr("border/borderStyle") === "dashed") {
			if (!ctx.element.isEmbedded()) {
				throw GraphError.createCellError(
					"Non-interrupting <Event> must be at the <Activity> boundary",
					ctx.element
				);
			}
			this.interrupting = false;
		}

		// Event options
		const typeOptions = ctx.element.attr("data/typeOptions") as Record<string, unknown>;
		const taskType = ctx.element.attr("type/text") as string;

		// Get params from type
		const params = getEventParams(taskType);

		// Construct arg names
		const argName = (opt: string) => this.metadata.name + "_" + opt;

		// Add arg to var list
		for (const param of params) {
			// Object keys from api
			const key = Object.keys(param)[0];
			const value = Object.values(param)[0];
			this.arguments.push(argName(key));
			this.ctx.vars[argName(key)] = typeOptions?.[key] ? typeOptions[key] : value;
		}
	}

	setTransition(transition: StateTransition): void {
		this.transition = transition;
	}

	start() {
		throw GraphError.createMsgError("Cannot start an <Event>");
	}

	serialize(): schema.Statement[] {
		const statement: schema.Statement = {
			name: this.metadata.name,
			next: this.transition?.next,
			event: {
				id: this.metadata.id,
				interrupting: this.interrupting,
				type: this.metadata.type,
				arguments: this.arguments,
				result: this.metadata.result,
			},
		};
		return [statement];
	}
}

// Terminate shape is also handled by EndState
class EndState implements State {
	private readonly ctx: StateContext;
	private readonly category: "END" | "TERMINATE";

	private readonly metadata: {
		id: string;
		name: string;
	};

	get name() {
		return this.metadata.name;
	}

	get parent() {
		return this.ctx.parent;
	}

	constructor(ctx: StateContext) {
		if (ctx.element instanceof shapes.End && ctx.element instanceof shapes.Terminate) {
			throw GraphError.createMsgError("Element is not an <End> or <Terminate>");
		}
		this.category = ctx.element?.get("type") === shapes.Terminate.type ? "TERMINATE" : "END";
		this.ctx = ctx;
		this.metadata = {
			id: ctx.element?.id as string,
			name: ctx.element?.attr("label/text"),
		};
		if (ctx.element?.id) {
			ctx.visitedElements[ctx.element.id] = true;
		}
	}

	setTransition(_transition: StateTransition): void {
		// do nothing
	}

	start() {
		throw GraphError.createMsgError("Cannot start an <End>");
	}

	serialize(): schema.Statement[] {
		const statement: schema.Statement = {
			name: this.metadata.name,
			end: {
				id: this.metadata.id,
				terminate: this.category === "TERMINATE",
			},
		};
		return [statement];
	}
}

class SwitchStateCase implements Transitionable {
	private readonly name: string;
	private readonly conditionValue: schema.Conditional;
	private transition: StateTransition;

	constructor(name: string, conditionValue: schema.Conditional, next?: string) {
		this.name = name;
		this.conditionValue = conditionValue;
		this.transition = { next };
	}

	setTransition(transition: StateTransition): void {
		this.transition = transition;
	}

	serialize(): schema.SwitchCase {
		return {
			name: this.name,
			next: this.transition.next,
			conditional: this.conditionValue,
		};
	}
}

class SwitchState implements State {
	private readonly ctx: StateContext;
	private readonly metadata: { id: string; name: string };

	private readonly cases: SwitchStateCase[] = [];

	private default?: StateTransition = {};
	private readonly localStates: State[] = [];

	get name() {
		return this.metadata.name;
	}

	get parent() {
		return this.ctx.parent;
	}

	constructor(ctx: StateContext) {
		if (!ctx.element) {
			throw GraphError.createMsgError("Element is not a <Switch>");
		}
		this.ctx = ctx;
		this.metadata = {
			id: ctx.element.id as string,
			name: ctx.element.attr("label/text"),
		};
		ctx.visitedElements[ctx.element.id] = true;
	}

	setTransition(transition: StateTransition): void {
		this.default = transition;
	}

	setDefaultFlow(el: shapes.Switch): void {
		const defaultFlow = el.defaultFlow();
		if (defaultFlow) {
			const defaultTarget = defaultFlow.getTargetElement();
			if (defaultTarget) {
				const state = this.ctx.createState("sequence", {
					...this.ctx,
					parent: this,
				});
				this.localStates.push(state);
				this.ctx.taskQueue.enqueue({ start: defaultTarget, state, prevTransition: this });
			} else {
				throw GraphError.createCellError("Default <Flow> has no target", el);
			}
		}
	}

	setConditionalFlow(el: shapes.Switch, flow: shapes.Flow): void {
		const targetElement = flow.getTargetElement();
		if (!targetElement) {
			throw GraphError.createCellError("Conditional <Flow> has no target", el);
		}

		const state = this.ctx.createState("sequence", {
			...this.ctx,
			parent: this,
		});

		const isAdvanced = flow.attr("condition/tab") === TAB_VALUE.advanced;

		let input = "";
		if (isAdvanced) {
			if (!flow.attr("condition/advanced")) {
				throw GraphError.createCellError("<Flow> has no condition defined", el);
			}
		} else {
			input = flow.getVariableName();
			if (!input) {
				throw GraphError.createCellError("<Flow> has no input variable defined", el);
			}
		}

		flow.updateConditionalLabel();
		const label = flow.label(0).attrs?.label?.text;
		if (!label) {
			throw GraphError.createCellError("<Flow> has no label defined", el);
		} else {
			const conditionValue: schema.Conditional = isAdvanced
				? {
						advance: { expression: flow.attr("condition/advanced") },
					}
				: {
						basic: {
							input,
							operator: flow.attr("condition/operator") as schema.ConditionalBasicOperator,
							value: flow.attr("condition/value") as string | boolean | number,
						},
					};

			// Connection to different state
			const newCase = new SwitchStateCase(label, conditionValue);
			this.cases.push(newCase);
			this.localStates.push(state);
			this.ctx.taskQueue.enqueue({ state, start: targetElement, prevTransition: newCase });
		}
	}

	start(el: shapes.Switch): void {
		const graph = el.graph;

		// name
		this.metadata.name = el.attr("label/text");

		// set default if exist
		this.setDefaultFlow(el);

		// Check availability of conditional flow
		if (el.orderDict.values().next().done) {
			throw GraphError.createCellError("<Switch> has no condition defined", el);
		}

		// iterate over conditional flows and set them
		for (const [_, val] of el.orderDict) {
			const flow = graph.getCell(val) as shapes.Flow;
			this.setConditionalFlow(el, flow);
		}
	}

	serialize(): schema.Statement[] {
		const result: schema.Statement[] = [
			{
				name: this.metadata.name,
				switch: {
					id: this.metadata.id,
					cases: this.cases.map((c) => c.serialize()),
					default: this.default,
				},
			},
		];

		for (const state of this.localStates) {
			result.push(...state.serialize());
		}

		return result;
	}
}

class ParallelStateBranch implements Transitionable {
	private transition: StateTransition;

	constructor(next?: string) {
		this.transition = { next };
	}

	setTransition(transition: StateTransition): void {
		this.transition = transition;
	}

	serialize(): schema.ParallelBranch {
		return {
			next: this.transition.next,
		};
	}
}

class ParallelState implements State {
	private readonly ctx: StateContext;
	private readonly metadata: {
		id: string;
		name: string;
		forkGate?: ParallelState;
		jointGate?: ParallelState;
	};
	private transition: StateTransition = { next: undefined };

	private readonly branches: ParallelStateBranch[] = [];
	private readonly localStates: State[] = [];

	/**
	 * Name getter. Parallel name getter returns nothing if state was created from a join gate
	 * @returns State name or undefined if state was created from a join gate
	 */
	get name(): string | undefined {
		return this.metadata.forkGate ? undefined : this.metadata.name;
	}

	get parent() {
		return this.ctx.parent;
	}

	/**
	 * Get join gate state
	 * @param ctx - State context
	 * @returns ParallelState of join gate
	 */
	getJoinGate(ctx: StateContext): ParallelState {
		if (!this.metadata.jointGate) {
			this.metadata.jointGate = new ParallelState(ctx, this);
		}
		return this.metadata.jointGate;
	}

	constructor(ctx: StateContext, forkGate?: ParallelState) {
		if (!ctx.element) {
			throw GraphError.createMsgError("Element is not a <Parallel>");
		}
		this.ctx = ctx;
		this.metadata = {
			id: ctx.element.id as string,
			name: ctx.element.attr("label/text"),
			forkGate,
		};

		if (!forkGate) {
			ctx.visitedElements[ctx.element.id] = true;
		}
	}

	setTransition(transition: StateTransition): void {
		if (this.metadata.forkGate) {
			this.metadata.forkGate?.setTransition(transition);
		} else {
			this.transition = transition;
		}
	}

	start(start: dia.Element): void {
		// Stop instantly if is join gate
		if (this.metadata.forkGate?.parent) {
			const neighbor = start.graph.getNeighbors(start, { outbound: true })[0];
			this.setTransition({ next: neighbor.attr("label/text") });
			this.ctx.taskQueue.enqueue({ start: neighbor, state: this.metadata.forkGate.parent });
			return;
		}

		const neighbors = start.graph.getNeighbors(start, { outbound: true });
		if (neighbors.length === 0) {
			throw GraphError.createCellError("<Parallel> has no outgoing <Flow>", start);
		}

		neighbors.forEach((neighbor) => {
			const state = this.ctx.createState("sequence", {
				...this.ctx,
				parent: this,
			});
			const newBranch = new ParallelStateBranch();
			this.branches.push(newBranch);
			this.localStates.push(state);
			this.ctx.taskQueue.enqueue({ start: neighbor, state, prevTransition: newBranch });
		});
	}

	serialize(): schema.Statement[] {
		if (this.metadata.forkGate) {
			return [];
		}

		const result: schema.Statement[] = [];
		const parallelStmt: schema.Statement = {
			name: this.metadata.name,
			next: this.transition?.next,
			parallel: {
				id: this.metadata.id,
				branches: this.branches.map((c) => c.serialize()),
			},
		};

		// Push the parallel first
		result.push(parallelStmt);

		// Then push the branch elements
		for (const state of this.localStates) {
			result.push(...state.serialize());
		}

		return result;
	}
}

// Since the Switch loopback update, sequence state is no longer represented on Statement.
// Now it only returns an array of Statements
class SequenceState implements State {
	private readonly ctx: StateContext;
	private readonly states: State[] = [];

	get name() {
		return this.states[0].name;
	}

	get parent() {
		return this.ctx.parent;
	}

	constructor(ctx: StateContext) {
		this.ctx = ctx;
	}

	setTransition(_: StateTransition): void {
		// do nothing
	}

	start(el: dia.Element, prevTransition?: Transitionable) {
		// Only set transition if element has been visited before
		if (this.ctx.visitedElements[el.id]) {
			prevTransition?.setTransition({ next: el.attr("label/text") });
			return;
		}

		let state: State | undefined;

		// Parallel must go first because of the joint gate
		if (el.get("type") === shapes.Parallel.type) {
			state = this.ctx.createState("parallel", {
				...this.ctx,
				element: el,
				parent: this,
			});

			this.states.push(state);
			this.ctx.taskQueue.enqueue({ state, start: el });
			// Set transition of the previous state
			prevTransition?.setTransition({ next: state.name });

			return;
		}

		// Switch
		if (el.get("type") === shapes.Switch.type) {
			state = this.ctx.createState("switch", {
				...this.ctx,
				element: el,
				parent: this,
			});
			this.states.push(state);
			this.ctx.taskQueue.enqueue({ state, start: el, prevTransition: prevTransition });
			// Set transition of the previous state
			prevTransition?.setTransition({ next: state.name });
			return;
		}

		// Check end element
		if (el.get("type") === shapes.End.type || el.get("type") === shapes.Terminate.type) {
			state = this.ctx.createState("end", {
				...this.ctx,
				element: el,
				parent: this,
			});

			this.states.push(state);
			prevTransition?.setTransition({ next: el.attr("label/text") });
			return;
		}

		// If is activity, push it to the state list
		if (
			el.get("type") === shapes.Activity.type ||
			el.get("type") === shapes.Form.type ||
			el.get("type") === shapes.CallActivity.type
		) {
			state = this.ctx.createState("activity", {
				...this.ctx,
				element: el,
				parent: this,
			});
			this.states.push(state);
			// Set transition of the previous state
			prevTransition?.setTransition({ next: state.name });

			// Find embedded events in activity and push to states list
			const cells = el.getEmbeddedCells();
			for (const val of cells) {
				const element = val as dia.Element;
				const event = this.ctx.createState("event", {
					...this.ctx,
					element: element,
					parent: this,
				});
				this.states.push(event);
				const neighbors = val.graph.getNeighbors(val as dia.Element, { outbound: true });
				if (neighbors.length > 1) {
					throw GraphError.createCellError("<Event> cannot have more than one outgoing <Flow>", el);
				}
				if (neighbors.length === 0) {
					throw GraphError.createCellError("<Event> must have an outgoing <Flow>", el);
				}
				this.start(neighbors[0], event);
			}
		}

		// If is event, push it to the state list
		if (el.get("type") === shapes.Event.type) {
			state = this.ctx.createState("event", {
				...this.ctx,
				element: el,
				parent: this,
			});
			this.states.push(state);
			// Set transition of the previous state
			prevTransition?.setTransition({ next: state.name });
		}

		// Do it recursively until it ends
		const neighbors = el.graph.getNeighbors(el, { outbound: true });
		if (neighbors.length === 1) {
			this.start(neighbors[0], state);
		}
	}

	serialize(): schema.Statement[] {
		const result: schema.Statement[] = [];
		for (const state of this.states) {
			result.push(...state.serialize());
		}

		return result;
	}
}

function checkStartElement(element: shapes.Start, inboundLinks: dia.Link[], outboundLinks: dia.Link[]) {
	if (inboundLinks.length > 0) {
		throw GraphError.createCellError("<Start> cannot have an incoming <Flow>", element);
	}
	if (outboundLinks.length === 0) {
		throw GraphError.createCellError("<Start> must have an outgoing <Flow>", element);
	}
}

function checkEndOrTerminateElement(
	element: shapes.End | shapes.Terminate,
	inboundLinks: dia.Link[],
	outboundLinks: dia.Link[]
) {
	const elementType = element instanceof shapes.End ? "<End>" : "<Terminate>";
	if (outboundLinks.length > 0) {
		throw GraphError.createCellError(`${elementType} cannot have an outgoing <Flow>`, element);
	}
	if (inboundLinks.length === 0) {
		throw GraphError.createCellError(`${elementType} must have an incoming <Flow>`, element);
	}
}

function checkParallelOrSwitchElement(
	element: shapes.Parallel | shapes.Switch,
	inboundLinks: dia.Link[],
	outboundLinks: dia.Link[]
) {
	const elementType = element instanceof shapes.Parallel ? "<Parallel>" : "<Switch>";
	if (inboundLinks.length === 0 || outboundLinks.length === 0) {
		throw GraphError.createCellError(`${elementType} must have an incoming and outgoing <Flow>`, element);
	}
}

function checkActivityOrEventElement(
	element: shapes.ActivityBase | shapes.Event,
	inboundLinks: dia.Link[],
	outboundLinks: dia.Link[]
) {
	const elementType = element instanceof shapes.ActivityBase ? "<Activity>" : "<Event>";
	if (inboundLinks.length === 0) {
		throw GraphError.createCellError(`${elementType} must have an incoming <Flow>`, element);
	}
	if (outboundLinks.length === 0) {
		throw GraphError.createCellError(`${elementType} must have an outgoing <Flow>`, element);
	}
	if (outboundLinks.length > 1) {
		throw GraphError.createCellError(`${elementType} cannot have more than one outgoing <Flow>`, element);
	}
}

function checkElementError(element: dia.Element) {
	const inboundLinks = element.graph.getConnectedLinks(element, { inbound: true });
	const outboundLinks = element.graph.getConnectedLinks(element, { outbound: true });

	switch (true) {
		case element instanceof shapes.Start: {
			checkStartElement(element, inboundLinks, outboundLinks);
			break;
		}
		case element instanceof shapes.End:
		case element instanceof shapes.Terminate: {
			checkEndOrTerminateElement(element, inboundLinks, outboundLinks);
			break;
		}
		case element instanceof shapes.Parallel:
		case element instanceof shapes.Switch: {
			checkParallelOrSwitchElement(element, inboundLinks, outboundLinks);
			break;
		}
		case element instanceof shapes.Event:
		case element instanceof shapes.ActivityBase: {
			checkActivityOrEventElement(element, inboundLinks, outboundLinks);
		}
	}
}

function getCreateState() {
	const visitedParallels: Record<string, ParallelState> = {};

	return function createState(type: AvailableState, ctx: StateContext): State {
		switch (type) {
			case "sequence":
				return new SequenceState(ctx);
			case "switch":
				return new SwitchState(ctx);
			case "parallel": {
				const state = new ParallelState(ctx);

				// If parallel with the same label has been visited before, then it is a join gate
				const label = ctx.element?.attr("label/text") as string;
				if (!visitedParallels[label]) {
					visitedParallels[label] = state;
					return state;
				} else {
					return visitedParallels[label].getJoinGate(ctx);
				}
			}
			case "activity":
				return new ActivityState(ctx);
			case "event":
				return new EventState(ctx);
			case "end":
				return new EndState(ctx);
		}
	};
}

export function convert(graph: dia.Graph, metadata: Metadata): schema.WorkflowSchemaJson {
	// Find start point
	const elements = graph.getElements();
	let start: dia.Element | null = null;
	for (const element of elements) {
		checkElementError(element);
		if (element instanceof shapes.Start) {
			start = element;
		}
	}

	if (!start) {
		throw GraphError.createMsgError("Graph has no <Start>");
	}

	// Initialize data structures
	const vars: VarStorage = {};
	const taskQueue = new Queue<InQueueState>();
	const visitedElements: Record<string, boolean> = {};
	const createState = getCreateState();
	const root = new SequenceState({
		vars,
		createState,
		visitedElements,
		taskQueue,
	});

	// Queue first state
	taskQueue.enqueue({ state: root, start });

	// Start executing tasks
	while (taskQueue.length > 0) {
		const { state, start, prevTransition: prevState } = taskQueue.dequeue() as InQueueState;
		state.start(start, prevState);
	}

	const states = root.serialize();

	return {
		variables: vars,
		specVersion: metadata.specVersion,
		start: states[0].name,
		states,
	};
}
