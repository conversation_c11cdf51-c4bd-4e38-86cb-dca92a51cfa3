package postgresql

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/mssfoobar/app/wfe/internal/model/sqlplugin"
)

const (
	createServiceEventQuery = `INSERT INTO service_event (id, service_name, event_type, event_icon, 
								event_param, event_result) VALUES ($1, $2, $3, $4, $5, $6)`

	updateServiceEventQuery = `UPDATE service_event SET service_name = $1, event_type = $2, event_icon = $3,
								event_param = $4, event_result = $5 WHERE id = $6`

	getServiceEventQuery = `SELECT * FROM service_event WHERE id = $1`

	deleteServiceEventByIdQuery = `DELETE FROM service_event WHERE id = $1`

	listServiceEvent        = `SELECT * FROM service_event LIMIT $1 OFFSET $2`
	listServiceEventOrderBy = `SELECT * FROM service_event ORDER BY %s LIMIT $1 OFFSET $2`

	getTotalCountServiceEvent = `SELECT COUNT(*) FROM service_event`
)

func (pdb *db) InsertIntoServiceEvent(
	ctx context.Context,
	rows *sqlplugin.ServiceEventRow,
) (sql.Result, error) {
	return pdb.conn.ExecContext(
		ctx,
		createServiceEventQuery,
		rows.Id,
		rows.ServiceName,
		rows.EventType,
		rows.EventIcon,
		rows.EventParam,
		rows.EventResult,
	)
}

func (pdb *db) UpdateServiceEvent(
	ctx context.Context,
	rows *sqlplugin.ServiceEventRow,
) (sql.Result, error) {
	return pdb.conn.ExecContext(
		ctx,
		updateServiceEventQuery,
		rows.ServiceName,
		rows.EventType,
		rows.EventIcon,
		rows.EventParam,
		rows.EventResult,
		rows.Id,
	)
}

func (pdb *db) SelectFromServiceEvent(
	ctx context.Context,
	filter sqlplugin.ServiceEventFilter,
) (*sqlplugin.ServiceEventRow, error) {
	var row sqlplugin.ServiceEventRow
	err := pdb.conn.GetContext(ctx, &row, getServiceEventQuery, filter.Id)
	return &row, err
}

func (pdb *db) ListFromServiceEvent(
	ctx context.Context,
	filter sqlplugin.ServiceEventPaginateFilter,
) ([]sqlplugin.ServiceEventRow, error) {
	switch {
	case filter.OrderBy != "":
		return pdb.listFromServiceEventOrderBy(ctx, filter)
	default:
		return pdb.listFromServiceEvent(ctx, filter)
	}
}

func (pdb *db) listFromServiceEvent(
	ctx context.Context,
	filter sqlplugin.ServiceEventPaginateFilter,
) ([]sqlplugin.ServiceEventRow, error) {
	var rows []sqlplugin.ServiceEventRow
	err := pdb.conn.SelectContext(ctx,
		&rows,
		listServiceEvent,
		filter.Limit,
		filter.Offset,
	)
	return rows, err
}

func (pdb *db) listFromServiceEventOrderBy(
	ctx context.Context,
	filter sqlplugin.ServiceEventPaginateFilter,
) ([]sqlplugin.ServiceEventRow, error) {
	var rows []sqlplugin.ServiceEventRow
	err := pdb.conn.SelectContext(ctx,
		&rows,
		fmt.Sprintf(listServiceEventOrderBy, filter.OrderBy),
		filter.Limit,
		filter.Offset,
	)
	return rows, err
}

func (pdb *db) DeleteFromServiceEvent(
	ctx context.Context,
	filter sqlplugin.ServiceEventFilter,
) (sql.Result, error) {
	return pdb.conn.ExecContext(ctx, deleteServiceEventByIdQuery, filter.Id)
}

func (pdb *db) CountFromServiceEvent(
	ctx context.Context,
) (int, error) {
	var total int
	err := pdb.conn.GetContext(ctx, &total, getTotalCountServiceEvent)
	return total, err
}
