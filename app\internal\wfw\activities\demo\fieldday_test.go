package demo

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/mssfoobar/aoh-golib/temporal/propagator"
	"github.com/mssfoobar/app/wfe/internal/config"
	"github.com/stretchr/testify/assert"
	"go.temporal.io/api/common/v1"
	"go.temporal.io/sdk/converter"
	"go.temporal.io/sdk/testsuite"
	"go.temporal.io/sdk/workflow"
)

type mockAIModel struct {
	ChatFunc func(context.Context, string) (string, error)
}

func (m *mockAIModel) Chat(ctx context.Context, prompt string) (string, error) {
	return m.ChatFunc(ctx, prompt)
}

func TestActivities_SummarizeIncident(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(rw http.ResponseWriter, req *http.Request) {
		//nolint:errcheck
		rw.Write([]byte(`{"data":[{"id": "123", "created_at": "2023-01-01T00:00:00Z", "Description": "test", "Priority": 1, "Contact": "123"}]}`))
	}))
	defer server.Close()

	a := &Activities{
		ims: &IMS{
			getIncidentApi: server.URL,
		},
		aiModel: &mockAIModel{
			ChatFunc: func(ctx context.Context, prompt string) (string, error) {
				return "hello", nil
			},
		},
	}
	s := &testsuite.WorkflowTestSuite{}
	s.SetContextPropagators([]workflow.ContextPropagator{propagator.NewContextPropagator()})
	env := s.NewTestActivityEnvironment()
	payload, _ := converter.GetDefaultDataConverter().ToPayload(map[string]any{"incidentId": "INC-20231212-0001"})
	env.SetHeader(&common.Header{
		Fields: map[string]*common.Payload{
			"custom-header": payload,
		},
	})
	env.RegisterActivity(a)
	got, err := env.ExecuteActivity(a.SummarizeIncident, "123")
	assert.NoError(t, err)
	var result string
	_ = got.Get(&result)
	fmt.Println(result)
}

func TestActivities_GetTripParticipants(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(rw http.ResponseWriter, req *http.Request) {
		//nolint:errcheck
		rw.Write([]byte(`{"Aoh_ims_incident":[{"Trip__incidents":[{"Trip":{"Participants":[{"Name": "test", "Contact": "123"}]}}]}]}`))
	}))
	defer server.Close()

	a := &Activities{
		hasura: &Hasura{
			config.Hasura{AdminKey: ""},
		},
	}
	s := &testsuite.WorkflowTestSuite{}
	s.SetContextPropagators([]workflow.ContextPropagator{propagator.NewContextPropagator()})
	env := s.NewTestActivityEnvironment()
	payload, _ := converter.GetDefaultDataConverter().ToPayload(map[string]any{"incidentId": "INC-20231212-0001"})
	env.SetHeader(&common.Header{
		Fields: map[string]*common.Payload{
			"custom-header": payload,
		},
	})
	env.RegisterActivity(a)
	got, err := env.ExecuteActivity(a.GetTripParticipants, server.URL)
	assert.NoError(t, err)
	var result string
	_ = got.Get(&result)
	fmt.Println(result)
}

func TestNew(t *testing.T) {
	assert.NotNil(t, New(Option{}))
}
