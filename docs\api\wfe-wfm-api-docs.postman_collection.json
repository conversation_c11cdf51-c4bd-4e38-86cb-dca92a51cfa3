{"info": {"_postman_id": "ab3ca7f3-8123-4baa-b840-81a5eef4567a", "name": "AOH - WFE", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "22906264", "_collection_link": "https://agilrad.postman.co/workspace/AGIL-Ops-Hub~3d088e92-5274-41e4-8f8b-fbe3f46e43f9/collection/22906264-ab3ca7f3-8123-4baa-b840-81a5eef4567a?action=share&source=collection_link&creator=22906264"}, "item": [{"name": "Workflow Template Save", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"testWorkflow\",\n    \"workflow_json\": {\n        \"variables\": {\n            \"Activity_JSON\": \"{\\\"key\\\": \\\"abc\\\"}\",\n            \"Activity1_String\": \"=Result_Activity.key\"\n        },\n        \"specVersion\": \"2.0\",\n        \"start\": \"Activity\",\n        \"states\": [\n            {\n                \"name\": \"Activity\",\n                \"next\": \"Activity1\",\n                \"activity\": {\n                    \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\",\n                    \"type\": \"SampleActivityJson\",\n                    \"arguments\": [\n                        \"Activity_JSON\"\n                    ],\n                    \"boundaryEvents\": [],\n                    \"result\": \"Result_Activity\",\n                    \"options\": {\n                        \"startToCloseTimeout\": 300\n                    }\n                }\n            },\n            {\n                \"name\": \"Activity1\",\n                \"next\": \"End\",\n                \"activity\": {\n                    \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\",\n                    \"type\": \"SampleActivityString\",\n                    \"arguments\": [\n                        \"Activity1_String\"\n                    ],\n                    \"boundaryEvents\": [],\n                    \"result\": \"Result_Activity1\",\n                    \"options\": {\n                        \"startToCloseTimeout\": 300\n                    }\n                }\n            },\n            {\n                \"name\": \"End\",\n                \"end\": {\n                    \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\",\n                    \"terminate\": false\n                }\n            }\n        ]\n    },\n    \"designer_json\": {\n        \"type\": \"bpmn\",\n        \"cells\": [\n            {\n                \"type\": \"wf.Start\",\n                \"size\": {\n                    \"width\": 40,\n                    \"height\": 40\n                },\n                \"position\": {\n                    \"x\": 190,\n                    \"y\": 230\n                },\n                \"angle\": 0,\n                \"z\": 1,\n                \"id\": \"a6fc6b36-afe3-4d39-949b-eefd50233eab\",\n                \"attrs\": {}\n            },\n            {\n                \"type\": \"wf.Activity\",\n                \"size\": {\n                    \"width\": 100,\n                    \"height\": 80\n                },\n                \"markup\": [\n                    {\n                        \"tagName\": \"rect\",\n                        \"selector\": \"background\"\n                    },\n                    {\n                        \"tagName\": \"image\",\n                        \"selector\": \"icon\"\n                    },\n                    {\n                        \"tagName\": \"path\",\n                        \"selector\": \"border\"\n                    },\n                    {\n                        \"tagName\": \"text\",\n                        \"selector\": \"label\"\n                    },\n                    {\n                        \"tagName\": \"g\",\n                        \"selector\": \"markers\"\n                    },\n                    {\n                        \"tagName\": \"text\",\n                        \"selector\": \"type\",\n                        \"attributes\": {\n                            \"fill\": \"hsl(var(--primary))\"\n                        }\n                    }\n                ],\n                \"position\": {\n                    \"x\": 300,\n                    \"y\": 210\n                },\n                \"angle\": 0,\n                \"z\": 2,\n                \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\",\n                \"subscribers\": {},\n                \"attrs\": {\n                    \"label\": {\n                        \"text\": \"Activity\"\n                    },\n                    \"type\": {\n                        \"text\": \"SampleActivityJson\"\n                    },\n                    \"data\": {\n                        \"result\": \"Result_Activity\",\n                        \"resultType\": \"object\",\n                        \"taskResult\": {\n                            \"object\": {}\n                        },\n                        \"typeOptions\": {\n                            \"JSON\": \"{\\\"key\\\": \\\"abc\\\"}\"\n                        }\n                    }\n                }\n            },\n            {\n                \"type\": \"wf.Flow\",\n                \"source\": {\n                    \"id\": \"a6fc6b36-afe3-4d39-949b-eefd50233eab\"\n                },\n                \"target\": {\n                    \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\"\n                },\n                \"z\": 3,\n                \"id\": \"dfaf8a5e-9e5d-45d1-a09a-698438b7ef5e\",\n                \"labels\": [],\n                \"attrs\": {}\n            },\n            {\n                \"type\": \"wf.Activity\",\n                \"size\": {\n                    \"width\": 100,\n                    \"height\": 80\n                },\n                \"markup\": [\n                    {\n                        \"tagName\": \"rect\",\n                        \"selector\": \"background\"\n                    },\n                    {\n                        \"tagName\": \"image\",\n                        \"selector\": \"icon\"\n                    },\n                    {\n                        \"tagName\": \"path\",\n                        \"selector\": \"border\"\n                    },\n                    {\n                        \"tagName\": \"text\",\n                        \"selector\": \"label\"\n                    },\n                    {\n                        \"tagName\": \"g\",\n                        \"selector\": \"markers\"\n                    },\n                    {\n                        \"tagName\": \"text\",\n                        \"selector\": \"type\",\n                        \"attributes\": {\n                            \"fill\": \"hsl(var(--primary))\"\n                        }\n                    }\n                ],\n                \"position\": {\n                    \"x\": 450,\n                    \"y\": 210\n                },\n                \"angle\": 0,\n                \"z\": 6,\n                \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\",\n                \"subscribers\": {},\n                \"attrs\": {\n                    \"label\": {\n                        \"text\": \"Activity1\"\n                    },\n                    \"type\": {\n                        \"text\": \"SampleActivityString\"\n                    },\n                    \"data\": {\n                        \"result\": \"Result_Activity1\",\n                        \"resultType\": \"string\",\n                        \"taskResult\": \"string\",\n                        \"typeOptions\": {\n                            \"String\": \"=Result_Activity.key\",\n                            \"StringTmp\": \"Result_Activity.key\",\n                            \"StringValidate\": {\n                                \"message\": \"valid expression\",\n                                \"sent_at\": \"2024-12-23T09:26:08Z\"\n                            }\n                        },\n                        \"activityVariables\": {\n                            \"String\": true\n                        }\n                    }\n                }\n            },\n            {\n                \"type\": \"wf.Flow\",\n                \"source\": {\n                    \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\"\n                },\n                \"target\": {\n                    \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\"\n                },\n                \"z\": 7,\n                \"id\": \"49ef04eb-56b4-467c-bd62-8c63e89b3c80\",\n                \"labels\": [],\n                \"attrs\": {}\n            },\n            {\n                \"type\": \"wf.End\",\n                \"size\": {\n                    \"width\": 40,\n                    \"height\": 40\n                },\n                \"position\": {\n                    \"x\": 610,\n                    \"y\": 230\n                },\n                \"angle\": 0,\n                \"z\": 10,\n                \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\",\n                \"attrs\": {}\n            },\n            {\n                \"type\": \"wf.Flow\",\n                \"source\": {\n                    \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\"\n                },\n                \"target\": {\n                    \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\"\n                },\n                \"z\": 11,\n                \"id\": \"330878ee-846b-4a71-a320-d854e92b7854\",\n                \"labels\": [],\n                \"attrs\": {}\n            }\n        ],\n        \"name\": \"testWorkflow\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{WFM_ENDPOINT}}/v1/workflow_template/save", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "workflow_template", "save"]}}, "response": [{"name": "Workflow Template Save", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n  \"name\": \"testWorkflow\",\n  \"workflow_json\": {\n    \"variables\": {\n      \"Activity_JSON\": \"{\\\"key\\\": \\\"abc\\\"}\",\n      \"Activity1_String\": \"=Result_Activity.key\"\n    },\n    \"specVersion\": \"2.0\",\n    \"start\": \"Activity\",\n    \"states\": [\n      {\n        \"name\": \"Activity\",\n        \"next\": \"Activity1\",\n        \"activity\": {\n          \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\",\n          \"type\": \"SampleActivityJson\",\n          \"arguments\": [\"Activity_JSON\"],\n          \"boundaryEvents\": [],\n          \"result\": \"Result_Activity\",\n          \"options\": { \"startToCloseTimeout\": 300 }\n        }\n      },\n      {\n        \"name\": \"Activity1\",\n        \"next\": \"End\",\n        \"activity\": {\n          \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\",\n          \"type\": \"SampleActivityString\",\n          \"arguments\": [\"Activity1_String\"],\n          \"boundaryEvents\": [],\n          \"result\": \"Result_Activity1\",\n          \"options\": { \"startToCloseTimeout\": 300 }\n        }\n      },\n      {\n        \"name\": \"End\",\n        \"end\": {\n          \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\",\n          \"terminate\": false\n        }\n      }\n    ]\n  },\n  \"designer_json\": {\n    \"type\": \"bpmn\",\n    \"cells\": [\n      {\n        \"type\": \"wf.Start\",\n        \"size\": { \"width\": 40, \"height\": 40 },\n        \"position\": { \"x\": 190, \"y\": 230 },\n        \"angle\": 0,\n        \"z\": 1,\n        \"id\": \"a6fc6b36-afe3-4d39-949b-eefd50233eab\",\n        \"attrs\": {}\n      },\n      {\n        \"type\": \"wf.Activity\",\n        \"size\": { \"width\": 100, \"height\": 80 },\n        \"markup\": [\n          { \"tagName\": \"rect\", \"selector\": \"background\" },\n          { \"tagName\": \"image\", \"selector\": \"icon\" },\n          { \"tagName\": \"path\", \"selector\": \"border\" },\n          { \"tagName\": \"text\", \"selector\": \"label\" },\n          { \"tagName\": \"g\", \"selector\": \"markers\" },\n          {\n            \"tagName\": \"text\",\n            \"selector\": \"type\",\n            \"attributes\": { \"fill\": \"hsl(var(--primary))\" }\n          }\n        ],\n        \"position\": { \"x\": 300, \"y\": 210 },\n        \"angle\": 0,\n        \"z\": 2,\n        \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\",\n        \"subscribers\": {},\n        \"attrs\": {\n          \"label\": { \"text\": \"Activity\" },\n          \"type\": { \"text\": \"SampleActivityJson\" },\n          \"data\": {\n            \"result\": \"Result_Activity\",\n            \"resultType\": \"object\",\n            \"taskResult\": { \"object\": {} },\n            \"typeOptions\": { \"JSON\": \"{\\\"key\\\": \\\"abc\\\"}\" }\n          }\n        }\n      },\n      {\n        \"type\": \"wf.Flow\",\n        \"source\": { \"id\": \"a6fc6b36-afe3-4d39-949b-eefd50233eab\" },\n        \"target\": { \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\" },\n        \"z\": 3,\n        \"id\": \"dfaf8a5e-9e5d-45d1-a09a-698438b7ef5e\",\n        \"labels\": [],\n        \"attrs\": {}\n      },\n      {\n        \"type\": \"wf.Activity\",\n        \"size\": { \"width\": 100, \"height\": 80 },\n        \"markup\": [\n          { \"tagName\": \"rect\", \"selector\": \"background\" },\n          { \"tagName\": \"image\", \"selector\": \"icon\" },\n          { \"tagName\": \"path\", \"selector\": \"border\" },\n          { \"tagName\": \"text\", \"selector\": \"label\" },\n          { \"tagName\": \"g\", \"selector\": \"markers\" },\n          {\n            \"tagName\": \"text\",\n            \"selector\": \"type\",\n            \"attributes\": { \"fill\": \"hsl(var(--primary))\" }\n          }\n        ],\n        \"position\": { \"x\": 450, \"y\": 210 },\n        \"angle\": 0,\n        \"z\": 6,\n        \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\",\n        \"subscribers\": {},\n        \"attrs\": {\n          \"label\": { \"text\": \"Activity1\" },\n          \"type\": { \"text\": \"SampleActivityString\" },\n          \"data\": {\n            \"result\": \"Result_Activity1\",\n            \"resultType\": \"string\",\n            \"taskResult\": \"string\",\n            \"typeOptions\": {\n              \"String\": \"=Result_Activity.key\",\n              \"StringTmp\": \"Result_Activity.key\",\n              \"StringValidate\": {\n                \"message\": \"valid expression\",\n                \"sent_at\": \"2024-12-23T09:26:08Z\"\n              }\n            },\n            \"activityVariables\": { \"String\": true }\n          }\n        }\n      },\n      {\n        \"type\": \"wf.Flow\",\n        \"source\": { \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\" },\n        \"target\": { \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\" },\n        \"z\": 7,\n        \"id\": \"49ef04eb-56b4-467c-bd62-8c63e89b3c80\",\n        \"labels\": [],\n        \"attrs\": {}\n      },\n      {\n        \"type\": \"wf.End\",\n        \"size\": { \"width\": 40, \"height\": 40 },\n        \"position\": { \"x\": 610, \"y\": 230 },\n        \"angle\": 0,\n        \"z\": 10,\n        \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\",\n        \"attrs\": {}\n      },\n      {\n        \"type\": \"wf.Flow\",\n        \"source\": { \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\" },\n        \"target\": { \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\" },\n        \"z\": 11,\n        \"id\": \"330878ee-846b-4a71-a320-d854e92b7854\",\n        \"labels\": [],\n        \"attrs\": {}\n      }\n    ],\n    \"name\": \"testWorkflow\"\n  }\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{WFM_ENDPOINT}}/v1/workflow_template/save", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "workflow_template", "save"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Date", "value": "Thu, 16 Jan 2025 04:43:18 GMT"}, {"key": "Transfer-Encoding", "value": "chunked"}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": \"910d11d0-6617-45c9-9f2f-00ce7f5b9ed3\",\n        \"name\": \"testWorkflow\",\n        \"workflow_json\": {\n            \"start\": \"Activity\",\n            \"states\": [\n                {\n                    \"name\": \"Activity\",\n                    \"next\": \"Activity1\",\n                    \"activity\": {\n                        \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\",\n                        \"type\": \"SampleActivityJson\",\n                        \"result\": \"Result_Activity\",\n                        \"options\": {\n                            \"startToCloseTimeout\": 300\n                        },\n                        \"arguments\": [\n                            \"Activity_JSON\"\n                        ],\n                        \"boundaryEvents\": []\n                    }\n                },\n                {\n                    \"name\": \"Activity1\",\n                    \"next\": \"End\",\n                    \"activity\": {\n                        \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\",\n                        \"type\": \"SampleActivityString\",\n                        \"result\": \"Result_Activity1\",\n                        \"options\": {\n                            \"startToCloseTimeout\": 300\n                        },\n                        \"arguments\": [\n                            \"Activity1_String\"\n                        ],\n                        \"boundaryEvents\": []\n                    }\n                },\n                {\n                    \"end\": {\n                        \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\",\n                        \"terminate\": false\n                    },\n                    \"name\": \"End\"\n                }\n            ],\n            \"variables\": {\n                \"Activity_JSON\": \"{\\\"key\\\": \\\"abc\\\"}\",\n                \"Activity1_String\": \"=Result_Activity.key\"\n            },\n            \"specVersion\": \"2.0\"\n        },\n        \"designer_json\": {\n            \"name\": \"testWorkflow\",\n            \"type\": \"bpmn\",\n            \"cells\": [\n                {\n                    \"z\": 1,\n                    \"id\": \"a6fc6b36-afe3-4d39-949b-eefd50233eab\",\n                    \"size\": {\n                        \"width\": 40,\n                        \"height\": 40\n                    },\n                    \"type\": \"wf.Start\",\n                    \"angle\": 0,\n                    \"attrs\": {},\n                    \"position\": {\n                        \"x\": 190,\n                        \"y\": 230\n                    }\n                },\n                {\n                    \"z\": 2,\n                    \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\",\n                    \"size\": {\n                        \"width\": 100,\n                        \"height\": 80\n                    },\n                    \"type\": \"wf.Activity\",\n                    \"angle\": 0,\n                    \"attrs\": {\n                        \"data\": {\n                            \"result\": \"Result_Activity\",\n                            \"resultType\": \"object\",\n                            \"taskResult\": {\n                                \"object\": {}\n                            },\n                            \"typeOptions\": {\n                                \"JSON\": \"{\\\"key\\\": \\\"abc\\\"}\"\n                            }\n                        },\n                        \"type\": {\n                            \"text\": \"SampleActivityJson\"\n                        },\n                        \"label\": {\n                            \"text\": \"Activity\"\n                        }\n                    },\n                    \"markup\": [\n                        {\n                            \"tagName\": \"rect\",\n                            \"selector\": \"background\"\n                        },\n                        {\n                            \"tagName\": \"image\",\n                            \"selector\": \"icon\"\n                        },\n                        {\n                            \"tagName\": \"path\",\n                            \"selector\": \"border\"\n                        },\n                        {\n                            \"tagName\": \"text\",\n                            \"selector\": \"label\"\n                        },\n                        {\n                            \"tagName\": \"g\",\n                            \"selector\": \"markers\"\n                        },\n                        {\n                            \"tagName\": \"text\",\n                            \"selector\": \"type\",\n                            \"attributes\": {\n                                \"fill\": \"hsl(var(--primary))\"\n                            }\n                        }\n                    ],\n                    \"position\": {\n                        \"x\": 300,\n                        \"y\": 210\n                    },\n                    \"subscribers\": {}\n                },\n                {\n                    \"z\": 3,\n                    \"id\": \"dfaf8a5e-9e5d-45d1-a09a-698438b7ef5e\",\n                    \"type\": \"wf.Flow\",\n                    \"attrs\": {},\n                    \"labels\": [],\n                    \"source\": {\n                        \"id\": \"a6fc6b36-afe3-4d39-949b-eefd50233eab\"\n                    },\n                    \"target\": {\n                        \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\"\n                    }\n                },\n                {\n                    \"z\": 6,\n                    \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\",\n                    \"size\": {\n                        \"width\": 100,\n                        \"height\": 80\n                    },\n                    \"type\": \"wf.Activity\",\n                    \"angle\": 0,\n                    \"attrs\": {\n                        \"data\": {\n                            \"result\": \"Result_Activity1\",\n                            \"resultType\": \"string\",\n                            \"taskResult\": \"string\",\n                            \"typeOptions\": {\n                                \"String\": \"=Result_Activity.key\",\n                                \"StringTmp\": \"Result_Activity.key\",\n                                \"StringValidate\": {\n                                    \"message\": \"valid expression\",\n                                    \"sent_at\": \"2024-12-23T09:26:08Z\"\n                                }\n                            },\n                            \"activityVariables\": {\n                                \"String\": true\n                            }\n                        },\n                        \"type\": {\n                            \"text\": \"SampleActivityString\"\n                        },\n                        \"label\": {\n                            \"text\": \"Activity1\"\n                        }\n                    },\n                    \"markup\": [\n                        {\n                            \"tagName\": \"rect\",\n                            \"selector\": \"background\"\n                        },\n                        {\n                            \"tagName\": \"image\",\n                            \"selector\": \"icon\"\n                        },\n                        {\n                            \"tagName\": \"path\",\n                            \"selector\": \"border\"\n                        },\n                        {\n                            \"tagName\": \"text\",\n                            \"selector\": \"label\"\n                        },\n                        {\n                            \"tagName\": \"g\",\n                            \"selector\": \"markers\"\n                        },\n                        {\n                            \"tagName\": \"text\",\n                            \"selector\": \"type\",\n                            \"attributes\": {\n                                \"fill\": \"hsl(var(--primary))\"\n                            }\n                        }\n                    ],\n                    \"position\": {\n                        \"x\": 450,\n                        \"y\": 210\n                    },\n                    \"subscribers\": {}\n                },\n                {\n                    \"z\": 7,\n                    \"id\": \"49ef04eb-56b4-467c-bd62-8c63e89b3c80\",\n                    \"type\": \"wf.Flow\",\n                    \"attrs\": {},\n                    \"labels\": [],\n                    \"source\": {\n                        \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\"\n                    },\n                    \"target\": {\n                        \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\"\n                    }\n                },\n                {\n                    \"z\": 10,\n                    \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\",\n                    \"size\": {\n                        \"width\": 40,\n                        \"height\": 40\n                    },\n                    \"type\": \"wf.End\",\n                    \"angle\": 0,\n                    \"attrs\": {},\n                    \"position\": {\n                        \"x\": 610,\n                        \"y\": 230\n                    }\n                },\n                {\n                    \"z\": 11,\n                    \"id\": \"330878ee-846b-4a71-a320-d854e92b7854\",\n                    \"type\": \"wf.Flow\",\n                    \"attrs\": {},\n                    \"labels\": [],\n                    \"source\": {\n                        \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\"\n                    },\n                    \"target\": {\n                        \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\"\n                    }\n                }\n            ]\n        },\n        \"editable\": true,\n        \"created_at\": \"2024-12-23T09:26:17.229735Z\",\n        \"created_by\": \"f67cb8f5-0645-444d-a4bd-c61aaf1b2db0\",\n        \"updated_at\": \"2025-01-16T04:43:18.983275Z\",\n        \"updated_by\": \"f67cb8f5-0645-444d-a4bd-c61aaf1b2db0\",\n        \"tenant_id\": \"430d2014-6e57-4e8a-ad75-484aa276a5cc\",\n        \"occ_lock\": 1\n    },\n    \"sent_at\": \"2025-01-16T04:43:18Z\"\n}"}]}, {"name": "Workflow Template Publish", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n  \"name\": \"testWorkflow\",\n  \"workflow_json\": {\n    \"variables\": {\n      \"Activity_JSON\": \"{\\\"key\\\": \\\"abc\\\"}\",\n      \"Activity1_String\": \"=Result_Activity.key\"\n    },\n    \"specVersion\": \"2.0\",\n    \"start\": \"Activity\",\n    \"states\": [\n      {\n        \"name\": \"Activity\",\n        \"next\": \"Activity1\",\n        \"activity\": {\n          \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\",\n          \"type\": \"SampleActivityJson\",\n          \"arguments\": [\"Activity_JSON\"],\n          \"boundaryEvents\": [],\n          \"result\": \"Result_Activity\",\n          \"options\": { \"startToCloseTimeout\": 300 }\n        }\n      },\n      {\n        \"name\": \"Activity1\",\n        \"next\": \"End\",\n        \"activity\": {\n          \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\",\n          \"type\": \"SampleActivityString\",\n          \"arguments\": [\"Activity1_String\"],\n          \"boundaryEvents\": [],\n          \"result\": \"Result_Activity1\",\n          \"options\": { \"startToCloseTimeout\": 300 }\n        }\n      },\n      {\n        \"name\": \"End\",\n        \"end\": {\n          \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\",\n          \"terminate\": false\n        }\n      }\n    ]\n  },\n  \"designer_json\": {\n    \"type\": \"bpmn\",\n    \"cells\": [\n      {\n        \"type\": \"wf.Start\",\n        \"size\": { \"width\": 40, \"height\": 40 },\n        \"position\": { \"x\": 190, \"y\": 230 },\n        \"angle\": 0,\n        \"z\": 1,\n        \"id\": \"a6fc6b36-afe3-4d39-949b-eefd50233eab\",\n        \"attrs\": {}\n      },\n      {\n        \"type\": \"wf.Activity\",\n        \"size\": { \"width\": 100, \"height\": 80 },\n        \"markup\": [\n          { \"tagName\": \"rect\", \"selector\": \"background\" },\n          { \"tagName\": \"image\", \"selector\": \"icon\" },\n          { \"tagName\": \"path\", \"selector\": \"border\" },\n          { \"tagName\": \"text\", \"selector\": \"label\" },\n          { \"tagName\": \"g\", \"selector\": \"markers\" },\n          {\n            \"tagName\": \"text\",\n            \"selector\": \"type\",\n            \"attributes\": { \"fill\": \"hsl(var(--primary))\" }\n          }\n        ],\n        \"position\": { \"x\": 300, \"y\": 210 },\n        \"angle\": 0,\n        \"z\": 2,\n        \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\",\n        \"subscribers\": {},\n        \"attrs\": {\n          \"label\": { \"text\": \"Activity\" },\n          \"type\": { \"text\": \"SampleActivityJson\" },\n          \"data\": {\n            \"result\": \"Result_Activity\",\n            \"resultType\": \"object\",\n            \"taskResult\": { \"object\": {} },\n            \"typeOptions\": { \"JSON\": \"{\\\"key\\\": \\\"abc\\\"}\" }\n          }\n        }\n      },\n      {\n        \"type\": \"wf.Flow\",\n        \"source\": { \"id\": \"a6fc6b36-afe3-4d39-949b-eefd50233eab\" },\n        \"target\": { \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\" },\n        \"z\": 3,\n        \"id\": \"dfaf8a5e-9e5d-45d1-a09a-698438b7ef5e\",\n        \"labels\": [],\n        \"attrs\": {}\n      },\n      {\n        \"type\": \"wf.Activity\",\n        \"size\": { \"width\": 100, \"height\": 80 },\n        \"markup\": [\n          { \"tagName\": \"rect\", \"selector\": \"background\" },\n          { \"tagName\": \"image\", \"selector\": \"icon\" },\n          { \"tagName\": \"path\", \"selector\": \"border\" },\n          { \"tagName\": \"text\", \"selector\": \"label\" },\n          { \"tagName\": \"g\", \"selector\": \"markers\" },\n          {\n            \"tagName\": \"text\",\n            \"selector\": \"type\",\n            \"attributes\": { \"fill\": \"hsl(var(--primary))\" }\n          }\n        ],\n        \"position\": { \"x\": 450, \"y\": 210 },\n        \"angle\": 0,\n        \"z\": 6,\n        \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\",\n        \"subscribers\": {},\n        \"attrs\": {\n          \"label\": { \"text\": \"Activity1\" },\n          \"type\": { \"text\": \"SampleActivityString\" },\n          \"data\": {\n            \"result\": \"Result_Activity1\",\n            \"resultType\": \"string\",\n            \"taskResult\": \"string\",\n            \"typeOptions\": {\n              \"String\": \"=Result_Activity.key\",\n              \"StringTmp\": \"Result_Activity.key\",\n              \"StringValidate\": {\n                \"message\": \"valid expression\",\n                \"sent_at\": \"2024-12-23T09:26:08Z\"\n              }\n            },\n            \"activityVariables\": { \"String\": true }\n          }\n        }\n      },\n      {\n        \"type\": \"wf.Flow\",\n        \"source\": { \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\" },\n        \"target\": { \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\" },\n        \"z\": 7,\n        \"id\": \"49ef04eb-56b4-467c-bd62-8c63e89b3c80\",\n        \"labels\": [],\n        \"attrs\": {}\n      },\n      {\n        \"type\": \"wf.End\",\n        \"size\": { \"width\": 40, \"height\": 40 },\n        \"position\": { \"x\": 610, \"y\": 230 },\n        \"angle\": 0,\n        \"z\": 10,\n        \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\",\n        \"attrs\": {}\n      },\n      {\n        \"type\": \"wf.Flow\",\n        \"source\": { \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\" },\n        \"target\": { \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\" },\n        \"z\": 11,\n        \"id\": \"330878ee-846b-4a71-a320-d854e92b7854\",\n        \"labels\": [],\n        \"attrs\": {}\n      }\n    ],\n    \"name\": \"testWorkflow\"\n  }\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{WFM_ENDPOINT}}/v1/workflow_template/publish", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "workflow_template", "publish"]}}, "response": [{"name": "Workflow Template Publish", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"testWorkflow\",\n    \"workflow_json\": {\n        \"variables\": {\n            \"Activity_JSON\": \"{\\\"key\\\": \\\"abc\\\"}\",\n            \"Activity1_String\": \"=Result_Activity.key\"\n        },\n        \"specVersion\": \"2.0\",\n        \"start\": \"Activity\",\n        \"states\": [\n            {\n                \"name\": \"Activity\",\n                \"next\": \"Activity1\",\n                \"activity\": {\n                    \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\",\n                    \"type\": \"SampleActivityJson\",\n                    \"arguments\": [\n                        \"Activity_JSON\"\n                    ],\n                    \"boundaryEvents\": [],\n                    \"result\": \"Result_Activity\",\n                    \"options\": {\n                        \"startToCloseTimeout\": 300\n                    }\n                }\n            },\n            {\n                \"name\": \"Activity1\",\n                \"next\": \"End\",\n                \"activity\": {\n                    \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\",\n                    \"type\": \"SampleActivityString\",\n                    \"arguments\": [\n                        \"Activity1_String\"\n                    ],\n                    \"boundaryEvents\": [],\n                    \"result\": \"Result_Activity1\",\n                    \"options\": {\n                        \"startToCloseTimeout\": 300\n                    }\n                }\n            },\n            {\n                \"name\": \"End\",\n                \"end\": {\n                    \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\",\n                    \"terminate\": false\n                }\n            }\n        ]\n    },\n    \"designer_json\": {\n        \"type\": \"bpmn\",\n        \"cells\": [\n            {\n                \"type\": \"wf.Start\",\n                \"size\": {\n                    \"width\": 40,\n                    \"height\": 40\n                },\n                \"position\": {\n                    \"x\": 190,\n                    \"y\": 230\n                },\n                \"angle\": 0,\n                \"z\": 1,\n                \"id\": \"a6fc6b36-afe3-4d39-949b-eefd50233eab\",\n                \"attrs\": {}\n            },\n            {\n                \"type\": \"wf.Activity\",\n                \"size\": {\n                    \"width\": 100,\n                    \"height\": 80\n                },\n                \"markup\": [\n                    {\n                        \"tagName\": \"rect\",\n                        \"selector\": \"background\"\n                    },\n                    {\n                        \"tagName\": \"image\",\n                        \"selector\": \"icon\"\n                    },\n                    {\n                        \"tagName\": \"path\",\n                        \"selector\": \"border\"\n                    },\n                    {\n                        \"tagName\": \"text\",\n                        \"selector\": \"label\"\n                    },\n                    {\n                        \"tagName\": \"g\",\n                        \"selector\": \"markers\"\n                    },\n                    {\n                        \"tagName\": \"text\",\n                        \"selector\": \"type\",\n                        \"attributes\": {\n                            \"fill\": \"hsl(var(--primary))\"\n                        }\n                    }\n                ],\n                \"position\": {\n                    \"x\": 300,\n                    \"y\": 210\n                },\n                \"angle\": 0,\n                \"z\": 2,\n                \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\",\n                \"subscribers\": {},\n                \"attrs\": {\n                    \"label\": {\n                        \"text\": \"Activity\"\n                    },\n                    \"type\": {\n                        \"text\": \"SampleActivityJson\"\n                    },\n                    \"data\": {\n                        \"result\": \"Result_Activity\",\n                        \"resultType\": \"object\",\n                        \"taskResult\": {\n                            \"object\": {}\n                        },\n                        \"typeOptions\": {\n                            \"JSON\": \"{\\\"key\\\": \\\"abc\\\"}\"\n                        }\n                    }\n                }\n            },\n            {\n                \"type\": \"wf.Flow\",\n                \"source\": {\n                    \"id\": \"a6fc6b36-afe3-4d39-949b-eefd50233eab\"\n                },\n                \"target\": {\n                    \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\"\n                },\n                \"z\": 3,\n                \"id\": \"dfaf8a5e-9e5d-45d1-a09a-698438b7ef5e\",\n                \"labels\": [],\n                \"attrs\": {}\n            },\n            {\n                \"type\": \"wf.Activity\",\n                \"size\": {\n                    \"width\": 100,\n                    \"height\": 80\n                },\n                \"markup\": [\n                    {\n                        \"tagName\": \"rect\",\n                        \"selector\": \"background\"\n                    },\n                    {\n                        \"tagName\": \"image\",\n                        \"selector\": \"icon\"\n                    },\n                    {\n                        \"tagName\": \"path\",\n                        \"selector\": \"border\"\n                    },\n                    {\n                        \"tagName\": \"text\",\n                        \"selector\": \"label\"\n                    },\n                    {\n                        \"tagName\": \"g\",\n                        \"selector\": \"markers\"\n                    },\n                    {\n                        \"tagName\": \"text\",\n                        \"selector\": \"type\",\n                        \"attributes\": {\n                            \"fill\": \"hsl(var(--primary))\"\n                        }\n                    }\n                ],\n                \"position\": {\n                    \"x\": 450,\n                    \"y\": 210\n                },\n                \"angle\": 0,\n                \"z\": 6,\n                \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\",\n                \"subscribers\": {},\n                \"attrs\": {\n                    \"label\": {\n                        \"text\": \"Activity1\"\n                    },\n                    \"type\": {\n                        \"text\": \"SampleActivityString\"\n                    },\n                    \"data\": {\n                        \"result\": \"Result_Activity1\",\n                        \"resultType\": \"string\",\n                        \"taskResult\": \"string\",\n                        \"typeOptions\": {\n                            \"String\": \"=Result_Activity.key\",\n                            \"StringTmp\": \"Result_Activity.key\",\n                            \"StringValidate\": {\n                                \"message\": \"valid expression\",\n                                \"sent_at\": \"2024-12-23T09:26:08Z\"\n                            }\n                        },\n                        \"activityVariables\": {\n                            \"String\": true\n                        }\n                    }\n                }\n            },\n            {\n                \"type\": \"wf.Flow\",\n                \"source\": {\n                    \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\"\n                },\n                \"target\": {\n                    \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\"\n                },\n                \"z\": 7,\n                \"id\": \"49ef04eb-56b4-467c-bd62-8c63e89b3c80\",\n                \"labels\": [],\n                \"attrs\": {}\n            },\n            {\n                \"type\": \"wf.End\",\n                \"size\": {\n                    \"width\": 40,\n                    \"height\": 40\n                },\n                \"position\": {\n                    \"x\": 610,\n                    \"y\": 230\n                },\n                \"angle\": 0,\n                \"z\": 10,\n                \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\",\n                \"attrs\": {}\n            },\n            {\n                \"type\": \"wf.Flow\",\n                \"source\": {\n                    \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\"\n                },\n                \"target\": {\n                    \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\"\n                },\n                \"z\": 11,\n                \"id\": \"330878ee-846b-4a71-a320-d854e92b7854\",\n                \"labels\": [],\n                \"attrs\": {}\n            }\n        ],\n        \"name\": \"testWorkflow\"\n    },\n    \"occ_lock\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{WFM_ENDPOINT}}/v1/workflow_template/publish", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "workflow_template", "publish"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Date", "value": "Thu, 16 Jan 2025 04:45:11 GMT"}, {"key": "Transfer-Encoding", "value": "chunked"}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": \"910d11d0-6617-45c9-9f2f-00ce7f5b9ed3\",\n        \"name\": \"testWorkflow\",\n        \"workflow_json\": {\n            \"start\": \"Activity\",\n            \"states\": [\n                {\n                    \"name\": \"Activity\",\n                    \"next\": \"Activity1\",\n                    \"activity\": {\n                        \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\",\n                        \"type\": \"SampleActivityJson\",\n                        \"result\": \"Result_Activity\",\n                        \"options\": {\n                            \"startToCloseTimeout\": 300\n                        },\n                        \"arguments\": [\n                            \"Activity_JSON\"\n                        ],\n                        \"boundaryEvents\": []\n                    }\n                },\n                {\n                    \"name\": \"Activity1\",\n                    \"next\": \"End\",\n                    \"activity\": {\n                        \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\",\n                        \"type\": \"SampleActivityString\",\n                        \"result\": \"Result_Activity1\",\n                        \"options\": {\n                            \"startToCloseTimeout\": 300\n                        },\n                        \"arguments\": [\n                            \"Activity1_String\"\n                        ],\n                        \"boundaryEvents\": []\n                    }\n                },\n                {\n                    \"end\": {\n                        \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\",\n                        \"terminate\": false\n                    },\n                    \"name\": \"End\"\n                }\n            ],\n            \"variables\": {\n                \"Activity_JSON\": \"{\\\"key\\\": \\\"abc\\\"}\",\n                \"Activity1_String\": \"=Result_Activity.key\"\n            },\n            \"specVersion\": \"2.0\"\n        },\n        \"designer_json\": {\n            \"name\": \"testWorkflow\",\n            \"type\": \"bpmn\",\n            \"cells\": [\n                {\n                    \"z\": 1,\n                    \"id\": \"a6fc6b36-afe3-4d39-949b-eefd50233eab\",\n                    \"size\": {\n                        \"width\": 40,\n                        \"height\": 40\n                    },\n                    \"type\": \"wf.Start\",\n                    \"angle\": 0,\n                    \"attrs\": {},\n                    \"position\": {\n                        \"x\": 190,\n                        \"y\": 230\n                    }\n                },\n                {\n                    \"z\": 2,\n                    \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\",\n                    \"size\": {\n                        \"width\": 100,\n                        \"height\": 80\n                    },\n                    \"type\": \"wf.Activity\",\n                    \"angle\": 0,\n                    \"attrs\": {\n                        \"data\": {\n                            \"result\": \"Result_Activity\",\n                            \"resultType\": \"object\",\n                            \"taskResult\": {\n                                \"object\": {}\n                            },\n                            \"typeOptions\": {\n                                \"JSON\": \"{\\\"key\\\": \\\"abc\\\"}\"\n                            }\n                        },\n                        \"type\": {\n                            \"text\": \"SampleActivityJson\"\n                        },\n                        \"label\": {\n                            \"text\": \"Activity\"\n                        }\n                    },\n                    \"markup\": [\n                        {\n                            \"tagName\": \"rect\",\n                            \"selector\": \"background\"\n                        },\n                        {\n                            \"tagName\": \"image\",\n                            \"selector\": \"icon\"\n                        },\n                        {\n                            \"tagName\": \"path\",\n                            \"selector\": \"border\"\n                        },\n                        {\n                            \"tagName\": \"text\",\n                            \"selector\": \"label\"\n                        },\n                        {\n                            \"tagName\": \"g\",\n                            \"selector\": \"markers\"\n                        },\n                        {\n                            \"tagName\": \"text\",\n                            \"selector\": \"type\",\n                            \"attributes\": {\n                                \"fill\": \"hsl(var(--primary))\"\n                            }\n                        }\n                    ],\n                    \"position\": {\n                        \"x\": 300,\n                        \"y\": 210\n                    },\n                    \"subscribers\": {}\n                },\n                {\n                    \"z\": 3,\n                    \"id\": \"dfaf8a5e-9e5d-45d1-a09a-698438b7ef5e\",\n                    \"type\": \"wf.Flow\",\n                    \"attrs\": {},\n                    \"labels\": [],\n                    \"source\": {\n                        \"id\": \"a6fc6b36-afe3-4d39-949b-eefd50233eab\"\n                    },\n                    \"target\": {\n                        \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\"\n                    }\n                },\n                {\n                    \"z\": 6,\n                    \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\",\n                    \"size\": {\n                        \"width\": 100,\n                        \"height\": 80\n                    },\n                    \"type\": \"wf.Activity\",\n                    \"angle\": 0,\n                    \"attrs\": {\n                        \"data\": {\n                            \"result\": \"Result_Activity1\",\n                            \"resultType\": \"string\",\n                            \"taskResult\": \"string\",\n                            \"typeOptions\": {\n                                \"String\": \"=Result_Activity.key\",\n                                \"StringTmp\": \"Result_Activity.key\",\n                                \"StringValidate\": {\n                                    \"message\": \"valid expression\",\n                                    \"sent_at\": \"2024-12-23T09:26:08Z\"\n                                }\n                            },\n                            \"activityVariables\": {\n                                \"String\": true\n                            }\n                        },\n                        \"type\": {\n                            \"text\": \"SampleActivityString\"\n                        },\n                        \"label\": {\n                            \"text\": \"Activity1\"\n                        }\n                    },\n                    \"markup\": [\n                        {\n                            \"tagName\": \"rect\",\n                            \"selector\": \"background\"\n                        },\n                        {\n                            \"tagName\": \"image\",\n                            \"selector\": \"icon\"\n                        },\n                        {\n                            \"tagName\": \"path\",\n                            \"selector\": \"border\"\n                        },\n                        {\n                            \"tagName\": \"text\",\n                            \"selector\": \"label\"\n                        },\n                        {\n                            \"tagName\": \"g\",\n                            \"selector\": \"markers\"\n                        },\n                        {\n                            \"tagName\": \"text\",\n                            \"selector\": \"type\",\n                            \"attributes\": {\n                                \"fill\": \"hsl(var(--primary))\"\n                            }\n                        }\n                    ],\n                    \"position\": {\n                        \"x\": 450,\n                        \"y\": 210\n                    },\n                    \"subscribers\": {}\n                },\n                {\n                    \"z\": 7,\n                    \"id\": \"49ef04eb-56b4-467c-bd62-8c63e89b3c80\",\n                    \"type\": \"wf.Flow\",\n                    \"attrs\": {},\n                    \"labels\": [],\n                    \"source\": {\n                        \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\"\n                    },\n                    \"target\": {\n                        \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\"\n                    }\n                },\n                {\n                    \"z\": 10,\n                    \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\",\n                    \"size\": {\n                        \"width\": 40,\n                        \"height\": 40\n                    },\n                    \"type\": \"wf.End\",\n                    \"angle\": 0,\n                    \"attrs\": {},\n                    \"position\": {\n                        \"x\": 610,\n                        \"y\": 230\n                    }\n                },\n                {\n                    \"z\": 11,\n                    \"id\": \"330878ee-846b-4a71-a320-d854e92b7854\",\n                    \"type\": \"wf.Flow\",\n                    \"attrs\": {},\n                    \"labels\": [],\n                    \"source\": {\n                        \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\"\n                    },\n                    \"target\": {\n                        \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\"\n                    }\n                }\n            ]\n        },\n        \"editable\": false,\n        \"created_at\": \"2024-12-23T09:26:17.229735Z\",\n        \"created_by\": \"f67cb8f5-0645-444d-a4bd-c61aaf1b2db0\",\n        \"updated_at\": \"2025-01-16T04:45:11.754052Z\",\n        \"updated_by\": \"f67cb8f5-0645-444d-a4bd-c61aaf1b2db0\",\n        \"tenant_id\": \"430d2014-6e57-4e8a-ad75-484aa276a5cc\",\n        \"occ_lock\": 2\n    },\n    \"sent_at\": \"2025-01-16T04:45:11Z\"\n}"}]}, {"name": "Workflow Template Get", "request": {"method": "GET", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/workflow_template/:template_id", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "workflow_template", ":template_id"], "variable": [{"key": "template_id", "value": "910d11d0-6617-45c9-9f2f-00ce7f5b9ed3"}]}}, "response": [{"name": "Workflow Template Get", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/workflow_template/:template_id", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "workflow_template", ":template_id"], "variable": [{"key": "template_id", "value": "910d11d0-6617-45c9-9f2f-00ce7f5b9ed3"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Date", "value": "Thu, 16 Jan 2025 04:45:42 GMT"}, {"key": "Transfer-Encoding", "value": "chunked"}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": \"910d11d0-6617-45c9-9f2f-00ce7f5b9ed3\",\n        \"name\": \"testWorkflow\",\n        \"workflow_json\": {\n            \"start\": \"Activity\",\n            \"states\": [\n                {\n                    \"name\": \"Activity\",\n                    \"next\": \"Activity1\",\n                    \"activity\": {\n                        \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\",\n                        \"type\": \"SampleActivityJson\",\n                        \"result\": \"Result_Activity\",\n                        \"options\": {\n                            \"startToCloseTimeout\": 300\n                        },\n                        \"arguments\": [\n                            \"Activity_JSON\"\n                        ],\n                        \"boundaryEvents\": []\n                    }\n                },\n                {\n                    \"name\": \"Activity1\",\n                    \"next\": \"End\",\n                    \"activity\": {\n                        \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\",\n                        \"type\": \"SampleActivityString\",\n                        \"result\": \"Result_Activity1\",\n                        \"options\": {\n                            \"startToCloseTimeout\": 300\n                        },\n                        \"arguments\": [\n                            \"Activity1_String\"\n                        ],\n                        \"boundaryEvents\": []\n                    }\n                },\n                {\n                    \"end\": {\n                        \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\",\n                        \"terminate\": false\n                    },\n                    \"name\": \"End\"\n                }\n            ],\n            \"variables\": {\n                \"Activity_JSON\": \"{\\\"key\\\": \\\"abc\\\"}\",\n                \"Activity1_String\": \"=Result_Activity.key\"\n            },\n            \"specVersion\": \"2.0\"\n        },\n        \"designer_json\": {\n            \"name\": \"testWorkflow\",\n            \"type\": \"bpmn\",\n            \"cells\": [\n                {\n                    \"z\": 1,\n                    \"id\": \"a6fc6b36-afe3-4d39-949b-eefd50233eab\",\n                    \"size\": {\n                        \"width\": 40,\n                        \"height\": 40\n                    },\n                    \"type\": \"wf.Start\",\n                    \"angle\": 0,\n                    \"attrs\": {},\n                    \"position\": {\n                        \"x\": 190,\n                        \"y\": 230\n                    }\n                },\n                {\n                    \"z\": 2,\n                    \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\",\n                    \"size\": {\n                        \"width\": 100,\n                        \"height\": 80\n                    },\n                    \"type\": \"wf.Activity\",\n                    \"angle\": 0,\n                    \"attrs\": {\n                        \"data\": {\n                            \"result\": \"Result_Activity\",\n                            \"resultType\": \"object\",\n                            \"taskResult\": {\n                                \"object\": {}\n                            },\n                            \"typeOptions\": {\n                                \"JSON\": \"{\\\"key\\\": \\\"abc\\\"}\"\n                            }\n                        },\n                        \"type\": {\n                            \"text\": \"SampleActivityJson\"\n                        },\n                        \"label\": {\n                            \"text\": \"Activity\"\n                        }\n                    },\n                    \"markup\": [\n                        {\n                            \"tagName\": \"rect\",\n                            \"selector\": \"background\"\n                        },\n                        {\n                            \"tagName\": \"image\",\n                            \"selector\": \"icon\"\n                        },\n                        {\n                            \"tagName\": \"path\",\n                            \"selector\": \"border\"\n                        },\n                        {\n                            \"tagName\": \"text\",\n                            \"selector\": \"label\"\n                        },\n                        {\n                            \"tagName\": \"g\",\n                            \"selector\": \"markers\"\n                        },\n                        {\n                            \"tagName\": \"text\",\n                            \"selector\": \"type\",\n                            \"attributes\": {\n                                \"fill\": \"hsl(var(--primary))\"\n                            }\n                        }\n                    ],\n                    \"position\": {\n                        \"x\": 300,\n                        \"y\": 210\n                    },\n                    \"subscribers\": {}\n                },\n                {\n                    \"z\": 3,\n                    \"id\": \"dfaf8a5e-9e5d-45d1-a09a-698438b7ef5e\",\n                    \"type\": \"wf.Flow\",\n                    \"attrs\": {},\n                    \"labels\": [],\n                    \"source\": {\n                        \"id\": \"a6fc6b36-afe3-4d39-949b-eefd50233eab\"\n                    },\n                    \"target\": {\n                        \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\"\n                    }\n                },\n                {\n                    \"z\": 6,\n                    \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\",\n                    \"size\": {\n                        \"width\": 100,\n                        \"height\": 80\n                    },\n                    \"type\": \"wf.Activity\",\n                    \"angle\": 0,\n                    \"attrs\": {\n                        \"data\": {\n                            \"result\": \"Result_Activity1\",\n                            \"resultType\": \"string\",\n                            \"taskResult\": \"string\",\n                            \"typeOptions\": {\n                                \"String\": \"=Result_Activity.key\",\n                                \"StringTmp\": \"Result_Activity.key\",\n                                \"StringValidate\": {\n                                    \"message\": \"valid expression\",\n                                    \"sent_at\": \"2024-12-23T09:26:08Z\"\n                                }\n                            },\n                            \"activityVariables\": {\n                                \"String\": true\n                            }\n                        },\n                        \"type\": {\n                            \"text\": \"SampleActivityString\"\n                        },\n                        \"label\": {\n                            \"text\": \"Activity1\"\n                        }\n                    },\n                    \"markup\": [\n                        {\n                            \"tagName\": \"rect\",\n                            \"selector\": \"background\"\n                        },\n                        {\n                            \"tagName\": \"image\",\n                            \"selector\": \"icon\"\n                        },\n                        {\n                            \"tagName\": \"path\",\n                            \"selector\": \"border\"\n                        },\n                        {\n                            \"tagName\": \"text\",\n                            \"selector\": \"label\"\n                        },\n                        {\n                            \"tagName\": \"g\",\n                            \"selector\": \"markers\"\n                        },\n                        {\n                            \"tagName\": \"text\",\n                            \"selector\": \"type\",\n                            \"attributes\": {\n                                \"fill\": \"hsl(var(--primary))\"\n                            }\n                        }\n                    ],\n                    \"position\": {\n                        \"x\": 450,\n                        \"y\": 210\n                    },\n                    \"subscribers\": {}\n                },\n                {\n                    \"z\": 7,\n                    \"id\": \"49ef04eb-56b4-467c-bd62-8c63e89b3c80\",\n                    \"type\": \"wf.Flow\",\n                    \"attrs\": {},\n                    \"labels\": [],\n                    \"source\": {\n                        \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\"\n                    },\n                    \"target\": {\n                        \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\"\n                    }\n                },\n                {\n                    \"z\": 10,\n                    \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\",\n                    \"size\": {\n                        \"width\": 40,\n                        \"height\": 40\n                    },\n                    \"type\": \"wf.End\",\n                    \"angle\": 0,\n                    \"attrs\": {},\n                    \"position\": {\n                        \"x\": 610,\n                        \"y\": 230\n                    }\n                },\n                {\n                    \"z\": 11,\n                    \"id\": \"330878ee-846b-4a71-a320-d854e92b7854\",\n                    \"type\": \"wf.Flow\",\n                    \"attrs\": {},\n                    \"labels\": [],\n                    \"source\": {\n                        \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\"\n                    },\n                    \"target\": {\n                        \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\"\n                    }\n                }\n            ]\n        },\n        \"editable\": false,\n        \"created_at\": \"2024-12-23T09:26:17.229735Z\",\n        \"created_by\": \"f67cb8f5-0645-444d-a4bd-c61aaf1b2db0\",\n        \"updated_at\": \"2025-01-16T04:45:11.754052Z\",\n        \"updated_by\": \"f67cb8f5-0645-444d-a4bd-c61aaf1b2db0\",\n        \"tenant_id\": \"430d2014-6e57-4e8a-ad75-484aa276a5cc\",\n        \"occ_lock\": 2\n    },\n    \"sent_at\": \"2025-01-16T04:45:42Z\"\n}"}]}, {"name": "Workflow Template List", "request": {"method": "GET", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/workflow_template?page=1&size=3&sort=created_at,desc", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "workflow_template"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "3"}, {"key": "sort", "value": "created_at,desc"}]}}, "response": [{"name": "Workflow Template List", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/workflow_template?page=1&size=3&sort=created_at,desc", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "workflow_template"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "3"}, {"key": "sort", "value": "created_at,desc"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Date", "value": "Thu, 16 Jan 2025 04:49:56 GMT"}, {"key": "Transfer-Encoding", "value": "chunked"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"id\": \"6d703ef6-dcb1-4f3d-99ec-290840bc4f74\",\n            \"name\": \"testWorkflow\",\n            \"workflow_json\": {\n                \"start\": \"Activity\",\n                \"states\": [\n                    {\n                        \"name\": \"Activity\",\n                        \"next\": \"Activity1\",\n                        \"activity\": {\n                            \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\",\n                            \"type\": \"SampleActivityJson\",\n                            \"result\": \"Result_Activity\",\n                            \"options\": {\n                                \"startToCloseTimeout\": 300\n                            },\n                            \"arguments\": [\n                                \"Activity_JSON\"\n                            ],\n                            \"boundaryEvents\": []\n                        }\n                    },\n                    {\n                        \"name\": \"Activity1\",\n                        \"next\": \"End\",\n                        \"activity\": {\n                            \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\",\n                            \"type\": \"SampleActivityString\",\n                            \"result\": \"Result_Activity1\",\n                            \"options\": {\n                                \"startToCloseTimeout\": 300\n                            },\n                            \"arguments\": [\n                                \"Activity1_String\"\n                            ],\n                            \"boundaryEvents\": []\n                        }\n                    },\n                    {\n                        \"end\": {\n                            \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\",\n                            \"terminate\": false\n                        },\n                        \"name\": \"End\"\n                    }\n                ],\n                \"variables\": {\n                    \"Activity_JSON\": \"{\\\"key\\\": \\\"abc\\\"}\",\n                    \"Activity1_String\": \"=Result_Activity.key\"\n                },\n                \"specVersion\": \"2.0\"\n            },\n            \"designer_json\": {\n                \"name\": \"testWorkflow\",\n                \"type\": \"bpmn\",\n                \"cells\": [\n                    {\n                        \"z\": 1,\n                        \"id\": \"a6fc6b36-afe3-4d39-949b-eefd50233eab\",\n                        \"size\": {\n                            \"width\": 40,\n                            \"height\": 40\n                        },\n                        \"type\": \"wf.Start\",\n                        \"angle\": 0,\n                        \"attrs\": {},\n                        \"position\": {\n                            \"x\": 190,\n                            \"y\": 230\n                        }\n                    },\n                    {\n                        \"z\": 2,\n                        \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\",\n                        \"size\": {\n                            \"width\": 100,\n                            \"height\": 80\n                        },\n                        \"type\": \"wf.Activity\",\n                        \"angle\": 0,\n                        \"attrs\": {\n                            \"data\": {\n                                \"result\": \"Result_Activity\",\n                                \"resultType\": \"object\",\n                                \"taskResult\": {\n                                    \"object\": {}\n                                },\n                                \"typeOptions\": {\n                                    \"JSON\": \"{\\\"key\\\": \\\"abc\\\"}\"\n                                }\n                            },\n                            \"type\": {\n                                \"text\": \"SampleActivityJson\"\n                            },\n                            \"label\": {\n                                \"text\": \"Activity\"\n                            }\n                        },\n                        \"markup\": [\n                            {\n                                \"tagName\": \"rect\",\n                                \"selector\": \"background\"\n                            },\n                            {\n                                \"tagName\": \"image\",\n                                \"selector\": \"icon\"\n                            },\n                            {\n                                \"tagName\": \"path\",\n                                \"selector\": \"border\"\n                            },\n                            {\n                                \"tagName\": \"text\",\n                                \"selector\": \"label\"\n                            },\n                            {\n                                \"tagName\": \"g\",\n                                \"selector\": \"markers\"\n                            },\n                            {\n                                \"tagName\": \"text\",\n                                \"selector\": \"type\",\n                                \"attributes\": {\n                                    \"fill\": \"hsl(var(--primary))\"\n                                }\n                            }\n                        ],\n                        \"position\": {\n                            \"x\": 300,\n                            \"y\": 210\n                        },\n                        \"subscribers\": {}\n                    },\n                    {\n                        \"z\": 3,\n                        \"id\": \"dfaf8a5e-9e5d-45d1-a09a-698438b7ef5e\",\n                        \"type\": \"wf.Flow\",\n                        \"attrs\": {},\n                        \"labels\": [],\n                        \"source\": {\n                            \"id\": \"a6fc6b36-afe3-4d39-949b-eefd50233eab\"\n                        },\n                        \"target\": {\n                            \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\"\n                        }\n                    },\n                    {\n                        \"z\": 6,\n                        \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\",\n                        \"size\": {\n                            \"width\": 100,\n                            \"height\": 80\n                        },\n                        \"type\": \"wf.Activity\",\n                        \"angle\": 0,\n                        \"attrs\": {\n                            \"data\": {\n                                \"result\": \"Result_Activity1\",\n                                \"resultType\": \"string\",\n                                \"taskResult\": \"string\",\n                                \"typeOptions\": {\n                                    \"String\": \"=Result_Activity.key\",\n                                    \"StringTmp\": \"Result_Activity.key\",\n                                    \"StringValidate\": {\n                                        \"message\": \"valid expression\",\n                                        \"sent_at\": \"2024-12-23T09:26:08Z\"\n                                    }\n                                },\n                                \"activityVariables\": {\n                                    \"String\": true\n                                }\n                            },\n                            \"type\": {\n                                \"text\": \"SampleActivityString\"\n                            },\n                            \"label\": {\n                                \"text\": \"Activity1\"\n                            }\n                        },\n                        \"markup\": [\n                            {\n                                \"tagName\": \"rect\",\n                                \"selector\": \"background\"\n                            },\n                            {\n                                \"tagName\": \"image\",\n                                \"selector\": \"icon\"\n                            },\n                            {\n                                \"tagName\": \"path\",\n                                \"selector\": \"border\"\n                            },\n                            {\n                                \"tagName\": \"text\",\n                                \"selector\": \"label\"\n                            },\n                            {\n                                \"tagName\": \"g\",\n                                \"selector\": \"markers\"\n                            },\n                            {\n                                \"tagName\": \"text\",\n                                \"selector\": \"type\",\n                                \"attributes\": {\n                                    \"fill\": \"hsl(var(--primary))\"\n                                }\n                            }\n                        ],\n                        \"position\": {\n                            \"x\": 450,\n                            \"y\": 210\n                        },\n                        \"subscribers\": {}\n                    },\n                    {\n                        \"z\": 7,\n                        \"id\": \"49ef04eb-56b4-467c-bd62-8c63e89b3c80\",\n                        \"type\": \"wf.Flow\",\n                        \"attrs\": {},\n                        \"labels\": [],\n                        \"source\": {\n                            \"id\": \"d06bfc1d-7541-44ee-97b8-b3131dd06a31\"\n                        },\n                        \"target\": {\n                            \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\"\n                        }\n                    },\n                    {\n                        \"z\": 10,\n                        \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\",\n                        \"size\": {\n                            \"width\": 40,\n                            \"height\": 40\n                        },\n                        \"type\": \"wf.End\",\n                        \"angle\": 0,\n                        \"attrs\": {},\n                        \"position\": {\n                            \"x\": 610,\n                            \"y\": 230\n                        }\n                    },\n                    {\n                        \"z\": 11,\n                        \"id\": \"330878ee-846b-4a71-a320-d854e92b7854\",\n                        \"type\": \"wf.Flow\",\n                        \"attrs\": {},\n                        \"labels\": [],\n                        \"source\": {\n                            \"id\": \"20a0ec07-f881-4bd2-bb58-a6efb474a18d\"\n                        },\n                        \"target\": {\n                            \"id\": \"9e4b803e-7f38-4550-917b-82a5d7d00bbb\"\n                        }\n                    }\n                ]\n            },\n            \"editable\": true,\n            \"created_at\": \"2025-01-16T04:49:26.14791Z\",\n            \"created_by\": \"f67cb8f5-0645-444d-a4bd-c61aaf1b2db0\",\n            \"updated_at\": \"2025-01-16T04:49:26.14791Z\",\n            \"updated_by\": \"f67cb8f5-0645-444d-a4bd-c61aaf1b2db0\",\n            \"tenant_id\": \"430d2014-6e57-4e8a-ad75-484aa276a5cc\",\n            \"occ_lock\": 0\n        },\n        {\n            \"id\": \"be558d14-a461-43fc-a669-b5603a0efb5c\",\n            \"name\": \"testing\",\n            \"workflow_json\": {\n                \"start\": \"Activity\",\n                \"states\": [\n                    {\n                        \"name\": \"Activity\",\n                        \"next\": \"Terminate\",\n                        \"activity\": {\n                            \"id\": \"5a707703-9e35-4ac4-8284-c8b0e89a3ec1\",\n                            \"type\": \"Delay\",\n                            \"result\": \"Result_Activity\",\n                            \"options\": {\n                                \"startToCloseTimeout\": 300\n                            },\n                            \"arguments\": [\n                                \"Activity_Second\"\n                            ],\n                            \"boundaryEvents\": []\n                        }\n                    },\n                    {\n                        \"end\": {\n                            \"id\": \"b1a803c7-6c94-4c5a-a4b5-7ce1674701bb\",\n                            \"terminate\": true\n                        },\n                        \"name\": \"Terminate\"\n                    }\n                ],\n                \"variables\": {\n                    \"Activity_Second\": 0\n                },\n                \"specVersion\": \"2.0\"\n            },\n            \"designer_json\": {\n                \"name\": \"testing123\",\n                \"type\": \"bpmn\",\n                \"cells\": [\n                    {\n                        \"z\": 1,\n                        \"id\": \"d451b758-39fb-4d11-bd0a-61f568ae2871\",\n                        \"size\": {\n                            \"width\": 40,\n                            \"height\": 40\n                        },\n                        \"type\": \"wf.Start\",\n                        \"angle\": 0,\n                        \"attrs\": {},\n                        \"position\": {\n                            \"x\": 300,\n                            \"y\": 240\n                        }\n                    },\n                    {\n                        \"z\": 12,\n                        \"id\": \"5a707703-9e35-4ac4-8284-c8b0e89a3ec1\",\n                        \"size\": {\n                            \"width\": 100,\n                            \"height\": 80\n                        },\n                        \"type\": \"wf.Activity\",\n                        \"angle\": 0,\n                        \"attrs\": {\n                            \"data\": {\n                                \"result\": \"Result_Activity\",\n                                \"resultType\": \"null\",\n                                \"taskResult\": null\n                            },\n                            \"type\": {\n                                \"text\": \"Delay\"\n                            },\n                            \"label\": {\n                                \"text\": \"Activity\"\n                            }\n                        },\n                        \"markup\": [\n                            {\n                                \"tagName\": \"rect\",\n                                \"selector\": \"background\"\n                            },\n                            {\n                                \"tagName\": \"image\",\n                                \"selector\": \"icon\"\n                            },\n                            {\n                                \"tagName\": \"path\",\n                                \"selector\": \"border\"\n                            },\n                            {\n                                \"tagName\": \"text\",\n                                \"selector\": \"label\"\n                            },\n                            {\n                                \"tagName\": \"g\",\n                                \"selector\": \"markers\"\n                            },\n                            {\n                                \"tagName\": \"text\",\n                                \"selector\": \"type\",\n                                \"attributes\": {\n                                    \"fill\": \"hsl(var(--primary))\"\n                                }\n                            }\n                        ],\n                        \"position\": {\n                            \"x\": 370,\n                            \"y\": 160\n                        },\n                        \"subscribers\": {}\n                    },\n                    {\n                        \"z\": 13,\n                        \"id\": \"bcebd192-f00e-4210-a429-9d2e77a5d435\",\n                        \"type\": \"wf.Flow\",\n                        \"attrs\": {},\n                        \"labels\": [],\n                        \"source\": {\n                            \"id\": \"d451b758-39fb-4d11-bd0a-61f568ae2871\"\n                        },\n                        \"target\": {\n                            \"id\": \"5a707703-9e35-4ac4-8284-c8b0e89a3ec1\"\n                        }\n                    },\n                    {\n                        \"z\": 14,\n                        \"id\": \"b1a803c7-6c94-4c5a-a4b5-7ce1674701bb\",\n                        \"size\": {\n                            \"width\": 40,\n                            \"height\": 40\n                        },\n                        \"type\": \"wf.Terminate\",\n                        \"angle\": 0,\n                        \"attrs\": {},\n                        \"position\": {\n                            \"x\": 610,\n                            \"y\": 220\n                        }\n                    },\n                    {\n                        \"z\": 15,\n                        \"id\": \"4292477e-bc0b-479c-8a93-58c3412199b2\",\n                        \"type\": \"wf.Flow\",\n                        \"attrs\": {},\n                        \"labels\": [],\n                        \"source\": {\n                            \"id\": \"5a707703-9e35-4ac4-8284-c8b0e89a3ec1\"\n                        },\n                        \"target\": {\n                            \"id\": \"b1a803c7-6c94-4c5a-a4b5-7ce1674701bb\"\n                        }\n                    }\n                ]\n            },\n            \"editable\": true,\n            \"created_at\": \"2025-01-13T16:45:54.805859Z\",\n            \"created_by\": \"f67cb8f5-0645-444d-a4bd-c61aaf1b2db0\",\n            \"updated_at\": \"2025-01-13T16:57:52.519659Z\",\n            \"updated_by\": \"f67cb8f5-0645-444d-a4bd-c61aaf1b2db0\",\n            \"tenant_id\": \"430d2014-6e57-4e8a-ad75-484aa276a5cc\",\n            \"occ_lock\": 5\n        }\n    ],\n    \"sent_at\": \"2025-01-16T04:49:56Z\",\n    \"page\": {\n        \"number\": 1,\n        \"size\": 3,\n        \"total_records\": 2,\n        \"count\": 2,\n        \"sort\": [\n            \"created_at desc\"\n        ]\n    }\n}"}]}, {"name": "Workflow Template Delete", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/workflow_template/:template_id", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "workflow_template", ":template_id"], "variable": [{"key": "template_id", "value": "af2c5220-cccd-468e-a7d7-d0885a42a8aa"}]}}, "response": [{"name": "Workflow Template Delete", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/workflow_template/:template_id", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "workflow_template", ":template_id"], "variable": [{"key": "template_id", "value": "910d11d0-6617-45c9-9f2f-00ce7f5b9ed3"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Date", "value": "Thu, 16 Jan 2025 04:47:31 GMT"}, {"key": "Content-Length", "value": "35"}], "cookie": [], "body": "{\n    \"sent_at\": \"2025-01-16T04:47:31Z\"\n}"}]}, {"name": "Form Save", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"AttendanceForm\",\n    \"form_json\": {\n        \"components\": [\n            {\n                \"label\": \"Attendance List1\",\n                \"type\": \"checklist\",\n                \"layout\": {\n                    \"row\": \"Row_1fxsjpo\",\n                    \"columns\": 7\n                },\n                \"id\": \"Field_1p5m2f1\",\n                \"key\": \"checklist_xmj6qe\",\n                \"valuesKey\": \"http://hasura.dev2.ar2/api/rest/student/:incidentid\"\n            },\n            {\n                \"action\": \"submit\",\n                \"label\": \"Submit\",\n                \"type\": \"button\",\n                \"layout\": {\n                    \"row\": \"Row_1ryef5k\",\n                    \"columns\": 2\n                },\n                \"id\": \"Field_110toor\",\n                \"properties\": {}\n            }\n        ],\n        \"type\": \"default\",\n        \"id\": \"Form_1r7eh7h\",\n        \"exporter\": {\n            \"name\": \"form-js (https://demo.bpmn.io)\",\n            \"version\": \"1.4.0\"\n        },\n        \"schemaVersion\": 12\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{WFM_ENDPOINT}}/v1/form_template/save", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "form_template", "save"]}}, "response": [{"name": "Form Save", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"AttendanceForm\",\n    \"form_json\": {\n        \"components\": [\n            {\n                \"label\": \"Attendance List1\",\n                \"type\": \"checklist\",\n                \"layout\": {\n                    \"row\": \"Row_1fxsjpo\",\n                    \"columns\": 7\n                },\n                \"id\": \"Field_1p5m2f1\",\n                \"key\": \"checklist_xmj6qe\",\n                \"valuesKey\": \"http://hasura.dev2.ar2/api/rest/student/:incidentid\"\n            },\n            {\n                \"action\": \"submit\",\n                \"label\": \"Submit\",\n                \"type\": \"button\",\n                \"layout\": {\n                    \"row\": \"Row_1ryef5k\",\n                    \"columns\": 2\n                },\n                \"id\": \"Field_110toor\",\n                \"properties\": {}\n            }\n        ],\n        \"type\": \"default\",\n        \"id\": \"Form_1r7eh7h\",\n        \"exporter\": {\n            \"name\": \"form-js (https://demo.bpmn.io)\",\n            \"version\": \"1.4.0\"\n        },\n        \"schemaVersion\": 12\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{WFM_ENDPOINT}}/v1/form_template/save", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "form_template", "save"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Date", "value": "Thu, 16 Jan 2025 04:48:04 GMT"}, {"key": "Content-Length", "value": "894"}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": \"38afe467-b147-4f8e-b4d7-55f7a3712b34\",\n        \"name\": \"AttendanceForm\",\n        \"form_json\": {\n            \"id\": \"Form_1r7eh7h\",\n            \"type\": \"default\",\n            \"exporter\": {\n                \"name\": \"form-js (https://demo.bpmn.io)\",\n                \"version\": \"1.4.0\"\n            },\n            \"components\": [\n                {\n                    \"id\": \"Field_1p5m2f1\",\n                    \"key\": \"checklist_xmj6qe\",\n                    \"type\": \"checklist\",\n                    \"label\": \"Attendance List1\",\n                    \"layout\": {\n                        \"row\": \"Row_1fxsjpo\",\n                        \"columns\": 7\n                    },\n                    \"valuesKey\": \"http://hasura.dev2.ar2/api/rest/student/:incidentid\"\n                },\n                {\n                    \"id\": \"Field_110toor\",\n                    \"type\": \"button\",\n                    \"label\": \"Submit\",\n                    \"action\": \"submit\",\n                    \"layout\": {\n                        \"row\": \"Row_1ryef5k\",\n                        \"columns\": 2\n                    },\n                    \"properties\": {}\n                }\n            ],\n            \"schemaVersion\": 12\n        },\n        \"component_keys\": [\n            \"checklist_xmj6qe\"\n        ],\n        \"created_at\": \"2025-01-16T04:48:04.28452Z\",\n        \"created_by\": \"f67cb8f5-0645-444d-a4bd-c61aaf1b2db0\",\n        \"updated_at\": \"2025-01-16T04:48:04.28452Z\",\n        \"updated_by\": \"f67cb8f5-0645-444d-a4bd-c61aaf1b2db0\",\n        \"tenant_id\": \"430d2014-6e57-4e8a-ad75-484aa276a5cc\",\n        \"occ_lock\": 0\n    },\n    \"sent_at\": \"2025-01-16T04:48:04Z\"\n}"}]}, {"name": "Form Get", "request": {"method": "GET", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/form_template/:template_id", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "form_template", ":template_id"], "variable": [{"key": "template_id", "value": "38afe467-b147-4f8e-b4d7-55f7a3712b34"}]}}, "response": [{"name": "Form Get", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/form_template/:template_id", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "form_template", ":template_id"], "variable": [{"key": "template_id", "value": "38afe467-b147-4f8e-b4d7-55f7a3712b34"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Date", "value": "Thu, 16 Jan 2025 04:48:34 GMT"}, {"key": "Content-Length", "value": "894"}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": \"38afe467-b147-4f8e-b4d7-55f7a3712b34\",\n        \"name\": \"AttendanceForm\",\n        \"form_json\": {\n            \"id\": \"Form_1r7eh7h\",\n            \"type\": \"default\",\n            \"exporter\": {\n                \"name\": \"form-js (https://demo.bpmn.io)\",\n                \"version\": \"1.4.0\"\n            },\n            \"components\": [\n                {\n                    \"id\": \"Field_1p5m2f1\",\n                    \"key\": \"checklist_xmj6qe\",\n                    \"type\": \"checklist\",\n                    \"label\": \"Attendance List1\",\n                    \"layout\": {\n                        \"row\": \"Row_1fxsjpo\",\n                        \"columns\": 7\n                    },\n                    \"valuesKey\": \"http://hasura.dev2.ar2/api/rest/student/:incidentid\"\n                },\n                {\n                    \"id\": \"Field_110toor\",\n                    \"type\": \"button\",\n                    \"label\": \"Submit\",\n                    \"action\": \"submit\",\n                    \"layout\": {\n                        \"row\": \"Row_1ryef5k\",\n                        \"columns\": 2\n                    },\n                    \"properties\": {}\n                }\n            ],\n            \"schemaVersion\": 12\n        },\n        \"component_keys\": [\n            \"checklist_xmj6qe\"\n        ],\n        \"created_at\": \"2025-01-16T04:48:04.28452Z\",\n        \"created_by\": \"f67cb8f5-0645-444d-a4bd-c61aaf1b2db0\",\n        \"updated_at\": \"2025-01-16T04:48:04.28452Z\",\n        \"updated_by\": \"f67cb8f5-0645-444d-a4bd-c61aaf1b2db0\",\n        \"tenant_id\": \"430d2014-6e57-4e8a-ad75-484aa276a5cc\",\n        \"occ_lock\": 0\n    },\n    \"sent_at\": \"2025-01-16T04:48:34Z\"\n}"}]}, {"name": "Form List", "request": {"method": "GET", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/form_template?page=1&size=3&sort=created_at,desc", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "form_template"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "3"}, {"key": "sort", "value": "created_at,desc"}]}}, "response": [{"name": "Form List", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/form_template?page=1&size=3&sort=created_at,desc", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "form_template"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "3"}, {"key": "sort", "value": "created_at,desc"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Date", "value": "Thu, 16 Jan 2025 04:48:52 GMT"}, {"key": "Transfer-Encoding", "value": "chunked"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"id\": \"38afe467-b147-4f8e-b4d7-55f7a3712b34\",\n            \"name\": \"AttendanceForm\",\n            \"form_json\": {\n                \"id\": \"Form_1r7eh7h\",\n                \"type\": \"default\",\n                \"exporter\": {\n                    \"name\": \"form-js (https://demo.bpmn.io)\",\n                    \"version\": \"1.4.0\"\n                },\n                \"components\": [\n                    {\n                        \"id\": \"Field_1p5m2f1\",\n                        \"key\": \"checklist_xmj6qe\",\n                        \"type\": \"checklist\",\n                        \"label\": \"Attendance List1\",\n                        \"layout\": {\n                            \"row\": \"Row_1fxsjpo\",\n                            \"columns\": 7\n                        },\n                        \"valuesKey\": \"http://hasura.dev2.ar2/api/rest/student/:incidentid\"\n                    },\n                    {\n                        \"id\": \"Field_110toor\",\n                        \"type\": \"button\",\n                        \"label\": \"Submit\",\n                        \"action\": \"submit\",\n                        \"layout\": {\n                            \"row\": \"Row_1ryef5k\",\n                            \"columns\": 2\n                        },\n                        \"properties\": {}\n                    }\n                ],\n                \"schemaVersion\": 12\n            },\n            \"component_keys\": [\n                \"checklist_xmj6qe\"\n            ],\n            \"created_at\": \"2025-01-16T04:48:04.28452Z\",\n            \"created_by\": \"f67cb8f5-0645-444d-a4bd-c61aaf1b2db0\",\n            \"updated_at\": \"2025-01-16T04:48:04.28452Z\",\n            \"updated_by\": \"f67cb8f5-0645-444d-a4bd-c61aaf1b2db0\",\n            \"tenant_id\": \"430d2014-6e57-4e8a-ad75-484aa276a5cc\",\n            \"occ_lock\": 0\n        },\n        {\n            \"id\": \"b5628c8b-14ed-4eed-b687-9779b8155a91\",\n            \"name\": \"FormTemplate1\",\n            \"form_json\": {\n                \"id\": \"IncidentApprovalForm\",\n                \"type\": \"default\",\n                \"exporter\": {\n                    \"name\": \"form-js (https://demo.bpmn.io)\",\n                    \"version\": \"1.4.0\"\n                },\n                \"components\": [\n                    {\n                        \"id\": \"Field_1qapqec\",\n                        \"key\": \"textfield_5oi7mh\",\n                        \"type\": \"textfield\",\n                        \"label\": \"Reviewer Name\",\n                        \"layout\": {\n                            \"row\": \"Row_0lda7il\",\n                            \"columns\": 8\n                        },\n                        \"validate\": {\n                            \"required\": true\n                        }\n                    },\n                    {\n                        \"id\": \"Field_1qbm0ol\",\n                        \"key\": \"textfield_7avnmt\",\n                        \"type\": \"textfield\",\n                        \"label\": \"Reviewer Title\",\n                        \"layout\": {\n                            \"row\": \"Row_0lda7il\",\n                            \"columns\": null\n                        },\n                        \"validate\": {\n                            \"required\": true\n                        }\n                    },\n                    {\n                        \"id\": \"Field_18mzv5y\",\n                        \"key\": \"textarea_dvllf8\",\n                        \"type\": \"textarea\",\n                        \"label\": \"Reviewer's Assessment\",\n                        \"layout\": {\n                            \"row\": \"Row_0fs6p7i\",\n                            \"columns\": null\n                        },\n                        \"validate\": {\n                            \"required\": true\n                        },\n                        \"defaultValue\": \"[Specify any follow-up actions that need to be taken based on the review.]\"\n                    },\n                    {\n                        \"id\": \"Field_1a6kuma\",\n                        \"key\": \"datetime_tr7gyn\",\n                        \"type\": \"datetime\",\n                        \"label\": \"Date time\",\n                        \"layout\": {\n                            \"row\": \"Row_00bhde4\",\n                            \"columns\": null\n                        },\n                        \"subtype\": \"date\",\n                        \"validate\": {\n                            \"required\": true\n                        },\n                        \"dateLabel\": \"Date\"\n                    },\n                    {\n                        \"id\": \"Field_1052nm0\",\n                        \"key\": \"radio_k92er\",\n                        \"type\": \"radio\",\n                        \"label\": \"Review Outcome\",\n                        \"layout\": {\n                            \"row\": \"Row_10b4ivc\",\n                            \"columns\": null\n                        },\n                        \"values\": [\n                            {\n                                \"label\": \"Approved\",\n                                \"value\": \"Approved\"\n                            },\n                            {\n                                \"label\": \"Rejected\",\n                                \"value\": \"Rejected\"\n                            }\n                        ],\n                        \"validate\": {\n                            \"required\": true\n                        }\n                    },\n                    {\n                        \"id\": \"Field_1t2i4co\",\n                        \"key\": \"textarea_7rzp3q\",\n                        \"type\": \"textarea\",\n                        \"label\": \"Comments (if any)\",\n                        \"layout\": {\n                            \"row\": \"Row_15a50y4\",\n                            \"columns\": null\n                        }\n                    },\n                    {\n                        \"id\": \"Field_1jpxeqk\",\n                        \"type\": \"button\",\n                        \"label\": \"Submit\",\n                        \"action\": \"submit\",\n                        \"layout\": {\n                            \"row\": \"Row_1way9ch\",\n                            \"columns\": null\n                        }\n                    }\n                ],\n                \"schemaVersion\": 16\n            },\n            \"component_keys\": [\n                \"textfield_5oi7mh\",\n                \"textfield_7avnmt\",\n                \"textarea_dvllf8\",\n                \"datetime_tr7gyn\",\n                \"radio_k92er\",\n                \"textarea_7rzp3q\"\n            ],\n            \"created_at\": \"2025-01-13T08:29:29.576008Z\",\n            \"created_by\": \"2023-11-09T05:28:01.753856+00:00\",\n            \"updated_at\": \"2025-01-13T08:34:31.452414Z\",\n            \"updated_by\": \"2023-11-09T05:28:01.753856+00:00\",\n            \"tenant_id\": \"430d2014-6e57-4e8a-ad75-484aa276a5cc\",\n            \"occ_lock\": 2\n        }\n    ],\n    \"sent_at\": \"2025-01-16T04:48:52Z\",\n    \"page\": {\n        \"number\": 1,\n        \"size\": 3,\n        \"total_records\": 2,\n        \"count\": 2,\n        \"sort\": [\n            \"created_at desc\"\n        ]\n    }\n}"}]}, {"name": "Form Delete", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/form_template/:template_id", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "form_template", ":template_id"], "variable": [{"key": "template_id", "value": "38afe467-b147-4f8e-b4d7-55f7a3712b34"}]}}, "response": [{"name": "Form Delete", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/form_template/:template_id", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "form_template", ":template_id"], "variable": [{"key": "template_id", "value": "38afe467-b147-4f8e-b4d7-55f7a3712b34"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Date", "value": "Thu, 16 Jan 2025 04:50:22 GMT"}, {"key": "Content-Length", "value": "35"}], "cookie": [], "body": "{\n    \"sent_at\": \"2025-01-16T04:50:22Z\"\n}"}]}, {"name": "Service Activity List", "request": {"method": "GET", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/service_activity?page=1&size=3", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "service_activity"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "3"}]}}, "response": [{"name": "Service Activity List", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/service_activity?page=1&size=3", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "service_activity"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "3"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Date", "value": "Thu, 16 Jan 2025 04:50:38 GMT"}, {"key": "Content-Length", "value": "814"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"id\": \"179ad8ad-bb0f-4209-a5ed-28c90fdd9dc4\",\n            \"service_name\": \"wfw\",\n            \"activity_type\": \"SendEmail\",\n            \"activity_icon\": \"none\",\n            \"activity_param\": [\n                {\n                    \"SendTo\": \"string\"\n                },\n                {\n                    \"Subject\": \"string\"\n                },\n                {\n                    \"Message\": \"value\"\n                }\n            ],\n            \"activity_result\": null,\n            \"timeout_in_second\": 300\n        },\n        {\n            \"id\": \"1d02ca97-8537-4d54-ada4-653991f08171\",\n            \"service_name\": \"wfw\",\n            \"activity_type\": \"HttpCall\",\n            \"activity_icon\": \"none\",\n            \"activity_param\": [\n                {\n                    \"Url\": \"string\"\n                },\n                {\n                    \"Method\": \"string\"\n                },\n                {\n                    \"Body\": {}\n                }\n            ],\n            \"activity_result\": {\n                \"object\": {}\n            },\n            \"timeout_in_second\": 300\n        },\n        {\n            \"id\": \"e2fa732e-e86b-44b6-b974-2de1402fb957\",\n            \"service_name\": \"wfw\",\n            \"activity_type\": \"SummarizeChatGPT\",\n            \"activity_icon\": \"none\",\n            \"activity_param\": [\n                {\n                    \"Prompt\": \"string\"\n                }\n            ],\n            \"activity_result\": \"string\",\n            \"timeout_in_second\": 300\n        }\n    ],\n    \"sent_at\": \"2025-01-16T04:50:38Z\",\n    \"page\": {\n        \"number\": 1,\n        \"size\": 3,\n        \"total_records\": 8,\n        \"count\": 3,\n        \"sort\": [\n            \"\"\n        ]\n    }\n}"}]}, {"name": "Service Event List", "request": {"method": "GET", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/service_event?page=1&size=3", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "service_event"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "3"}]}}, "response": [{"name": "Service Event List", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/service_event?page=1&size=3", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "service_event"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "3"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Date", "value": "Thu, 16 Jan 2025 04:51:17 GMT"}, {"key": "Content-Length", "value": "281"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"id\": \"e393dcf5-4b6f-414b-9bba-f6741a83ea27\",\n            \"service_name\": \"wfw\",\n            \"event_type\": \"Timer\",\n            \"event_icon\": \"timer1\",\n            \"event_param\": [\n                {\n                    \"Duration\": \"string\"\n                }\n            ],\n            \"event_result\": null\n        }\n    ],\n    \"sent_at\": \"2025-01-16T04:51:17Z\",\n    \"page\": {\n        \"number\": 1,\n        \"size\": 3,\n        \"total_records\": 1,\n        \"count\": 1,\n        \"sort\": [\n            \"\"\n        ]\n    }\n}"}]}, {"name": "Validate Expression", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"expression\": \"foo && bar.baz == 'hello'\",\n    \"variables\": {\n        \"foo\": 100,\n        \"bar\": {\n            \"baz\": \"abc\"\n        }\n    }\n} ", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{WFM_ENDPOINT}}/v1/validate/expression", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "validate", "expression"]}}, "response": [{"name": "Validate Expression", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"expression\": \"foo + bar.baz\",\n    \"variables\": {\n        \"foo\": 100,\n        \"bar\": {\n            \"baz\": 200\n        }\n    }\n} ", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{WFM_ENDPOINT}}/v1/validate/expression", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "validate", "expression"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Date", "value": "Thu, 16 Jan 2025 04:54:24 GMT"}, {"key": "Content-Length", "value": "64"}], "cookie": [], "body": "{\n    \"message\": \"valid expression\",\n    \"sent_at\": \"2025-01-16T04:54:24Z\"\n}"}]}, {"name": "Validate Condition", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"expression\": \"foo * bar.baz\",\n    \"variables\": {\n        \"foo\": 100,\n        \"bar\": {\n            \"baz\": 0.1\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{WFM_ENDPOINT}}/v1/validate/condition", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "validate", "condition"]}}, "response": [{"name": "Validate Condition", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"expression\": \"foo * bar.baz == 300\",\n    \"variables\": {\n        \"foo\": 100,\n        \"bar\": {\n            \"baz\": 3\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{WFM_ENDPOINT}}/v1/validate/condition", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "validate", "condition"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Date", "value": "Thu, 16 Jan 2025 04:55:00 GMT"}, {"key": "Content-Length", "value": "63"}], "cookie": [], "body": "{\n    \"message\": \"valid condition\",\n    \"sent_at\": \"2025-01-16T04:55:00Z\"\n}"}]}, {"name": "Workflow Start", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"template_id\": \"9b04440f-0e69-4082-a502-e5f3bb1d0777\",\n    \"metadata\": {\n        \"trigger_rule_id\": \"123abc\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{WFM_ENDPOINT}}/v1/workflow", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "workflow"]}}, "response": [{"name": "Workflow Start", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"template_id\": \"6d703ef6-dcb1-4f3d-99ec-290840bc4f74\",\n    \"metadata\": {\n        \"trigger_rule_id\": \"123abc\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{WFM_ENDPOINT}}/v1/workflow", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "workflow"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Date", "value": "Thu, 16 Jan 2025 04:55:35 GMT"}, {"key": "Content-Length", "value": "97"}], "cookie": [], "body": "{\n    \"data\": {\n        \"workflow_id\": \"8ed5f2cd-d992-4677-a109-9aa9b8b235a6\"\n    },\n    \"sent_at\": \"2025-01-16T04:55:35Z\"\n}"}]}, {"name": "Workflow Execution List", "request": {"method": "GET", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/workflow?page=1&size=3&sort=start_time,asc", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "workflow"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "3"}, {"key": "sort", "value": "start_time,asc"}]}}, "response": [{"name": "Workflow Execution List", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/workflow?page=1&size=3&sort=start_time,asc", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "workflow"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "3"}, {"key": "sort", "value": "start_time,asc"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Date", "value": "Thu, 16 Jan 2025 07:53:30 GMT"}, {"key": "Content-Length", "value": "421"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"workflow_id\": \"c9be33c9-d9b0-478b-bc11-27a07bc6d19a\",\n            \"start_time\": \"2025-01-16T07:53:04.617039Z\"\n        },\n        {\n            \"workflow_id\": \"7bb93918-bfff-48ae-ab37-ed3b61a295c7\",\n            \"start_time\": \"2025-01-16T07:53:05.954141Z\"\n        },\n        {\n            \"workflow_id\": \"29efa88b-6889-4be1-bd24-3fec807bbdfe\",\n            \"start_time\": \"2025-01-16T07:53:06.828039Z\"\n        }\n    ],\n    \"sent_at\": \"2025-01-16T07:53:30Z\",\n    \"page\": {\n        \"number\": 1,\n        \"size\": 3,\n        \"total_records\": 3,\n        \"count\": 3,\n        \"sort\": [\n            \"start_time asc\"\n        ]\n    }\n}"}]}, {"name": "Workflow History", "request": {"method": "GET", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/workflow/:workflow_id?page=1&size=3&sort=timestamp,desc", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "workflow", ":workflow_id"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "3"}, {"key": "sort", "value": "timestamp,desc"}], "variable": [{"key": "workflow_id", "value": "92ac7977-29f9-4729-af21-1a588b3a3df9"}]}}, "response": [{"name": "Workflow History", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/workflow/:workflow_id?page=1&size=3&sort=timestamp,desc", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "workflow", ":workflow_id"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "3"}, {"key": "sort", "value": "timestamp,desc"}], "variable": [{"key": "workflow_id", "value": "92ac7977-29f9-4729-af21-1a588b3a3df9"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Date", "value": "Thu, 16 Jan 2025 06:28:00 GMT"}, {"key": "Content-Length", "value": "674"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"event_type\": \"WorkflowExecutionCompleted\",\n            \"timestamp\": \"2025-01-16T05:19:07.25089374Z\",\n            \"attributes\": {\n                \"result\": [\n                    {\n                        \"Activity1_String\": \"=Result_Activity.key\",\n                        \"Activity_JSON\": \"{\\\"key\\\": \\\"abc\\\"}\",\n                        \"Result_Activity\": {\n                            \"key\": \"abc\"\n                        },\n                        \"Result_Activity1\": \"abc\"\n                    }\n                ]\n            }\n        },\n        {\n            \"event_type\": \"ActivityTaskCompleted\",\n            \"timestamp\": \"2025-01-16T05:19:07.250891164Z\",\n            \"task_name\": \"End\",\n            \"task_type\": \"End\"\n        },\n        {\n            \"event_type\": \"ActivityTaskCompleted\",\n            \"timestamp\": \"2025-01-16T05:19:07.244530221Z\",\n            \"task_name\": \"Activity1\",\n            \"task_type\": \"SampleActivityString\",\n            \"attributes\": {\n                \"result\": [\n                    \"abc\"\n                ]\n            }\n        }\n    ],\n    \"sent_at\": \"2025-01-16T06:28:00Z\",\n    \"page\": {\n        \"number\": 1,\n        \"size\": 3,\n        \"total_records\": 9,\n        \"count\": 3,\n        \"sort\": [\n            \"timestamp desc\"\n        ]\n    }\n}"}]}, {"name": "Workflow Signal", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"data\": \"1. Activity_result == 1\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{WFM_ENDPOINT}}/v1/workflow/:workflow_id/activity_name/:activity_name", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "workflow", ":workflow_id", "activity_name", ":activity_name"], "variable": [{"key": "workflow_id", "value": "85547bef-e469-4014-a428-6ee9aac31194"}, {"key": "activity_name", "value": "Form"}]}}, "response": [{"name": "Signal", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"data\": {\n        \"field_0hm6j8d\": [\n            \"checked1\",\n            \"checked2\",\n            \"checked3\"\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{WFM_ENDPOINT}}/v1/signal/workflow/:workflowid/signalname/:signalname", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "signal", "workflow", ":workflowid", "signalname", ":signalname"], "variable": [{"key": "workflowid", "value": "6a7b1af0-9e8b-4a0b-b7fe-6ac332cff578_6"}, {"key": "signalname", "value": "Form"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Content-Length", "value": "105"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Date", "value": "Fri, 20 Oct 2023 08:26:39 GMT"}, {"key": "Vary", "value": "Origin"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"field_0hm6j8d\": [\n                \"checked1\",\n                \"checked2\",\n                \"checked3\"\n            ]\n        }\n    ],\n    \"sent_at\": \"2023-10-20T08:26:39Z\"\n}"}]}, {"name": "Workflow Terminate", "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\n    \"reason\": \"terminated by frontend\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{WFM_ENDPOINT}}/v1/workflow/:workflow_id", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "workflow", ":workflow_id"], "variable": [{"key": "workflow_id", "value": "8ed5f2cd-d992-4677-a109-9aa9b8b235a6"}]}}, "response": [{"name": "Workflow Terminate", "originalRequest": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\n    \"reason\": \"terminated by frontend\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{WFM_ENDPOINT}}/v1/workflow/:workflow_id", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "workflow", ":workflow_id"], "variable": [{"key": "workflow_id", "value": "8ed5f2cd-d992-4677-a109-9aa9b8b235a6"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Date", "value": "Thu, 16 Jan 2025 05:00:44 GMT"}, {"key": "Content-Length", "value": "35"}], "cookie": [], "body": "{\n    \"sent_at\": \"2025-01-16T05:00:44Z\"\n}"}]}, {"name": "Liveness", "request": {"method": "GET", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/livez", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "livez"]}}, "response": [{"name": "Liveness", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/livez", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "livez"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "plain", "header": [{"key": "Date", "value": "Thu, 16 Jan 2025 06:29:59 GMT"}, {"key": "Content-Length", "value": "0"}], "cookie": [], "body": null}]}, {"name": "Readiness", "request": {"method": "GET", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/readyz", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "readyz"]}}, "response": [{"name": "Readiness", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{WFM_ENDPOINT}}/v1/readyz", "host": ["{{WFM_ENDPOINT}}"], "path": ["v1", "readyz"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "plain", "header": [{"key": "Date", "value": "Thu, 16 Jan 2025 06:30:15 GMT"}, {"key": "Content-Length", "value": "0"}], "cookie": [], "body": null}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{BEARER_TOKEN}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": ["const token_endpoint = `${pm.collectionVariables.get(\"IAM_ENDPOINT\")}/realms/${pm.collectionVariables.get(\"IAM_REALM\")}/protocol/openid-connect/token`;", "const client_id = pm.collectionVariables.get(\"DEV_CLIENT_ID\");", "const client_secret = pm.collectionVariables.get(\"DEV_CLIENT_SECRET\");", "const username = pm.collectionVariables.get(\"DEV_USER\");", "const password = pm.collectionVariables.get(\"DEV_PASSWORD\");", "", "pm.sendRequest( {", "    method: \"POST\",", "    url: token_endpoint,", "    headers: {", "        'Content-Type': 'application/x-www-form-urlencoded',", "    },", "    body: {", "        mode: \"urlencoded\",", "        urlencoded: [", "            { key: \"client_id\", value: client_id },", "            { key: \"client_secret\", value: client_secret },", "            { key: \"scope\", value: \"openid\" },", "            { key: \"grant_type\", value: \"password\" },", "            { key: \"username\", value: username },", "            { key: \"password\", value: password },", "        ]", "    }", "}, (err, response) => {", "", "    if (err) {", "        console.error({err, token_endpoint, client_id, client_secret, username, password}, \"Unable to login. Please check variables.\");", "    } else {", "        const result = response.json();", "        const access_token = result.access_token;", "", "        if (access_token) {", "            pm.collectionVariables.set(\"BEARER_TOKEN\", access_token);", "        } else {", "           console.error({result, token_endpoint, client_id, client_secret, username, password}, \"Unable to login. Please check variables.\");", "        }", "    }", "});"]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "DEV_USER", "value": "admin", "type": "string"}, {"key": "DEV_PASSWORD", "value": "P@ssw0rd", "type": "string"}, {"key": "DEV_CLIENT_ID", "value": "iams", "type": "string"}, {"key": "DEV_CLIENT_SECRET", "value": "P@ssw0rd", "type": "string"}, {"key": "BEARER_TOKEN", "value": "", "type": "string"}, {"key": "IAM_ENDPOINT", "value": "http://iams-keycloak.localhost", "type": "string"}, {"key": "IAM_REALM", "value": "AOH", "type": "string"}]}