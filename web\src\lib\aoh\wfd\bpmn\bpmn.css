/*! JointJS+ v3.5.0 - HTML5 Diagramming Framework

Copyright (c) 2022 client IO

 2022-03-02


This Source Code Form is subject to the terms of the JointJS+ License
, v. 2.0. If a copy of the JointJS+ License was not distributed with this
file, You can obtain one at http://jointjs.com/license/rappid_v2.txt
 or from the JointJS+ archive as was distributed by client IO. See the LICENSE file.*/

/* PAPER */

.joint-paper-scroller.joint-theme-bpmn {
	width: 100%;
	height: 100%;
}

/* STENCIL */

.joint-stencil.joint-theme-bpmn {
	position: absolute;
	display: flex;
	justify-content: center;
	align-items: center;
	top: 20px;
	left: 20px;
	max-height: 510px;
	width: 60px;
	box-sizing: border-box;
	border: 1px solid hsl(var(--border));
	border-radius: 10px;
	background-color: hsl(var(--background));
}

.joint-stencil > .content {
	display: flex !important;
	justify-content: center;
	align-items: center;
}

.joint-stencil.joint-theme-bpmn .elements {
	overflow-x: auto;
	overflow-y: hidden;
}

.joint-stencil.joint-theme-bpmn .elements text,
.stencil-paper-drag .joint-element text {
	display: none;
}

/* INSPECTOR */

.joint-inspector.joint-theme-bpmn {
	position: relative;
	text-transform: none;
	background: transparent;
	resize: none;
}

.joint-inspector.joint-theme-bpmn .group {
	padding: 0 16px 8px;
	border-bottom: 1px solid hsl(var(--border));
}

.joint-inspector.joint-theme-bpmn .group > .group-label {
	font-size: 15px;
	border: none;
	height: 40px;
	line-height: 30px;
	color: hsl(var(--primary));
	font-weight: bold;
	padding-left: 30px;
	left: -10px;
}

.joint-inspector.joint-theme-bpmn .group.closed > .group-label {
	height: 40px;
	line-height: 30px;
}

.joint-inspector.joint-theme-bpmn .group.closed {
	height: 40px;
	padding: 0 16px 10px;
}

.joint-inspector.joint-theme-bpmn .group > .group-label:before {
	display: block;
	position: absolute;
	width: 8px;
	height: 8px;
	border: 2px solid hsl(var(--primary));
	border-bottom: none;
	border-left: none;
	transform: rotate(135deg);
	top: calc(50% - 6px);

	left: 10px;
	margin: 0;
}

.joint-inspector.joint-theme-bpmn .group.closed > .group-label:before {
	transform: rotate(45deg);
	top: calc(50% - 4px);
}

.joint-inspector.joint-theme-bpmn input,
.joint-inspector.joint-theme-bpmn select {
	float: none;
	width: 100%;
	height: auto;
	box-sizing: border-box;
}

/* SELECTION */

.joint-selection.joint-theme-bpmn .selection-box {
	border: 3px dotted hsl(var(--primary));
	padding-right: 6px;
	padding-bottom: 6px;
	margin-top: -6px;
	margin-left: -6px;
	border-radius: 6px;
}

/* Hiding selection-wrapper with selection halo tools for this application. */
.joint-selection.joint-theme-bpmn .selection-wrapper {
	display: none;
}

.joint-selection.lasso.joint-theme-bpmn {
	background-color: hsl(var(--accent));
	border: 2px solid hsl(var(--accent-foreground));
}

/* TOOLTIPS */

.joint-tooltip.joint-theme-bpmn {
	background: hsl(var(--primary));
	border: 1px solid hsl(var(--secondary));
	color: hsl(var(--primary));
	text-shadow: none !important;
}

.joint-tooltip.joint-theme-bpmn.bottom .tooltip-arrow-mask {
	border-top-color: hsl(var(--background));
}

.joint-tooltip.joint-theme-bpmn.bottom .tooltip-arrow {
	border-top-color: hsl(var(--primary));
}

.joint-tooltip.joint-theme-bpmn.top .tooltip-arrow-mask {
	border-bottom-color: hsl(var(--background));
}

.joint-tooltip.joint-theme-bpmn.top .tooltip-arrow {
	border-bottom-color: hsl(var(--primary));
}

/* SNAPLINES */
.snapline.horizontal {
	border-bottom: 1px dashed hsl(var(--primary));
}
.snapline.vertical {
	border-right: 1px dashed hsl(var(--primary));
}

/* SCROLLBARS */

::-webkit-scrollbar {
	width: 8px;
	height: 10px;
}
::-webkit-scrollbar-track {
	background: hsl(var(--background));
}
::-webkit-scrollbar-thumb {
	background: hsl(var(--primary));
	-webkit-box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
}
::-webkit-scrollbar-corner {
	background: hsl(var(--background));
	box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.2);
}

.joint-elements::-webkit-scrollbar {
	height: 20px;
}

.joint-elements::-webkit-scrollbar-track {
	background: hsl(var(--background));
}

.joint-elements::-webkit-scrollbar-thumb {
	background: hsl(var(--background));
	-webkit-box-shadow: inset 0 0 6px hsl(var(--background));
	border-top-left-radius: 5px;
	border-top-right-radius: 5px;
}

/* FREETRANSFORM */

.joint-free-transform {
	border: 1px dashed hsl(var(--foreground));
}

.joint-free-transform > div,
.joint-free-transform > div:hover,
.joint-free-transform.in-operation > div {
	background: hsl(var(--primary));
}

/* TOOLS */

.joint-halo.joint-theme-bpmn .handle.remove {
	background-image: none;
}

.joint-halo.joint-theme-bpmn .handle.remove:after {
	width: 25px;
	height: 25px;
	--svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath fill='%23000' d='M256 48a208 208 0 1 1 0 416a208 208 0 1 1 0-416m0 464a256 256 0 1 0 0-512a256 256 0 1 0 0 512m-81-337c-9.4 9.4-9.4 24.6 0 33.9l47 47l-47 47c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l47-47l47 47c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-47-47l47-47c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-47 47l-47-47c-9.4-9.4-24.6-9.4-33.9 0'/%3E%3C/svg%3E");
	background-color: hsl(var(--primary));
	-webkit-mask-image: var(--svg);
	mask-image: var(--svg);
	-webkit-mask-repeat: no-repeat;
	mask-repeat: no-repeat;
	-webkit-mask-size: 100% 100%;
	mask-size: 100% 100%;
}

.joint-halo.joint-theme-bpmn .handle.link {
	position: absolute;
	left: 100%;
	top: calc(50% - 18px);
	background-image: none;
}

.joint-halo.joint-theme-bpmn .handle.link:after {
	width: 25px;
	height: 25px;
	--svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 320 512'%3E%3Cpath fill='%23000' d='M310.6 233.4c12.5 12.5 12.5 32.8 0 45.3l-192 192c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L242.7 256L73.4 86.6c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l192 192z'/%3E%3C/svg%3E");
	background-color: hsl(var(--primary));
	-webkit-mask-image: var(--svg);
	mask-image: var(--svg);
	-webkit-mask-repeat: no-repeat;
	mask-repeat: no-repeat;
	-webkit-mask-size: 100% 100%;
	mask-size: 100% 100%;
}

.joint-tool[data-tool-name="button"] circle {
	stroke: hsl(var(--destructive));
	fill: hsl(var(--destructive));
	scale: 1.2;
}
.joint-tool[data-tool-name="button"] path {
	scale: 1.2;
}

.joint-tool[data-tool-name="segments"] rect,
.joint-tool[data-tool-name="vertices"] circle,
.joint-tool[data-tool-name="target-anchor"] circle,
.joint-tool[data-tool-name="source-anchor"] circle,
.joint-tool[data-tool-name="target-arrowhead"],
.joint-tool[data-tool-name="source-arrowhead"] {
	stroke: hsl(var(--foreground));
	fill: hsl(var(--background));
}

.joint-tool[data-tool-name="boundary"] {
	stroke: hsl(var(--foreground));
}

.joint-paper.joint-theme-bpmn {
	padding: 5px;
}

/* CORE */
.joint-paper.joint-theme-bpmn .joint-link .marker-arrowhead,
.joint-paper.joint-theme-bpmn .joint-link .marker-vertex {
	fill: hsl(var(--foreground));
}

.joint-paper.joint-theme-bpmn .joint-link .tool-remove circle {
	fill: hsl(var(--background));
}

.joint-paper.joint-theme-bpmn .joint-link .tool-remove > path {
	fill: hsl(var(--foreground));
}

.joint-paper.joint-theme-bpmn .connection-wrap:hover {
	opacity: 0.4;
	stroke-opacity: 0.4;
}

.joint-paper.joint-theme-bpmn .connection-wrap {
	fill: none;
	stroke: var(--background);
	stroke-width: 15;
	stroke-linecap: round;
	stroke-linejoin: round;
	opacity: 0;
	cursor: move;
}

.joint-paper.joint-theme-bpmn .marker-vertex-remove {
	fill: var(--background);
}

.joint-inspector.joint-theme-bpmn .input-wrapper {
	display: inline;
}

/* Hide "empty" paths as firefox returns a huge bounding box for them. */
@-moz-document url-prefix() {
	.joint-theme-bpmn .joint-link .marker-source[d="M 0 0"],
	.joint-theme-bpmn .joint-link .marker-target[d="M 0 0"] {
		display: none;
	}
}

/* toolbar handle */
.joint-halo.joint-theme-bpmn .handle {
	background-color: transparent;
	background-position: 0 0;
	background-repeat: no-repeat;
	background-size: 20px 20px;
}

/*  Surrounding  */
.joint-halo.joint-theme-bpmn.surrounding.type-link .handle.remove {
	background-color: hsl(var(--primary));
	border-radius: 100%;
}
/*  Surrounding  */

/*  Toolbar  */
.joint-halo.joint-theme-bpmn.toolbar .handles {
	position: static;
	display: inline-block;
	vertical-align: top;
	white-space: nowrap;
	background-color: hsl(var(--sidebar-primary-foreground));
	border: 1px solid hsl(var(--ring));
	border-bottom: 3px solid hsl(var(--ring));
	border-radius: 5px;
	margin-top: -50px;
	margin-left: 5px;
}
.joint-halo.joint-theme-bpmn.toolbar .handles:after {
	top: -10px;
	left: 15px;
	border-top: 6px solid hsl(var(--ring));
	border-right: 10px solid transparent;
	border-left: 10px solid transparent;
	margin-top: 0;
}

.joint-halo.joint-theme-bpmn.toolbar .handle {
	display: inline-block;
	vertical-align: top;
}
.joint-halo.joint-theme-bpmn.toolbar .handle + .handle {
	margin-left: 4px;
}

/* Toolbar for element */
.joint-halo.joint-theme-bpmn.toolbar.type-element .handle.remove {
	position: absolute;
	right: 100%;
	bottom: 100%;
	margin-right: 6px;
	margin-bottom: 3px;
}
/* Toolbar for element */

/* Toolbar for link */

.joint-halo.joint-theme-bpmn.toolbar.type-link .handles {
	margin-top: -60px;
	margin-left: -18px;
}
.joint-halo.joint-theme-bpmn.toolbar.type-link .handles:after {
	top: -22px;
	left: -9px;
}
/* Toolbar for link */

/*  Toolbar  */
