docker build . --file Dockerfile -t hocsvn/hcc3-wfe-designer \
    --build-arg PORT=8080 \
    --build-arg ORIGIN=https://cc3-hoc-wfd.dratini.tech \
    --build-arg IAM_URL=https://cc3-hoc-keycloak.dratini.tech/realms/AOH/.well-known/openid-configuration \
    --build-arg IAM_CLIENT_ID=wfm_client \
    --build-arg PUBLIC_DOMAIN=dratini.tech \
    --build-arg PUBLIC_COOKIE_PREFIX=wfd \
    --build-arg OIDC_ALLOW_INSECURE_REQUESTS=1 \
    --build-arg LOGIN_DESTINATION=/aoh/wfd \
    --build-arg WFM_URL=https://cc3-hoc-wfm.dratini.tech \
    --build-arg ACTIVITY_AAS_URL=https://cc3-hoc-iams.dratini.tech \
    --build-arg DATA_AGG_URL=https://data-agg-api.dratini.tech \
    --build-arg TEAMS_URL=http://54.254.97.15:8787

docker logout

docker login -u <EMAIL> -p cfgparameter@123

docker push hocsvn/hcc3-wfe-designer
