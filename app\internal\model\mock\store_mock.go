// Code generated by MockGen. DO NOT EDIT.
// Source: interfaces.go
//
// Generated by this command:
//
//	mockgen -source=interfaces.go -destination=mock/store_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	model "github.com/mssfoobar/app/wfe/internal/model"
	gomock "go.uber.org/mock/gomock"
)

// MockWorkflowTemplateStore is a mock of WorkflowTemplateStore interface.
type MockWorkflowTemplateStore struct {
	ctrl     *gomock.Controller
	recorder *MockWorkflowTemplateStoreMockRecorder
}

// MockWorkflowTemplateStoreMockRecorder is the mock recorder for MockWorkflowTemplateStore.
type MockWorkflowTemplateStoreMockRecorder struct {
	mock *MockWorkflowTemplateStore
}

// NewMockWorkflowTemplateStore creates a new mock instance.
func NewMockWorkflowTemplateStore(ctrl *gomock.Controller) *MockWorkflowTemplateStore {
	mock := &MockWorkflowTemplateStore{ctrl: ctrl}
	mock.recorder = &MockWorkflowTemplateStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWorkflowTemplateStore) EXPECT() *MockWorkflowTemplateStoreMockRecorder {
	return m.recorder
}

// DeleteWorkflowTemplate mocks base method.
func (m *MockWorkflowTemplateStore) DeleteWorkflowTemplate(ctx context.Context, request *model.DeleteWorkflowTemplateRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteWorkflowTemplate", ctx, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteWorkflowTemplate indicates an expected call of DeleteWorkflowTemplate.
func (mr *MockWorkflowTemplateStoreMockRecorder) DeleteWorkflowTemplate(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteWorkflowTemplate", reflect.TypeOf((*MockWorkflowTemplateStore)(nil).DeleteWorkflowTemplate), ctx, request)
}

// GetWorkflowTemplate mocks base method.
func (m *MockWorkflowTemplateStore) GetWorkflowTemplate(ctx context.Context, request *model.GetWorkflowTemplateRequest) (*model.WorkflowTemplateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflowTemplate", ctx, request)
	ret0, _ := ret[0].(*model.WorkflowTemplateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflowTemplate indicates an expected call of GetWorkflowTemplate.
func (mr *MockWorkflowTemplateStoreMockRecorder) GetWorkflowTemplate(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflowTemplate", reflect.TypeOf((*MockWorkflowTemplateStore)(nil).GetWorkflowTemplate), ctx, request)
}

// ListWorkflowTemplate mocks base method.
func (m *MockWorkflowTemplateStore) ListWorkflowTemplate(ctx context.Context, request *model.ListWorkflowTemplateRequest) (*model.ListWorkflowTemplateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListWorkflowTemplate", ctx, request)
	ret0, _ := ret[0].(*model.ListWorkflowTemplateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListWorkflowTemplate indicates an expected call of ListWorkflowTemplate.
func (mr *MockWorkflowTemplateStoreMockRecorder) ListWorkflowTemplate(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListWorkflowTemplate", reflect.TypeOf((*MockWorkflowTemplateStore)(nil).ListWorkflowTemplate), ctx, request)
}

// PublishWorkflowTemplate mocks base method.
func (m *MockWorkflowTemplateStore) PublishWorkflowTemplate(ctx context.Context, request *model.WorkflowTemplateRequest) (*model.WorkflowTemplateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishWorkflowTemplate", ctx, request)
	ret0, _ := ret[0].(*model.WorkflowTemplateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PublishWorkflowTemplate indicates an expected call of PublishWorkflowTemplate.
func (mr *MockWorkflowTemplateStoreMockRecorder) PublishWorkflowTemplate(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishWorkflowTemplate", reflect.TypeOf((*MockWorkflowTemplateStore)(nil).PublishWorkflowTemplate), ctx, request)
}

// SaveWorkflowTemplate mocks base method.
func (m *MockWorkflowTemplateStore) SaveWorkflowTemplate(ctx context.Context, request *model.WorkflowTemplateRequest) (*model.WorkflowTemplateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveWorkflowTemplate", ctx, request)
	ret0, _ := ret[0].(*model.WorkflowTemplateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveWorkflowTemplate indicates an expected call of SaveWorkflowTemplate.
func (mr *MockWorkflowTemplateStoreMockRecorder) SaveWorkflowTemplate(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveWorkflowTemplate", reflect.TypeOf((*MockWorkflowTemplateStore)(nil).SaveWorkflowTemplate), ctx, request)
}

// MockFormTemplateStore is a mock of FormTemplateStore interface.
type MockFormTemplateStore struct {
	ctrl     *gomock.Controller
	recorder *MockFormTemplateStoreMockRecorder
}

// MockFormTemplateStoreMockRecorder is the mock recorder for MockFormTemplateStore.
type MockFormTemplateStoreMockRecorder struct {
	mock *MockFormTemplateStore
}

// NewMockFormTemplateStore creates a new mock instance.
func NewMockFormTemplateStore(ctrl *gomock.Controller) *MockFormTemplateStore {
	mock := &MockFormTemplateStore{ctrl: ctrl}
	mock.recorder = &MockFormTemplateStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFormTemplateStore) EXPECT() *MockFormTemplateStoreMockRecorder {
	return m.recorder
}

// DeleteFormTemplate mocks base method.
func (m *MockFormTemplateStore) DeleteFormTemplate(ctx context.Context, request *model.DeleteFormTemplateRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteFormTemplate", ctx, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteFormTemplate indicates an expected call of DeleteFormTemplate.
func (mr *MockFormTemplateStoreMockRecorder) DeleteFormTemplate(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteFormTemplate", reflect.TypeOf((*MockFormTemplateStore)(nil).DeleteFormTemplate), ctx, request)
}

// GetFormTemplate mocks base method.
func (m *MockFormTemplateStore) GetFormTemplate(ctx context.Context, request *model.GetFormTemplateRequest) (*model.FormTemplateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFormTemplate", ctx, request)
	ret0, _ := ret[0].(*model.FormTemplateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFormTemplate indicates an expected call of GetFormTemplate.
func (mr *MockFormTemplateStoreMockRecorder) GetFormTemplate(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFormTemplate", reflect.TypeOf((*MockFormTemplateStore)(nil).GetFormTemplate), ctx, request)
}

// ListFormTemplate mocks base method.
func (m *MockFormTemplateStore) ListFormTemplate(ctx context.Context, request *model.ListFormTemplateRequest) (*model.ListFormTemplateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListFormTemplate", ctx, request)
	ret0, _ := ret[0].(*model.ListFormTemplateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListFormTemplate indicates an expected call of ListFormTemplate.
func (mr *MockFormTemplateStoreMockRecorder) ListFormTemplate(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListFormTemplate", reflect.TypeOf((*MockFormTemplateStore)(nil).ListFormTemplate), ctx, request)
}

// SaveFormTemplate mocks base method.
func (m *MockFormTemplateStore) SaveFormTemplate(ctx context.Context, request *model.FormTemplateRequest) (*model.FormTemplateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveFormTemplate", ctx, request)
	ret0, _ := ret[0].(*model.FormTemplateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveFormTemplate indicates an expected call of SaveFormTemplate.
func (mr *MockFormTemplateStoreMockRecorder) SaveFormTemplate(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveFormTemplate", reflect.TypeOf((*MockFormTemplateStore)(nil).SaveFormTemplate), ctx, request)
}

// MockServiceActivityStore is a mock of ServiceActivityStore interface.
type MockServiceActivityStore struct {
	ctrl     *gomock.Controller
	recorder *MockServiceActivityStoreMockRecorder
}

// MockServiceActivityStoreMockRecorder is the mock recorder for MockServiceActivityStore.
type MockServiceActivityStoreMockRecorder struct {
	mock *MockServiceActivityStore
}

// NewMockServiceActivityStore creates a new mock instance.
func NewMockServiceActivityStore(ctrl *gomock.Controller) *MockServiceActivityStore {
	mock := &MockServiceActivityStore{ctrl: ctrl}
	mock.recorder = &MockServiceActivityStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceActivityStore) EXPECT() *MockServiceActivityStoreMockRecorder {
	return m.recorder
}

// CreateServiceActivity mocks base method.
func (m *MockServiceActivityStore) CreateServiceActivity(ctx context.Context, request *model.CreateServiceActivityRequest) (*model.ServiceActivityResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateServiceActivity", ctx, request)
	ret0, _ := ret[0].(*model.ServiceActivityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateServiceActivity indicates an expected call of CreateServiceActivity.
func (mr *MockServiceActivityStoreMockRecorder) CreateServiceActivity(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateServiceActivity", reflect.TypeOf((*MockServiceActivityStore)(nil).CreateServiceActivity), ctx, request)
}

// DeleteServiceActivity mocks base method.
func (m *MockServiceActivityStore) DeleteServiceActivity(ctx context.Context, request *model.DeleteServiceActivityRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteServiceActivity", ctx, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteServiceActivity indicates an expected call of DeleteServiceActivity.
func (mr *MockServiceActivityStoreMockRecorder) DeleteServiceActivity(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteServiceActivity", reflect.TypeOf((*MockServiceActivityStore)(nil).DeleteServiceActivity), ctx, request)
}

// GetServiceActivity mocks base method.
func (m *MockServiceActivityStore) GetServiceActivity(ctx context.Context, request *model.GetServiceActivityRequest) (*model.ServiceActivityResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceActivity", ctx, request)
	ret0, _ := ret[0].(*model.ServiceActivityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceActivity indicates an expected call of GetServiceActivity.
func (mr *MockServiceActivityStoreMockRecorder) GetServiceActivity(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceActivity", reflect.TypeOf((*MockServiceActivityStore)(nil).GetServiceActivity), ctx, request)
}

// ListServiceActivity mocks base method.
func (m *MockServiceActivityStore) ListServiceActivity(ctx context.Context, request *model.ListServiceActivityRequest) (*model.ListServiceActivityResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListServiceActivity", ctx, request)
	ret0, _ := ret[0].(*model.ListServiceActivityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListServiceActivity indicates an expected call of ListServiceActivity.
func (mr *MockServiceActivityStoreMockRecorder) ListServiceActivity(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListServiceActivity", reflect.TypeOf((*MockServiceActivityStore)(nil).ListServiceActivity), ctx, request)
}

// UpdateServiceActivity mocks base method.
func (m *MockServiceActivityStore) UpdateServiceActivity(ctx context.Context, request *model.UpdateServiceActivityRequest) (*model.ServiceActivityResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateServiceActivity", ctx, request)
	ret0, _ := ret[0].(*model.ServiceActivityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateServiceActivity indicates an expected call of UpdateServiceActivity.
func (mr *MockServiceActivityStoreMockRecorder) UpdateServiceActivity(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateServiceActivity", reflect.TypeOf((*MockServiceActivityStore)(nil).UpdateServiceActivity), ctx, request)
}

// MockServiceEventStore is a mock of ServiceEventStore interface.
type MockServiceEventStore struct {
	ctrl     *gomock.Controller
	recorder *MockServiceEventStoreMockRecorder
}

// MockServiceEventStoreMockRecorder is the mock recorder for MockServiceEventStore.
type MockServiceEventStoreMockRecorder struct {
	mock *MockServiceEventStore
}

// NewMockServiceEventStore creates a new mock instance.
func NewMockServiceEventStore(ctrl *gomock.Controller) *MockServiceEventStore {
	mock := &MockServiceEventStore{ctrl: ctrl}
	mock.recorder = &MockServiceEventStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceEventStore) EXPECT() *MockServiceEventStoreMockRecorder {
	return m.recorder
}

// CreateServiceEvent mocks base method.
func (m *MockServiceEventStore) CreateServiceEvent(ctx context.Context, request *model.CreateServiceEventRequest) (*model.ServiceEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateServiceEvent", ctx, request)
	ret0, _ := ret[0].(*model.ServiceEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateServiceEvent indicates an expected call of CreateServiceEvent.
func (mr *MockServiceEventStoreMockRecorder) CreateServiceEvent(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateServiceEvent", reflect.TypeOf((*MockServiceEventStore)(nil).CreateServiceEvent), ctx, request)
}

// DeleteServiceEvent mocks base method.
func (m *MockServiceEventStore) DeleteServiceEvent(ctx context.Context, request *model.DeleteServiceEventRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteServiceEvent", ctx, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteServiceEvent indicates an expected call of DeleteServiceEvent.
func (mr *MockServiceEventStoreMockRecorder) DeleteServiceEvent(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteServiceEvent", reflect.TypeOf((*MockServiceEventStore)(nil).DeleteServiceEvent), ctx, request)
}

// GetServiceEvent mocks base method.
func (m *MockServiceEventStore) GetServiceEvent(ctx context.Context, request *model.GetServiceEventRequest) (*model.ServiceEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceEvent", ctx, request)
	ret0, _ := ret[0].(*model.ServiceEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceEvent indicates an expected call of GetServiceEvent.
func (mr *MockServiceEventStoreMockRecorder) GetServiceEvent(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceEvent", reflect.TypeOf((*MockServiceEventStore)(nil).GetServiceEvent), ctx, request)
}

// ListServiceEvent mocks base method.
func (m *MockServiceEventStore) ListServiceEvent(ctx context.Context, request *model.ListServiceEventRequest) (*model.ListServiceEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListServiceEvent", ctx, request)
	ret0, _ := ret[0].(*model.ListServiceEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListServiceEvent indicates an expected call of ListServiceEvent.
func (mr *MockServiceEventStoreMockRecorder) ListServiceEvent(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListServiceEvent", reflect.TypeOf((*MockServiceEventStore)(nil).ListServiceEvent), ctx, request)
}

// UpdateServiceEvent mocks base method.
func (m *MockServiceEventStore) UpdateServiceEvent(ctx context.Context, request *model.UpdateServiceEventRequest) (*model.ServiceEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateServiceEvent", ctx, request)
	ret0, _ := ret[0].(*model.ServiceEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateServiceEvent indicates an expected call of UpdateServiceEvent.
func (mr *MockServiceEventStoreMockRecorder) UpdateServiceEvent(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateServiceEvent", reflect.TypeOf((*MockServiceEventStore)(nil).UpdateServiceEvent), ctx, request)
}
