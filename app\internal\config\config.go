package config

import (
	"errors"
	"os"
	"time"

	"github.com/caarlos0/env/v10"
	"github.com/joho/godotenv"
)

type (
	Option struct {
		Port     string `env:"APP_PORT" envDefault:"5000"`
		LogLevel string `env:"LOG_LEVEL" envDefault:"info"`
		Keycloak `envPrefix:"IAMS_KEYCLOAK_"`
		Temporal `envPrefix:"TEMPORAL_"`
		SQL      `envPrefix:"SQL_"`
		Hasura   `envPrefix:"HASURA_"`
		Ucs      `envPrefix:"UCS_"`
		Ims      `envPrefix:"IMS_"`
		Smtp     `envPrefix:"SMTP_"`
		ChatGpt  `envPrefix:"CHAT_GPT_"`
		Ian      `envPrefix:"IAN_"`
	}

	Keycloak struct {
		Url string `env:"URL"`
	}

	Temporal struct {
		Host      string `env:"HOST" envDefault:"localhost"`
		Port      string `env:"PORT" envDefault:"7233"`
		Namespace string `env:"NAMESPACE" envDefault:"default"`
		TaskQueue string `env:"TASKQUEUE" envDefault:"taskqueue"`
	}

	SQL struct {
		Host            string        `env:"HOST" envDefault:"localhost"`
		Port            string        `env:"PORT" envDefault:"5432"`
		User            string        `env:"USER" envDefault:"postgres"`
		Password        string        `env:"PASSWORD" envDefault:"postgres"`
		PluginName      string        `env:"PLUGIN_NAME" envDefault:"postgres"`
		DatabaseName    string        `env:"DATABASE_NAME" envDefault:"postgres"`
		SchemaName      string        `env:"SCHEMA_NAME" envDefault:"public"`
		MaxConns        int           `env:"MAX_CONNS" envDefault:"10"`
		MaxIdleConns    int           `env:"MAX_IDLE_CONNS" envDefault:"10"`
		MaxConnLifetime time.Duration `env:"MAX_CONN_LIFETIME" envDefault:"10m"`
		SslMode         string        `env:"SSL_MODE" envDefault:"disable"`
	}

	Hasura struct {
		AdminKey string `env:"ADMIN_KEY"`
	}

	Ucs struct {
		Url string `env:"URL"`
	}

	Ims struct {
		Url string `env:"URL"`
	}

	Smtp struct {
		Host     string `env:"HOST"`
		Port     string `env:"PORT"`
		Username string `env:"USERNAME"`
		Password string `env:"PASSWORD"`
		From     string `env:"FROM"`
	}

	ChatGpt struct {
		Url    string `env:"URL"`
		ApiKey string `env:"API_KEY"`
	}

	Ian struct {
		Url string `env:"URL"`
	}
)

var AppConfig *Option

// Init load env from file into os and parse env to AppConfig
func Init(file string) (err error) {
	opt := Option{}
	if err = godotenv.Load(file); err != nil && !errors.Is(err, os.ErrNotExist) {
		return err
	}
	if err = env.Parse(&opt); err != nil {
		return err
	}
	AppConfig = &opt
	return nil
}
