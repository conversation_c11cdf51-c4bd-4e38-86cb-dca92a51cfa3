FROM node:slim AS build

ARG PORT
ARG ORIGIN
ARG IAM_URL
ARG IAM_CLIENT_ID
ARG PUBLIC_DOMAIN
ARG PUBLIC_COOKIE_PREFIX
ARG LOGIN_DESTINATION
ARG LOGIN_PAGE
ARG OIDC_ALLOW_INSECURE_REQUESTS
ARG WFM_URL
ARG ACTIVITY_AAS_URL
ARG DATA_AGG_URL

ENV PORT=${PORT}
ENV ORIGIN=${ORIGIN}
ENV IAM_URL=${IAM_URL}
ENV IAM_CLIENT_ID=${IAM_CLIENT_ID}
ENV PUBLIC_DOMAIN=${PUBLIC_DOMAIN}
ENV PUBLIC_COOKIE_PREFIX=${PUBLIC_COOKIE_PREFIX}
ENV LOGIN_DESTINATION=${LOGIN_DESTINATION}
ENV LOGIN_PAGE=${LOGIN_PAGE}
ENV OIDC_ALLOW_INSECURE_REQUESTS=${OIDC_ALLOW_INSECURE_REQUESTS}
ENV WFM_URL=${WFM_URL}
ENV ACTIVITY_AAS_URL=${ACTIVITY_AAS_URL}
ENV DATA_AGG_URL=${DATA_AGG_URL}

ARG PUBLIC_STATIC_BUILD_VERSION

ENV NODE_OPTIONS=--max_old_space_size=8192
ENV PUBLIC_STATIC_BUILD_VERSION=${PUBLIC_STATIC_BUILD_VERSION}

WORKDIR /app

COPY package*.json ./
COPY rappid.tgz ./

RUN mkdir node_modules

COPY . .

RUN node -e "require('v8'); console.table(v8.getHeapStatistics());"
RUN npm pkg delete scripts.prepare
RUN npm i 
RUN npm run build && npm prune --production

FROM node:lts-alpine

WORKDIR /app

# Only copy in what we need
COPY --from=build /app/build ./build
COPY --from=build /app/node_modules ./node_modules
COPY package.json .

# Let our deployment know this is running in a container
ENV IS_CONTAINER=true

ARG PORT
ARG ORIGIN
ARG IAM_URL
ARG IAM_CLIENT_ID
ARG PUBLIC_DOMAIN
ARG PUBLIC_COOKIE_PREFIX
ARG LOGIN_DESTINATION
ARG LOGIN_PAGE
ARG OIDC_ALLOW_INSECURE_REQUESTS
ARG WFM_URL
ARG ACTIVITY_AAS_URL
ARG DATA_AGG_URL

ENV PORT=${PORT}
ENV ORIGIN=${ORIGIN}
ENV IAM_URL=${IAM_URL}
ENV IAM_CLIENT_ID=${IAM_CLIENT_ID}
ENV PUBLIC_DOMAIN=${PUBLIC_DOMAIN}
ENV PUBLIC_COOKIE_PREFIX=${PUBLIC_COOKIE_PREFIX}
ENV LOGIN_DESTINATION=${LOGIN_DESTINATION}
ENV LOGIN_PAGE=${LOGIN_PAGE}
ENV OIDC_ALLOW_INSECURE_REQUESTS=${OIDC_ALLOW_INSECURE_REQUESTS}
ENV WFM_URL=${WFM_URL}
ENV ACTIVITY_AAS_URL=${ACTIVITY_AAS_URL}
ENV DATA_AGG_URL=${DATA_AGG_URL}

CMD ["node", "build"]