package handler

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/require"
)

func Test_registerHealth(t *testing.T) {
	t.Run("livez", func(t *testing.T) {
		handler := registerHealth()
		req, err := http.NewRequest(http.MethodGet, "/livez", nil)
		if err != nil {
			t.Fatal(err)
		}
		w := httptest.NewRecorder()
		handler.ServeHTTP(w, req)
		require.Equal(t, http.StatusOK, w.Code)
	})
	t.Run("readyz", func(t *testing.T) {
		handler := registerHealth()
		req, err := http.NewRequest(http.MethodGet, "/readyz", nil)
		if err != nil {
			t.Fatal(err)
		}
		w := httptest.NewRecorder()
		handler.ServeHTTP(w, req)
		require.Equal(t, http.StatusOK, w.Code)
	})
}
