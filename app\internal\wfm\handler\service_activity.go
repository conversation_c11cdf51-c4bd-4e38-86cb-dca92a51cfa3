package handler

import (
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
	aohhttp "github.com/mssfoobar/aoh-golib/http"
	aohlog "github.com/mssfoobar/aoh-golib/logger"
	"github.com/mssfoobar/app/wfe/internal/model"
	"github.com/mssfoobar/app/wfe/internal/wfm/handler/util"
	"go.uber.org/zap"
)

type ServiceActivity struct {
	store model.ServiceActivityStore
}

func RegisterServiceActivity(factory *model.Factory, keycloakUrl string) http.Handler {
	r := chi.NewRouter()
	util.SetMiddleware(r, keycloakUrl)

	a := ServiceActivity{
		store: factory.NewServiceActivityStore(),
	}

	r.Route("/", func(r chi.Router) {
		r.Get("/", a.List)
	})

	return r
}

// List retrieves a list of service activity
func (a *ServiceActivity) List(w http.ResponseWriter, r *http.Request) {
	page, err := util.GetQueryPagination(w, r)
	if err != nil {
		return
	}

	resp, err := a.store.ListServiceActivity(r.Context(), &model.ListServiceActivityRequest{
		Page: *page,
	})
	if err != nil {
		aohlog.Error("[ServiceActivity] list service activity failed",
			zap.Error(err))
		code, err := util.CheckStoreError(err)
		_ = render.Render(w, r, aohhttp.ErrResponse(code, "", []error{err}))
		return
	}

	respData := append(make([]model.ServiceActivityResponse, 0), resp.ServiceActivities...)
	respPage := aohhttp.PageResponse{
		Number:       page.Number,
		Size:         page.Size,
		TotalRecords: resp.TotalCount,
		Count:        len(respData),
		Sort:         []string{page.Sorts.String()},
	}

	aohlog.Info("[ServiceActivity] list service activity successfully", zap.Int("count", len(respData)))
	_ = render.Render(w, r, aohhttp.PaginationResponse(http.StatusOK, "", respPage, respData))
}
