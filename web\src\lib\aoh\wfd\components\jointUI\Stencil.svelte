<script lang="ts">
	import {
		Activity,
		CallActivity,
		End,
		Terminate,
		Event,
		Form,
		Parallel,
		Start,
		Switch,
	} from "$lib/aoh/wfd/bpmn/shapes";
	import { ui, type dia, layout } from "rappid/rappid";

	interface Props {
		paper: dia.Paper;
		snaplines?: ui.Snaplines;
		elementDrop?: (view: dia.ElementView) => void;
	}

	let { paper, snaplines, elementDrop }: Props = $props();

	let container: HTMLElement;

	$effect(() => {
		const stencil = new ui.Stencil({
			paper,
			width: 42,
			height: 500,
			snaplines: snaplines,
			dragStartClone: function (cell: dia.Cell) {
				// retain default attributes
				const clone = cell.clone();
				clone.attributes = clone.defaults();
				return clone;
			},
		});
		const elements = [
			new Activity({
				size: {
					width: 40,
					height: 32,
				},
			}),
			new Form({
				size: {
					width: 40,
					height: 32,
				},
			}),
			new CallActivity({
				size: {
					width: 40,
					height: 32,
				},
			}),
			new Event(),
			new Start({
				attrs: {
					label: {
						text: "",
					},
				},
			}),
			new End({
				attrs: {
					label: {
						text: "",
					},
				},
			}),
			new Terminate({
				attrs: {
					label: {
						text: "",
					},
				},
			}),
			new Switch({
				attrs: {
					label: {
						text: "",
					},
				},
			}),
			new Parallel({
				attrs: {
					label: {
						text: "",
					},
				},
			}),
		];
		stencil.render();
		stencil.load(elements);

		stencil.on("element:drop", function (elementView: dia.ElementView) {
			elementDrop?.(elementView);
		});

		container.replaceChildren(stencil.el);

		layout.GridLayout.layout(stencil.getGraph(), {
			columnWidth: "compact",
			marginY: 10,
			marginX: 1,
			rowGap: 15,
			verticalAlign: "middle",
		});
	});
</script>

<div class="w-full" bind:this={container}></div>
