package temporal

import (
	"context"
	"encoding/json"
	"errors"
	"slices"
	"time"

	aohlog "github.com/mssfoobar/aoh-golib/logger"
	"github.com/mssfoobar/aoh-golib/temporal"
	"github.com/mssfoobar/app/wfe/internal/config"
	"github.com/mssfoobar/app/wfe/internal/wfe"
	"github.com/mssfoobar/app/wfe/internal/wfe/dsl"
	constant "github.com/mssfoobar/app/wfe/internal/wfm/common"
	"go.temporal.io/api/common/v1"
	"go.temporal.io/api/enums/v1"
	"go.temporal.io/api/history/v1"
	"go.temporal.io/api/workflowservice/v1"
	"go.temporal.io/sdk/client"
)

type ServiceImpl struct {
	client client.Client
	conf   config.Temporal
}

func NewService(client temporal.Client, conf config.Temporal) *ServiceImpl {
	return &ServiceImpl{client: client, conf: conf}
}

func (t *ServiceImpl) ExecuteWorkflow(ctx context.Context, workflowSchema json.RawMessage, metadata map[string]any) (*WorkflowRun, error) {
	var dslWorkflow dsl.Workflow
	if err := json.Unmarshal(workflowSchema, &dslWorkflow); err != nil {
		return nil, err
	}

	workflowOptions := client.StartWorkflowOptions{
		TaskQueue: t.conf.TaskQueue,
	}

	ctxWithValue := temporal.ContextWithValue(ctx, metadata)

	we, err := t.client.ExecuteWorkflow(
		ctxWithValue,
		workflowOptions,
		wfe.DslWorkflowType,
		dslWorkflow,
	)
	if err != nil {
		return nil, err
	}

	return &WorkflowRun{WorkflowId: we.GetID()}, nil
}

func (t *ServiceImpl) TerminateWorkflow(ctx context.Context, workflowId string, reason string) error {
	return t.client.TerminateWorkflow(ctx, workflowId, "", reason)
}

func (t *ServiceImpl) SignalWorkflow(ctx context.Context, workflowId, signalName string, data any) error {
	return t.client.SignalWorkflow(ctx, workflowId, "", signalName, data)
}

func (t *ServiceImpl) ListOpenWorkflow(ctx context.Context, page, size int, asc bool) ([]WorkflowExecution, int, error) {
	workflows, err := t.client.ListWorkflow(ctx, &workflowservice.ListWorkflowExecutionsRequest{
		Namespace: t.conf.Namespace,
		Query:     "ExecutionStatus='Running'",
	})
	if err != nil {
		return nil, 0, err
	}
	if asc {
		slices.Reverse(workflows.Executions)
	}
	workflowExecInfos, err := paginate(workflows.Executions, page, size)
	if err != nil {
		return nil, 0, err
	}

	var workflowExecutions []WorkflowExecution
	for _, info := range workflowExecInfos {
		workflowExecutions = append(workflowExecutions, WorkflowExecution{
			WorkflowId: info.GetExecution().GetWorkflowId(),
			StartTime:  info.GetStartTime().AsTime().Format(time.RFC3339Nano),
		})
	}

	return workflowExecutions, len(workflows.Executions), err
}

func (t *ServiceImpl) GetWorkflowHistory(ctx context.Context, id string, page, size int, desc bool) ([]HistoryEvent, int, error) {
	iter := t.client.GetWorkflowHistory(
		ctx,
		id,
		"",
		false,
		enums.HISTORY_EVENT_FILTER_TYPE_ALL_EVENT,
	)

	var histEvents []HistoryEvent
	for iter.HasNext() {
		event, err := iter.Next()
		if err != nil {
			return nil, 0, err
		}
		hist, err := getHistoryEvent(event, histEvents)
		if err != nil {
			aohlog.Error(err.Error())
		}
		if hist != nil {
			histEvents = append(histEvents, *hist)
		}
	}

	if desc {
		slices.Reverse(histEvents)
	}

	pageHist, err := paginate(histEvents, page, size)
	if err != nil {
		return nil, 0, err
	}

	return pageHist, len(histEvents), nil
}

func paginate[T any](list []T, pageNumber int, pageSize int) ([]T, error) {
	if pageNumber < 1 || pageSize < 1 {
		return nil, errors.New("invalid page number or page size")
	}

	offset := (pageNumber - 1) * pageSize
	limit := pageSize

	if offset > len(list) {
		return []T{}, nil
	}

	end := offset + limit
	if end > len(list) {
		end = len(list)
	}

	return list[offset:end], nil
}

func getHistoryEvent(evt *history.HistoryEvent, histEvents []HistoryEvent) (*HistoryEvent, error) {
	var hist HistoryEvent
	var err error
	switch evt.GetEventType() {
	case enums.EVENT_TYPE_WORKFLOW_EXECUTION_STARTED:
		hist, err = workflowExecutionStarted(evt)
	case enums.EVENT_TYPE_WORKFLOW_EXECUTION_TERMINATED:
		hist = workflowExecutionTerminated(evt)
	case enums.EVENT_TYPE_WORKFLOW_EXECUTION_COMPLETED:
		hist, err = workflowExecutionCompleted(evt)
	case enums.EVENT_TYPE_WORKFLOW_EXECUTION_FAILED:
		hist = workflowExecutionFailed(evt)
	case enums.EVENT_TYPE_WORKFLOW_EXECUTION_CANCELED:
		hist, err = workflowExecutionCanceled(evt)
	case enums.EVENT_TYPE_ACTIVITY_TASK_SCHEDULED:
		hist, err = activityTaskScheduled(evt)
	case enums.EVENT_TYPE_ACTIVITY_TASK_STARTED:
		hist, err = activityTaskStarted(evt, histEvents)
	case enums.EVENT_TYPE_ACTIVITY_TASK_COMPLETED:
		hist, err = activityTaskCompleted(evt, histEvents)
	case enums.EVENT_TYPE_ACTIVITY_TASK_CANCEL_REQUESTED:
		hist, err = activityTaskCanceledRequested(evt, histEvents)
	case enums.EVENT_TYPE_ACTIVITY_TASK_CANCELED:
		hist, err = activityTaskCanceled(evt, histEvents)
	case enums.EVENT_TYPE_ACTIVITY_TASK_FAILED:
		hist, err = activityTaskFailed(evt, histEvents)
	case enums.EVENT_TYPE_START_CHILD_WORKFLOW_EXECUTION_INITIATED:
		hist, err = childWorkflowInitiated(evt)
	case enums.EVENT_TYPE_CHILD_WORKFLOW_EXECUTION_STARTED:
		hist, err = childWorkflowStarted(evt, histEvents)
	case enums.EVENT_TYPE_CHILD_WORKFLOW_EXECUTION_COMPLETED:
		hist, err = childWorkflowCompleted(evt, histEvents)
	case enums.EVENT_TYPE_CHILD_WORKFLOW_EXECUTION_FAILED:
		hist, err = childWorkflowFailed(evt, histEvents)
	case enums.EVENT_TYPE_MARKER_RECORDED:
		hist, err = eventTypeMarkerRecorded(evt)
	default:
		return nil, nil
	}
	return &hist, err
}

func workflowExecutionStarted(evt *history.HistoryEvent) (hist HistoryEvent, err error) {
	hist = newHistoryEvent(evt)
	attr := evt.GetWorkflowExecutionStartedEventAttributes()
	hist.Attributes, err = unmarshalPayload(attr.GetInput().GetPayloads(), constant.WorkflowInput)
	return
}

func workflowExecutionTerminated(evt *history.HistoryEvent) (hist HistoryEvent) {
	hist = newHistoryEvent(evt)
	attr := evt.GetWorkflowExecutionTerminatedEventAttributes()
	if attr.GetReason() == constant.WorkflowTerminateEndEvent {
		hist.EventType = enums.EVENT_TYPE_WORKFLOW_EXECUTION_COMPLETED.String()
	}
	if attr.GetReason() != "" {
		hist.Attributes = &map[string]any{constant.WorkflowReason: attr.GetReason()}
	}
	return
}

func workflowExecutionCompleted(evt *history.HistoryEvent) (hist HistoryEvent, err error) {
	hist = newHistoryEvent(evt)
	attr := evt.GetWorkflowExecutionCompletedEventAttributes()
	hist.Attributes, err = unmarshalPayload(attr.GetResult().GetPayloads(), constant.WorkflowResult)
	return
}

func workflowExecutionFailed(evt *history.HistoryEvent) (hist HistoryEvent) {
	hist = newHistoryEvent(evt)
	attr := evt.GetWorkflowExecutionFailedEventAttributes()
	if attr.GetFailure() != nil {
		hist.Attributes = &map[string]any{constant.WorkflowFailure: attr.GetFailure().GetMessage()}
	}
	return
}

func workflowExecutionCanceled(evt *history.HistoryEvent) (hist HistoryEvent, err error) {
	hist = newHistoryEvent(evt)
	details := evt.GetWorkflowExecutionCanceledEventAttributes().GetDetails()
	hist.Attributes, err = unmarshalPayload(details.GetPayloads(), constant.WorkflowDetails)
	return
}

func activityTaskScheduled(evt *history.HistoryEvent) (hist HistoryEvent, err error) {
	hist = newHistoryEvent(evt)
	attr := evt.GetActivityTaskScheduledEventAttributes()
	hist.TaskName = &attr.ActivityId
	hist.TaskType = &attr.ActivityType.Name
	hist.Attributes, err = unmarshalPayload(attr.GetInput().GetPayloads(), constant.WorkflowInput)
	return
}

func activityTaskStarted(evt *history.HistoryEvent, histEvents []HistoryEvent) (hist HistoryEvent, err error) {
	hist = newHistoryEvent(evt)
	attr := evt.GetActivityTaskStartedEventAttributes()
	var scheduledEvt *HistoryEvent
	scheduledEvt, err = findEvent(histEvents, attr.GetScheduledEventId())
	if scheduledEvt != nil {
		hist.TaskName = scheduledEvt.TaskName
		hist.TaskType = scheduledEvt.TaskType
	}
	if attr.GetLastFailure() != nil {
		hist.Attributes = &map[string]any{constant.WorkflowLastFailure: attr.GetLastFailure().GetMessage()}
	}
	return
}

func activityTaskCompleted(evt *history.HistoryEvent, histEvents []HistoryEvent) (hist HistoryEvent, err error) {
	var err1, err2 error
	var scheduledEvt *HistoryEvent
	hist = newHistoryEvent(evt)
	attr := evt.GetActivityTaskCompletedEventAttributes()
	scheduledEvt, err1 = findEvent(histEvents, attr.GetScheduledEventId())
	if scheduledEvt != nil {
		hist.TaskName = scheduledEvt.TaskName
		hist.TaskType = scheduledEvt.TaskType
	}
	hist.Attributes, err2 = unmarshalPayload(attr.GetResult().GetPayloads(), constant.WorkflowResult)
	err = errors.Join(err1, err2)
	return
}

func activityTaskCanceledRequested(evt *history.HistoryEvent, histEvents []HistoryEvent) (hist HistoryEvent, err error) {
	var scheduledEvt *HistoryEvent
	hist = newHistoryEvent(evt)
	attr := evt.GetActivityTaskCancelRequestedEventAttributes()
	scheduledEvt, err = findEvent(histEvents, attr.GetScheduledEventId())
	if scheduledEvt != nil {
		hist.TaskName = scheduledEvt.TaskName
		hist.TaskType = scheduledEvt.TaskType
	}
	return
}

func activityTaskCanceled(evt *history.HistoryEvent, histEvents []HistoryEvent) (hist HistoryEvent, err error) {
	var scheduledEvt *HistoryEvent
	hist = newHistoryEvent(evt)
	attr := evt.GetActivityTaskCanceledEventAttributes()
	scheduledEvt, err = findEvent(histEvents, attr.GetScheduledEventId())
	if scheduledEvt != nil {
		hist.TaskName = scheduledEvt.TaskName
		hist.TaskType = scheduledEvt.TaskType
	}
	return
}

func activityTaskFailed(evt *history.HistoryEvent, histEvents []HistoryEvent) (hist HistoryEvent, err error) {
	var scheduledEvt *HistoryEvent
	hist = newHistoryEvent(evt)
	attr := evt.GetActivityTaskFailedEventAttributes()
	scheduledEvt, err = findEvent(histEvents, attr.GetScheduledEventId())
	if scheduledEvt != nil {
		hist.TaskName = scheduledEvt.TaskName
		hist.TaskType = scheduledEvt.TaskType
	}
	if attr.GetFailure() != nil {
		hist.Attributes = &map[string]any{constant.WorkflowFailure: attr.GetFailure().GetMessage()}
	}
	return
}

func childWorkflowInitiated(evt *history.HistoryEvent) (hist HistoryEvent, err error) {
	var err1, err2 error
	var name string
	hist = newHistoryEvent(evt)
	attr := evt.GetStartChildWorkflowExecutionInitiatedEventAttributes()
	hist.EventType = attr.WorkflowType.Name + constant.WorkflowTaskScheduled
	b := attr.GetMemo().Fields[constant.WorkflowName].GetData()
	if b != nil {
		err1 = json.Unmarshal(b, &name) //nolint:errcheck
	}
	hist.TaskName = &name
	hist.TaskType = &attr.WorkflowType.Name
	hist.Attributes, err2 = unmarshalPayload(attr.GetInput().GetPayloads(), constant.WorkflowInput)
	err = errors.Join(err1, err2)
	return
}

func childWorkflowStarted(evt *history.HistoryEvent, histEvents []HistoryEvent) (hist HistoryEvent, err error) {
	var scheduledEvt *HistoryEvent
	hist = newHistoryEvent(evt)
	attr := evt.GetChildWorkflowExecutionStartedEventAttributes()
	scheduledEvt, err = findEvent(histEvents, attr.GetInitiatedEventId())
	if scheduledEvt != nil {
		hist.TaskName = scheduledEvt.TaskName
	}
	hist.EventType = attr.WorkflowType.Name + constant.WorkflowTaskStarted
	hist.TaskType = &attr.WorkflowType.Name
	hist.Attributes = &map[string]any{
		constant.WorkflowId:    attr.GetWorkflowExecution().GetWorkflowId(),
		constant.WorkflowRunId: attr.GetWorkflowExecution().GetRunId(),
	}
	return
}

func childWorkflowCompleted(evt *history.HistoryEvent, histEvents []HistoryEvent) (hist HistoryEvent, err error) {
	var err1, err2 error
	var scheduledEvt *HistoryEvent
	hist = newHistoryEvent(evt)
	attr := evt.GetChildWorkflowExecutionCompletedEventAttributes()
	scheduledEvt, err1 = findEvent(histEvents, attr.GetInitiatedEventId())
	if scheduledEvt != nil {
		hist.TaskName = scheduledEvt.TaskName
	}
	hist.EventType = attr.WorkflowType.Name + constant.WorkflowTaskCompleted
	hist.TaskType = &attr.WorkflowType.Name
	hist.Attributes, err2 = unmarshalPayload(attr.GetResult().GetPayloads(), constant.WorkflowResult)
	err = errors.Join(err1, err2)
	return
}

func childWorkflowFailed(evt *history.HistoryEvent, histEvents []HistoryEvent) (hist HistoryEvent, err error) {
	var scheduledEvt *HistoryEvent
	hist = newHistoryEvent(evt)
	attr := evt.GetChildWorkflowExecutionFailedEventAttributes()
	scheduledEvt, err = findEvent(histEvents, attr.GetInitiatedEventId())
	if scheduledEvt != nil {
		hist.TaskName = scheduledEvt.TaskName
	}
	hist.EventType = attr.WorkflowType.Name + constant.WorkflowTaskFailed
	hist.TaskType = &attr.WorkflowType.Name
	if attr.GetFailure() != nil {
		hist.Attributes = &map[string]any{constant.WorkflowFailure: attr.GetFailure().GetMessage()}
	}
	return
}

func eventTypeMarkerRecorded(evt *history.HistoryEvent) (hist HistoryEvent, err error) {
	hist = newHistoryEvent(evt)
	hist.EventType = enums.EVENT_TYPE_ACTIVITY_TASK_COMPLETED.String()
	attr := evt.GetMarkerRecordedEventAttributes()
	var res map[string]string
	if v, ok := attr.GetDetails()[constant.WorkflowData]; ok {
		for _, p := range v.GetPayloads() {
			if p.Data != nil {
				if err = json.Unmarshal(p.Data, &res); err != nil {
					break
				}
			}
		}
	}
	if id, ok := res[constant.WorkflowSideEffectId]; ok {
		hist.TaskName = &id
	}
	if typ, ok := res[constant.WorkflowSideEffectType]; ok {
		hist.TaskType = &typ
	}
	return
}

func newHistoryEvent(evt *history.HistoryEvent) HistoryEvent {
	return HistoryEvent{
		EventId:   evt.EventId,
		EventType: evt.EventType.String(),
		Timestamp: evt.EventTime.AsTime().Format(time.RFC3339Nano),
	}
}

func findEvent(histEvents []HistoryEvent, id int64) (*HistoryEvent, error) {
	for _, v := range histEvents {
		if v.EventId == id {
			return &v, nil
		}
	}
	return nil, errors.New("event not found")
}

func unmarshalPayload(p []*common.Payload, key string) (*map[string]any, error) {
	m := make(map[string]any)
	var d []interface{}
	for _, v := range p {
		if v.Data != nil {
			var m interface{}
			if err := json.Unmarshal(v.Data, &m); err != nil {
				return nil, err
			}
			d = append(d, m)
		}
	}
	if d != nil {
		m[key] = d
	}
	return &m, nil
}
