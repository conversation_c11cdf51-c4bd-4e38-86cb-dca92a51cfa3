package util

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/go-chi/render"
	"github.com/google/uuid"
	aohhttp "github.com/mssfoobar/aoh-golib/http"
	aohlog "github.com/mssfoobar/aoh-golib/logger"
	"github.com/mssfoobar/app/wfe/internal/model"
)

func SetMiddleware(r *chi.Mux, keycloakUrl string) {
	r.Use(aohhttp.BearerAuth(keycloakUrl, aohlog.Get()))
	r.Use(aohhttp.RequestLogger(aohlog.Get()))
	r.Use(middleware.Recoverer)
	r.Use(render.SetContentType(render.ContentTypeJSON))
	r.Use(aohhttp.ResponseLogger(aohlog.Get()))
}

func RequestBodyError(err error) (int, error) {
	if errors.Is(err, io.EOF) {
		err = errors.New("empty request body")
	}
	var unmarshalErr *json.UnmarshalTypeError
	if errors.As(err, &unmarshalErr) {
		err = fmt.Errorf("invalid request body json field: %s", unmarshalErr.Field)
	}
	return http.StatusBadRequest, err
}

// CheckStoreError check if error is store error that can be handled by client, or return internal server error
func CheckStoreError(err error) (int, error) {
	var storeErr *model.StoreError
	if errors.As(err, &storeErr) {
		return http.StatusBadRequest, storeErr
	}
	return http.StatusInternalServerError, err
}

func AddChiURLParams(r *http.Request, params map[string]string) *http.Request {
	ctx := chi.NewRouteContext()
	for k, v := range params {
		ctx.URLParams.Add(k, v)
	}
	return r.WithContext(context.WithValue(r.Context(), chi.RouteCtxKey, ctx))
}

func GetResponseData(buffer *bytes.Buffer) *aohhttp.ResponsePayload {
	b, _ := io.ReadAll(buffer)
	var responseBody aohhttp.ResponsePayload
	_ = json.Unmarshal(b, &responseBody)
	return &responseBody
}

func StructToBytes(data any) []byte {
	b, _ := json.Marshal(data)
	return b
}

func StructToMap(data any) map[string]any {
	var m map[string]any
	rawExpected, _ := json.Marshal(data)
	_ = json.Unmarshal(rawExpected, &m)
	return m
}

func StructToSlice(data any) []any {
	var m []any
	rawExpected, _ := json.Marshal(data)
	_ = json.Unmarshal(rawExpected, &m)
	return m
}

func GetJwt(w http.ResponseWriter, r *http.Request) (*aohhttp.JwtClaim, error) {
	jwt, err := aohhttp.GetJWTClaim(r)
	if err != nil {
		_ = render.Render(w, r, aohhttp.ErrResponse(http.StatusUnauthorized, "", []error{err}))
		return nil, err
	}
	return jwt, nil
}

func ValidateUriParams(w http.ResponseWriter, r *http.Request, uri any) error {
	err := aohhttp.ValidateUriParams(r, uri)
	if err != nil {
		_ = render.Render(w, r, aohhttp.ErrResponse(http.StatusBadRequest, "", []error{err}))
	}
	return err
}

func GetQueryPagination(w http.ResponseWriter, r *http.Request) (*aohhttp.PageRequest, error) {
	page, err := aohhttp.GetQueryPagination(r)
	if err != nil {
		_ = render.Render(w, r, aohhttp.ErrResponse(http.StatusBadRequest, "", []error{err}))
	}
	return page, err
}

func ParseUUID(w http.ResponseWriter, r *http.Request, req string) (uuid.UUID, error) {
	id, err := uuid.Parse(req)
	if err != nil {
		_ = render.Render(w, r, aohhttp.ErrResponse(http.StatusBadRequest, "", []error{err}))
	}
	return id, err
}
