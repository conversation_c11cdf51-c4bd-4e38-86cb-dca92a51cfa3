package sample

import (
	"context"
	"encoding/json"
	"errors"

	aohlog "github.com/mssfoobar/aoh-golib/logger"
	"go.temporal.io/sdk/activity"
	tmpl "go.temporal.io/sdk/temporal"
)

const runWithInput = "Run %s with input %v"

type Activities struct{}

// SampleActivityString is an example of an activity that returns a string
func (a *Activities) SampleActivityString(
	ctx context.Context,
	inputString string,
) (string, error) {
	name := activity.GetInfo(ctx).ActivityType.Name
	aohlog.Infof(runWithInput, name, inputString)
	return inputString, nil
}

// SampleActivityNumber is an example of an activity that returns a number
func (a *Activities) SampleActivityNumber(
	ctx context.Context,
	inputNumber float64,
) (float64, error) {
	name := activity.GetInfo(ctx).ActivityType.Name
	aohlog.Infof(runWithInput, name, inputNumber)
	return inputNumber, nil
}

// SampleActivityJson is an example of an activity that returns a json
func (a *Activities) SampleActivityJson(
	ctx context.Context,
	jsonString string,
) (any, error) {
	name := activity.GetInfo(ctx).ActivityType.Name
	aohlog.Infof(runWithInput, name, jsonString)
	var jsonMap map[string]interface{}
	_ = json.Unmarshal([]byte(jsonString), &jsonMap) //nolint:errcheck
	return jsonMap, nil
}

// SampleError is an example of an activity that returns an error
func (a *Activities) SampleError(ctx context.Context) error {
	return tmpl.NewNonRetryableApplicationError(
		"non retrable error",
		"errorType",
		errors.New("non retrable error"),
	)
}
