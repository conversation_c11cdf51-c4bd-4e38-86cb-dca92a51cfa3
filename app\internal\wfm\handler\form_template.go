package handler

import (
	"encoding/json"
	"errors"
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
	"github.com/google/uuid"
	aohhttp "github.com/mssfoobar/aoh-golib/http"
	aohlog "github.com/mssfoobar/aoh-golib/logger"
	"github.com/mssfoobar/app/wfe/internal/model"
	"github.com/mssfoobar/app/wfe/internal/wfm/handler/util"
	"go.uber.org/zap"
)

const (
	FormSubmit string = "submit"
	FormAction string = "action"
	FormKey    string = "key"
)

type FormTemplate struct {
	store model.FormTemplateStore
}

type (
	formTemplateUriParam struct {
		TemplateId string `json:"template_id" validate:"required,uuid"`
	}

	formTemplateReqBody struct {
		Name     string          `json:"name"`
		FormJson json.RawMessage `json:"form_json"`
		OccLock  int             `json:"occ_lock,omitempty"`
	}
)

func (r *formTemplateReqBody) Bind(_ *http.Request) error {
	if r.Name == "" || r.FormJson == nil {
		return errors.New("missing required fields")
	}
	return nil
}

func RegisterFormTemplate(factory *model.Factory, keycloakUrl string) http.Handler {
	r := chi.NewRouter()
	util.SetMiddleware(r, keycloakUrl)

	f := FormTemplate{
		store: factory.NewFormTemplateStore(),
	}

	r.Route("/", func(r chi.Router) {
		r.Get("/", f.List)
		r.Put("/save", f.Save)
		r.Get("/{template_id}", f.Get)
		r.Delete("/{template_id}", f.Delete)
	})

	return r
}

// Save is a http handler to create new form template
func (f *FormTemplate) Save(w http.ResponseWriter, r *http.Request) {
	var req formTemplateReqBody
	if err := render.Bind(r, &req); err != nil {
		code, err := util.RequestBodyError(err)
		_ = render.Render(w, r, aohhttp.ErrResponse(code, "", []error{err}))
		return
	}

	jwt, err := util.GetJwt(w, r)
	if err != nil {
		return
	}

	keys, err := getFormComponentKeys(req.FormJson)
	if err != nil {
		_ = render.Render(w, r, aohhttp.ErrResponse(http.StatusBadRequest, "", []error{err}))
		return
	}

	resp, err := f.store.SaveFormTemplate(r.Context(), &model.FormTemplateRequest{
		Name:          req.Name,
		FormJson:      req.FormJson,
		ComponentKeys: keys,
		Requester:     jwt.Id,
		TenantId:      jwt.ActiveTenant.Id,
		OccLock:       req.OccLock,
	})
	if err != nil {
		aohlog.Error("[FormTemplate] save form template failed", zap.Error(err))
		code, err := util.CheckStoreError(err)
		_ = render.Render(w, r, aohhttp.ErrResponse(code, "", []error{err}))
		return
	}

	aohlog.Info("[FormTemplate] saved form template successfully", zap.String("template_id", resp.Id.String()))
	_ = render.Render(w, r, aohhttp.Response(http.StatusOK, "", resp))
}

// List is a http handler to return paginated form template list
func (f *FormTemplate) List(w http.ResponseWriter, r *http.Request) {
	page, err := util.GetQueryPagination(w, r)
	if err != nil {
		return
	}

	jwt, err := util.GetJwt(w, r)
	if err != nil {
		return
	}

	resp, err := f.store.ListFormTemplate(r.Context(), &model.ListFormTemplateRequest{
		Page:     *page,
		TenantId: jwt.ActiveTenant.Id,
	})
	if err != nil {
		aohlog.Error("[FormTemplate] list form templates failed",
			zap.String("tenant_id", jwt.ActiveTenant.Id),
			zap.Error(err))
		code, err := util.CheckStoreError(err)
		_ = render.Render(w, r, aohhttp.ErrResponse(code, "", []error{err}))
		return
	}

	respData := append(make([]model.FormTemplateResponse, 0), resp.FormTemplates...)
	respPage := aohhttp.PageResponse{
		Number:       page.Number,
		Size:         page.Size,
		TotalRecords: resp.TotalCount,
		Count:        len(resp.FormTemplates),
		Sort:         []string{page.Sorts.String()},
	}

	aohlog.Info("[FormTemplate] list form templates successfully", zap.Int("count", len(respData)))
	_ = render.Render(w, r, aohhttp.PaginationResponse(http.StatusOK, "", respPage, respData))
}

// Get is a http handler to return form template
func (f *FormTemplate) Get(w http.ResponseWriter, r *http.Request) {
	var uri formTemplateUriParam
	if err := util.ValidateUriParams(w, r, &uri); err != nil {
		return
	}

	jwt, err := util.GetJwt(w, r)
	if err != nil {
		return
	}

	resp, err := f.store.GetFormTemplate(r.Context(), &model.GetFormTemplateRequest{
		Id:       uuid.MustParse(uri.TemplateId),
		TenantId: jwt.ActiveTenant.Id,
	})
	if err != nil {
		aohlog.Error("[FormTemplate] get form template failed",
			zap.String("template_id", uri.TemplateId),
			zap.String("tenant_id", jwt.ActiveTenant.Id),
			zap.Error(err))
		code, err := util.CheckStoreError(err)
		_ = render.Render(w, r, aohhttp.ErrResponse(code, "", []error{err}))
		return
	}

	aohlog.Info("[FormTemplate] get form template successfully", zap.String("template_id", uri.TemplateId))
	_ = render.Render(w, r, aohhttp.Response(http.StatusOK, "", resp))
}

// Delete is a http handler to delete form template
func (f *FormTemplate) Delete(w http.ResponseWriter, r *http.Request) {
	var uri formTemplateUriParam
	if err := util.ValidateUriParams(w, r, &uri); err != nil {
		return
	}

	jwt, err := util.GetJwt(w, r)
	if err != nil {
		return
	}

	err = f.store.DeleteFormTemplate(r.Context(), &model.DeleteFormTemplateRequest{
		Id:       uuid.MustParse(uri.TemplateId),
		TenantId: jwt.ActiveTenant.Id,
	})
	if err != nil {
		aohlog.Error("[FormTemplate] delete form template failed",
			zap.String("template_id", uri.TemplateId),
			zap.String("tenant_id", jwt.ActiveTenant.Id),
			zap.Error(err))
		code, err := util.CheckStoreError(err)
		_ = render.Render(w, r, aohhttp.ErrResponse(code, "", []error{err}))
		return
	}

	aohlog.Info("[FormTemplate] deleted form template successfully", zap.String("template_id", uri.TemplateId))
	_ = render.Render(w, r, aohhttp.Response(http.StatusOK, "", nil))
}

func getFormComponentKeys(form []byte) ([]byte, error) {
	var formMap map[string]interface{}
	if err := json.Unmarshal(form, &formMap); err != nil {
		return nil, err
	}

	if !checkKVInMap(formMap, FormAction, FormSubmit) {
		return nil, errors.New("invalid form. no submit button exist")
	}
	var keys []any
	getValFromMapByKey(formMap, FormKey, &keys)

	jsonKey, err := json.Marshal(keys)
	if err != nil {
		return nil, err
	}

	return jsonKey, nil
}

// getValFromMapByKey is a helper function that recursively extract
// all the values of same key in nested json
func getValFromMapByKey(data map[string]any, key string, values *[]any) {
	for k, v := range data {
		if k == key {
			*values = append(*values, v)
			continue
		}
		switch v.(type) {
		case []any:
			for _, v := range v.([]any) {
				switch v := v.(type) {
				case map[string]any:
					getValFromMapByKey(v, key, values)
				}
			}
		}
	}
}

// checkKVInMap is a helper function that recursively check
// key value pair exist in nested map
func checkKVInMap(data map[string]any, key string, value any) (isExist bool) {
	for k, v := range data {
		if k == key && v == value {
			isExist = true
			return
		}
		switch v.(type) {
		case []any:
			for _, v := range v.([]any) {
				switch v := v.(type) {
				case map[string]any:
					isExist = checkKVInMap(v, key, value)
				}
			}
		}
	}
	return
}
