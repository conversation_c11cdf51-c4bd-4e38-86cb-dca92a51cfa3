/**
 * The Activity interface provides methods for setting and getting parameters and UI state.
 * Use this in customized UI component to interact with an activity in the workflow.
 *
 * @warning Do not implement this interface directly. It is intended to be implemented by internal classes only.
 */
export interface Activity {
	/**
	 * Set value of activity parameter by name
	 * @param {string} name - parameter name
	 * @param {unknown} value - parameter value of arbitrary type
	 */
	setParameter(name: string, value: unknown): void;
	/**
	 * Get value of activity parameter by name
	 * @param {string} name - parameter name
	 * @return {unknown} - parameter value of arbitrary type
	 */
	getParameter(name: string): unknown;
	/**
	 * Clear value of activity parameter by name
	 * @param {string} name - parameter name
	 */
	clearParameter(name: string): void;
	/**
	 * Set value of activity UI state by key.
	 * Use this to persist the UI state by defined key.
	 * @method
	 * @param {string} key - UI state key
	 * @param {unknown} value - UI state value of arbitrary type
	 */
	setUiState(key: string, value: unknown): void;
	/**
	 * Get value of activity UI state by key.
	 * Use this to get the UI state by defined key.
	 * @method
	 * @param {string} key - UI state key
	 * @return {unknown} - UI state value of arbitrary type
	 */
	getUiState(key: string): unknown;
}
