<script lang="ts" module>
	/**
	 * This component defines the navigation bar using shadcn.
	 * It serves as a reference for implementing custom navigation bars.
	 *
	 * This component utilizes the following shadcn components:
	 *  - Breadcrumbs
	 *  - Dropdown Menu
	 *  - Avatar
	 */
	import * as Breadcrumb from "$lib/aoh/core/components/ui/breadcrumb/index";
	import * as DropdownMenu from "$lib/aoh/core/components/ui/dropdown-menu/index";
	import * as Avatar from "$lib/aoh/core/components/ui/avatar";
	import Moon from "lucide-svelte/icons/moon";
	import Sun from "lucide-svelte/icons/sun";
	import Settings from "lucide-svelte/icons/settings";
	import Logout from "lucide-svelte/icons/log-out";
	import Grip from "lucide-svelte/icons/grip";
	import type { Auth, AuthClaims } from "$lib/aoh/core/provider/auth/auth";
	import { isDarkMode, setDarkMode } from "$lib/aoh/core/provider/theme/ThemeProvider/index.svelte";
	import { breadcrumbStore } from "$lib/aoh/core/stores/breadcrumb/breadcrumb";
	import dayjs from "dayjs";
	import { onMount } from "svelte";
	import { goto } from "$app/navigation";
	import { titleCase } from "change-case-all";

	export type ModuleOption = {
		name: string;
		href: string;
	};

	export type NavbarProps = {
		user: Auth;
		modules?: ModuleOption[];
	};
</script>

<script lang="ts">
	const iconClass: string = "mr-2";
	const navbarIconClass: string = "text-icon hover:bg-bg-elevated p-2 rounded-sm";
	const LOGOUT_ENDPOINT: string = "/aoh/api/auth/logout";
	const TIME_FORMAT: string = "ddd DD/MM/YYYY HH:mm:ss";

	let { user, modules }: { user: AuthClaims; modules?: ArrayLike<ModuleOption> } = $props();
	let currentTime: string = $state(dayjs().format(TIME_FORMAT));

	async function onLogout(): Promise<void> {
		window.location.href = LOGOUT_ENDPOINT;
	}

	onMount(() => {
		const interval = setInterval(() => {
			currentTime = dayjs().format(TIME_FORMAT);
		}, 100);

		return () => clearInterval(interval);
	});
</script>

<nav class="h-14 items-center px-[30px] w-full border-border border-y flex">
	<Breadcrumb.Root>
		<Breadcrumb.List>
			{#each $breadcrumbStore as item, i}
				<Breadcrumb.Item
					class={i === $breadcrumbStore.length - 1 && item.href !== undefined
						? "pointer-events-none text-text"
						: ""}
				>
					<Breadcrumb.Link href={item.href}>{item.name}</Breadcrumb.Link>
				</Breadcrumb.Item>
				{#if $breadcrumbStore.length > 1 && i < $breadcrumbStore.length - 1}
					<Breadcrumb.Separator />
				{/if}
			{/each}
		</Breadcrumb.List>
	</Breadcrumb.Root>

	<div class="ml-auto flex items-center gap-4">
		<time class="bg-secondary w-44 text-center py-1 px-2 text-xs rounded text-text">{currentTime}</time>

		<!-- Module selector -->
		<DropdownMenu.Root>
			<DropdownMenu.Trigger class={navbarIconClass}>
				<Grip />
			</DropdownMenu.Trigger>
			<DropdownMenu.Content align="end">
				<DropdownMenu.Label class="text-text">Modules</DropdownMenu.Label>
				<DropdownMenu.Separator />
				<DropdownMenu.Group>
					{#if modules}
						{#each modules as module}
							<DropdownMenu.Item class="font-normal" onclick={() => goto(module.href)}>
								{module.name}
							</DropdownMenu.Item>
						{/each}
					{/if}
				</DropdownMenu.Group>
			</DropdownMenu.Content>
		</DropdownMenu.Root>

		<!-- User menu-->
		<DropdownMenu.Root>
			<DropdownMenu.Trigger
				><Avatar.Root>
					<Avatar.Image src="https://github.com/shadcn.png" alt="@shadcn" />
					<Avatar.Fallback>CN</Avatar.Fallback>
				</Avatar.Root></DropdownMenu.Trigger
			>

			<DropdownMenu.Content class="w-56" align="end">
				<DropdownMenu.Group>
					<DropdownMenu.Item class="font-normal pointer-events-none">
						<div class="flex flex-col gap-3">
							<p class="text-sm font-medium leading-none">{user.name}</p>
							{#if user.active_tenant && user.active_tenant.tenant_name}
								<p class="text-muted-foreground text-xs leading-none">
									{titleCase(user.active_tenant.tenant_name)}
								</p>
							{/if}
						</div>
					</DropdownMenu.Item>
					<DropdownMenu.Separator />
					<DropdownMenu.Item onclick={() => setDarkMode(!isDarkMode())}>
						{#if isDarkMode() === false}
							<Moon class={iconClass} />
							Dark Mode
						{:else}
							<Sun class={iconClass} />
							Light Mode
						{/if}
					</DropdownMenu.Item>
					<DropdownMenu.Item disabled={true}>
						<Settings class={iconClass} />
						Settings</DropdownMenu.Item
					>
				</DropdownMenu.Group>
				<DropdownMenu.Separator />
				<DropdownMenu.Item onclick={onLogout}>
					<Logout class={iconClass} />
					Log out</DropdownMenu.Item
				>
			</DropdownMenu.Content>
		</DropdownMenu.Root>
	</div>
</nav>
