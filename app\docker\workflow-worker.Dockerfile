FROM golang:1.23.4 AS builder

WORKDIR /app

COPY go.mod go.mod
COPY go.sum go.sum

COPY internal/ internal/
COPY schema/ schema/
COPY cmd/ cmd/

# Github Personal Access Token to access private go modules
ARG GITHUB_PAT

RUN git --version && \
    git config --global url.https://"$GITHUB_PAT"@github.com/.insteadOf https://github.com/ && \
    CGO_ENABLED=0 GOOS=linux GOARCH=amd64 GO111MODULE=on go build -a -o /build/workflow-worker ./cmd/workflow-worker

FROM alpine:3.21.0

ARG REVISION

LABEL "org.opencontainers.image.source"="https://github.com/mssfoobar/wfe"
LABEL "org.opencontainers.image.title"="Workflow Worker"
LABEL "org.opencontainers.image.description"="Out-of-the-box activities backend service for eSOP system"
LABEL "org.opencontainers.image.authors"="https://github.com/nyan979"
LABEL "org.opencontainers.image.revision"="${REVISION}"

RUN addgroup -g 1000 wfw && \
    adduser -u 1000 -G wfw -D wfw && \
    mkdir /app && \
    chown -R wfw:wfw /app
USER wfw

WORKDIR /app

COPY --from=builder /build/workflow-worker .

CMD ["/app/workflow-worker"]
