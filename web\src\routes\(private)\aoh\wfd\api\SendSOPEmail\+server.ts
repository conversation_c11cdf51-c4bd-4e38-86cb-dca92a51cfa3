import { json, error } from "@sveltejs/kit";
import type { RequestHandler } from "@sveltejs/kit";
import { StatusCodes } from "http-status-codes";
import { env } from "$env/dynamic/private";

export const GET: RequestHandler = async ({ locals }) => {
    if (!locals.authResult.success) {
        return new Response(
            JSON.stringify({
                message: "Unauthorized",
            }),
            {
                status: StatusCodes.UNAUTHORIZED,
            }
        );
    }

    const tenant_id =  locals?.authResult?.claims?.active_tenant?.tenant_id || "";

    const bearerAuthorizationString = `Bearer ${locals.authResult.access_token}`;

    const headers = {
        "Content-Type": "application/json",
        Authorization: bearerAuthorizationString,
    };

    const user_promise = await fetch(env.ACTIVITY_AAS_URL + `/admin/tenants/${tenant_id}/memberships`, {
        method: "GET",
        headers,
    });

    const role_promise = await fetch(env.ACTIVITY_AAS_URL + `/admin/tenants/${tenant_id}/roles`, {
        method: "GET",
        headers,
    });

    const data_agg_promise = await fetch(env.DATA_AGG_URL + `/sys-datasource/getdatasourcesbymaincategory?tenantid=${tenant_id}&maincategory=Aggregated Data Source,SOP Parameter&include_inactive=true`, {
        method: "GET",
        headers,
    });


    const [user_response, role_response] = await Promise.all([user_promise, role_promise, data_agg_promise]);

    if (!user_response.ok || !role_response.ok || !data_agg_promise.ok) {
        return error(StatusCodes.INTERNAL_SERVER_ERROR, "Failed to fetch IAMS data");
    }

    const data = {
        users: await user_response.json(),
        roles: await role_response.json(),
        datasources: await data_agg_promise.json(),
    };

    return json(data);
};
