package handler

import (
	"bytes"
	"errors"
	"math/rand/v2"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/mssfoobar/app/wfe/internal/model"
	mm "github.com/mssfoobar/app/wfe/internal/model/mock"
	"github.com/mssfoobar/app/wfe/internal/wfm/handler/util"
	"github.com/mssfoobar/app/wfe/internal/wfm/temporal"
	tm "github.com/mssfoobar/app/wfe/internal/wfm/temporal/mock"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

const testJwtToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiYWN0aXZlX3RlbmFudCI6eyJ0ZW5hbnRfaWQiOiIxMjM0NSIsInRlbmFudF9uYW1lIjoiYW9oIn19.x938YEuEMIUP7drj7x1tg7YaInVhHTt9pZMOahPP4_I"

func TestRegisterExecute(t *testing.T) {
	factory := model.Factory{}
	tmpl := tm.MockService{}
	handler := RegisterExecute(&factory, &tmpl, "http://localhost:8080")

	req, err := http.NewRequest(http.MethodPost, "/", nil)
	if err != nil {
		t.Fatal(err)
	}

	w := httptest.NewRecorder()
	handler.ServeHTTP(w, req)

	require.Equal(t, http.StatusUnauthorized, w.Code)
}

func TestWorkflow_Start(t *testing.T) {
	ctrl := gomock.NewController(t)
	mWorkflowStore := mm.NewMockWorkflowTemplateStore(ctrl)
	mTemporal := tm.NewMockService(ctrl)

	wf := Workflow{
		workflowStore: mWorkflowStore,
		temporal:      mTemporal,
	}

	t.Run("expect 200", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(
			http.MethodPost,
			"/",
			bytes.NewReader(util.StructToBytes(startReqBody{
				TemplateId: uuid.NewString(),
			})),
		)
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mWorkflowStore.EXPECT().
			GetWorkflowTemplate(gomock.Any(), gomock.Any()).
			Return(&model.WorkflowTemplateResponse{}, nil)
		mTemporal.EXPECT().
			ExecuteWorkflow(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&temporal.WorkflowRun{WorkflowId: "workflowId"}, nil)
		wf.Start(w, r)
		require.Equal(t, http.StatusOK, w.Code)
		require.Equal(t, util.StructToMap(temporal.WorkflowRun{
			WorkflowId: "workflowId",
		}), util.GetResponseData(w.Body).Data)
	})

	t.Run("invalid request body, expect 400", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(
			http.MethodPost,
			"/",
			bytes.NewReader(util.StructToBytes(startReqBody{
				TemplateId: "123",
			})),
		)
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		wf.Start(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid or missing jwt token, expect 401", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(
			http.MethodPost,
			"/",
			bytes.NewReader(util.StructToBytes(startReqBody{
				TemplateId: uuid.NewString(),
			})),
		)
		r.Header.Add("content-type", "application/json")
		wf.Start(w, r)
		require.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("database error, expect 500", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(
			http.MethodPost,
			"/",
			bytes.NewReader(util.StructToBytes(startReqBody{
				TemplateId: uuid.NewString(),
			})),
		)
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mWorkflowStore.EXPECT().GetWorkflowTemplate(gomock.Any(), gomock.Any()).Return(nil, errors.New("database error"))
		wf.Start(w, r)
		require.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

func TestWorkflow_Signal(t *testing.T) {
	ctrl := gomock.NewController(t)
	mTemporal := tm.NewMockService(ctrl)

	wf := Workflow{
		temporal: mTemporal,
	}

	t.Run("expect 200", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(
				http.MethodPost,
				"/{workflow_id}/activity_name/{activity_name}",
				bytes.NewReader(util.StructToBytes(signalReqBody{
					Data: "data",
				})),
			),
			map[string]string{"workflow_id": "workflowId", "activity_name": "activityName"})
		r.Header.Add("content-type", "application/json")
		mTemporal.EXPECT().SignalWorkflow(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		wf.Signal(w, r)
		require.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("invalid request body, expect 400", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(http.MethodPost, "/{workflow_id}/activity_name/{activity_name}", nil),
			map[string]string{"workflow_id": "workflowId", "activity_name": "activityName"})
		r.Header.Add("content-type", "application/json")
		wf.Signal(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("temporal server error, expect 500", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(
				http.MethodPost,
				"/{workflow_id}/activity_name/{activity_name}",
				bytes.NewReader(util.StructToBytes(signalReqBody{
					Data: "data",
				})),
			),
			map[string]string{"workflow_id": "workflowId", "activity_name": "activityName"})
		r.Header.Add("content-type", "application/json")
		mTemporal.EXPECT().SignalWorkflow(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("temporal error"))
		wf.Signal(w, r)
		require.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

func TestWorkflow_Terminate(t *testing.T) {
	ctrl := gomock.NewController(t)
	mTemporal := tm.NewMockService(ctrl)

	wf := Workflow{
		temporal: mTemporal,
	}

	t.Run("expect 200", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(
				http.MethodDelete,
				"/{workflow_id}",
				bytes.NewReader(util.StructToBytes(terminateReqBody{Reason: "terminated by user"})),
			),
			map[string]string{"workflow_id": "workflowId"},
		)
		r.Header.Add("content-type", "application/json")
		mTemporal.EXPECT().TerminateWorkflow(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		wf.Terminate(w, r)
		require.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("temporal server error, expect 500", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(
				http.MethodDelete,
				"/{workflow_id}",
				bytes.NewReader(util.StructToBytes(terminateReqBody{Reason: "terminated by user"})),
			),
			map[string]string{"workflow_id": "workflowId"},
		)
		r.Header.Add("content-type", "application/json")
		mTemporal.EXPECT().
			TerminateWorkflow(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("temporal error"))
		wf.Terminate(w, r)
		require.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

func TestWorkflow_GetWorkflowHistory(t *testing.T) {
	ctrl := gomock.NewController(t)
	mTemporal := tm.NewMockService(ctrl)
	wf := Workflow{
		temporal: mTemporal,
	}

	taskName := "activityName"
	taskType := "activityType"
	attr := map[string]any{"key": "value"}
	event := temporal.HistoryEvent{
		EventId:    rand.Int64(),
		EventType:  "eventName",
		Timestamp:  time.Now().Format(time.RFC3339),
		TaskName:   &taskName,
		TaskType:   &taskType,
		Attributes: &attr,
	}

	t.Run("expect 200", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(http.MethodGet, "/{workflow_id}", nil),
			map[string]string{"workflow_id": "workflowId"})
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mTemporal.
			EXPECT().
			GetWorkflowHistory(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]temporal.HistoryEvent{event}, 0, nil)
		wf.GetWorkflowHistory(w, r)
		require.Equal(t, http.StatusOK, w.Code)
		require.Equal(t, util.StructToSlice([]any{event}), util.GetResponseData(w.Body).Data)
	})

	t.Run("invalid query params, expect 400", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(http.MethodGet, "/{workflow_id}/?page=a&size=b", nil),
			map[string]string{"workflow_id": "workflowId"})
		r.Header.Add("Content-Type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		wf.GetWorkflowHistory(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("database error, expect 500", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := util.AddChiURLParams(
			httptest.NewRequest(http.MethodGet, "/{workflow_id}", nil),
			map[string]string{"workflow_id": "workflowId"})
		r.Header.Add("content-type", "application/json")
		r.Header.Add("Authorization", "Bearer "+testJwtToken)
		mTemporal.
			EXPECT().
			GetWorkflowHistory(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, 0, errors.New("temporal internal error"))
		wf.GetWorkflowHistory(w, r)
		require.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

func TestWorkflow_ListRunningWorkflow(t *testing.T) {
	ctrl := gomock.NewController(t)
	mTemporal := tm.NewMockService(ctrl)
	wf := Workflow{
		temporal: mTemporal,
	}

	testWorkflowExecutions := []temporal.WorkflowExecution{
		{
			WorkflowId: "workflowId",
			StartTime:  "2022-01-01T00:00:00Z",
		},
	}

	t.Run("expect 200", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(http.MethodGet, "/", nil)
		r.Header.Add("content-type", "application/json")
		mTemporal.EXPECT().ListOpenWorkflow(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return(testWorkflowExecutions, 1, nil)
		wf.ListRunningWorkflow(w, r)
		require.Equal(t, http.StatusOK, w.Code)
		require.Equal(t, util.StructToSlice(testWorkflowExecutions), util.GetResponseData(w.Body).Data)
	})

	t.Run("invalid query params, expect 400", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(http.MethodGet, "/?page=a&size=b", nil)
		r.Header.Add("Content-Type", "application/json")
		wf.ListRunningWorkflow(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("temporal error, expect 500", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest(http.MethodGet, "/", nil)
		r.Header.Add("content-type", "application/json")
		mTemporal.EXPECT().ListOpenWorkflow(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return(nil, 0, errors.New("temporal error"))
		wf.ListRunningWorkflow(w, r)
		require.Equal(t, http.StatusInternalServerError, w.Code)
	})
}
