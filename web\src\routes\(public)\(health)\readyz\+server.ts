import { StatusCodes } from "http-status-codes";

import type { <PERSON>quest<PERSON><PERSON><PERSON> } from "./$types";

/**
 * Implement checks here if you need to ensure some dependencies are running before considering the
 * web server to be ready to serve requests.
 */
const isServerReady = () => {
	return true;
};

/** For Kubernetes Readiness Probe - Returns Status 200/Ok when ready, 423/Locked when not */
export const GET: RequestHandler = async () => {
	let message: string;
	let status: StatusCodes;

	// We're ready once we have received valid endpoints from the database
	if (isServerReady()) {
		message = "Server ready.";
		status = StatusCodes.OK;
	} else {
		message = "Server not ready.";
		status = StatusCodes.LOCKED;
	}

	const responseBody: HTTPResponseBody<null> = {
		message,
		sent_at: new Date().toISOString(),
	};

	return new Response(JSON.stringify(responseBody), {
		status,
	});
};
