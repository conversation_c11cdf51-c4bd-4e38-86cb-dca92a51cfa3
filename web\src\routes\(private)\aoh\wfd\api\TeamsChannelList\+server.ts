import { json, error } from "@sveltejs/kit";
import type { RequestHandler } from "@sveltejs/kit";
import { StatusCodes } from "http-status-codes";
import { env } from "$env/dynamic/private";

// /v1/getTeamsChannelList

export const GET: RequestHandler = async ({ locals }) => {
	if (!locals.authResult.success) {
		return new Response(JSON.stringify({ message: "Unauthorized" }), { status: StatusCodes.UNAUTHORIZED });
	}

	const bearer = `Bearer ${locals.authResult.access_token}`;

	const headers = { "Content-Type": "application/json", Authorization: bearer };
	const options = { method: "GET", headers };

	const listResp = await fetch(env.TEAMS_URL + `/v1/getTeamsChannelList`, options);

	if (!listResp.ok) {
		return error(StatusCodes.INTERNAL_SERVER_ERROR, "Failed to fetch Teams Channel List");
	}

	const data = await listResp.json();

	return json(data);
};
