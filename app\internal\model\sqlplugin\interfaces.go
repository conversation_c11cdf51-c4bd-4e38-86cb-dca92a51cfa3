package sqlplugin

import (
	"context"
	"database/sql"

	"github.com/jmoiron/sqlx"
	"github.com/mssfoobar/app/wfe/internal/config"
)

type (
	// Plugin defines the interface for any SQL database that needs to implement
	Plugin interface {
		CreateDB(cfg *config.SQL) (DB, error)
		CreateModuleInfoDB(cfg *config.SQL) (ModuleInfoDB, error)
	}

	// TableCRUD defines the API for interacting with the database tables
	TableCRUD interface {
		ServiceActivity
		ServiceEvent
		FormTemplate
		WorkflowTemplate
	}

	ModuleInfoCRUD interface {
		CreateModuleInfoTable() error
		ReadSchemaVersion() (string, error)
		UpdateSchemaVersion(version string) error
		ListTables() ([]string, error)
		DropTable(table string) error
		DropAllTables() error
		Exec(stmt string, args ...interface{}) error
	}

	// Tx defines the API for a SQL transaction
	Tx interface {
		TableCRUD
		Commit() error
		Rollback() error
	}

	// DB defines the API for regular SQL operations of a Temporal server
	DB interface {
		TableCRUD

		BeginTx(ctx context.Context) (Tx, error)
		PluginName() string
		DbName() string
		SchemaName() string
		IsForeignKeyViolationError(err error) bool
		IsDupEntryError(err error) bool
		IsColumnNotExistError(err error) bool
		IsExceptionError(err error) bool
		Close() error
	}

	ModuleInfoDB interface {
		ModuleInfoCRUD
		PluginName() string
		ExpectedVersion() string
		VerifyVersion() error
		Close() error
	}

	// Conn defines the API for a single database connection
	Conn interface {
		Rebind(query string) string
		ExecContext(ctx context.Context, query string, args ...interface{}) (sql.Result, error)
		NamedExecContext(ctx context.Context, query string, arg interface{}) (sql.Result, error)
		GetContext(ctx context.Context, dest interface{}, query string, args ...interface{}) error
		SelectContext(ctx context.Context, dest interface{}, query string, args ...interface{}) error
		PrepareNamedContext(ctx context.Context, query string) (*sqlx.NamedStmt, error)
	}
)
