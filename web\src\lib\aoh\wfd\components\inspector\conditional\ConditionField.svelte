<script lang="ts" module>
	import { z } from "zod";
	import type { ui, dia } from "rappid/rappid";
	import { ActivityBase } from "$lib/aoh/wfd/bpmn/shapes";

	interface Props {
		inspector: ui.Inspector;
		path: string;
	}

	export const validate_condition_schema = z.object({
		expression: z.string(),
		variables: z.record(z.unknown()),
	});

	export type ValidateConditionFromSchema = z.infer<typeof validate_condition_schema>;

	export interface ValidateResponse {
		data?: ValidateData[];
		errors?: ValidateError[];
		message?: string;
	}

	interface ValidateData {
		Line: number;
		Column: number;
		Message: string;
		Snippet: string;
	}

	interface ValidateError {
		message: string;
	}

	export const traverseBfs = function (
		el: dia.Element,
		cell: dia.Cell,
		completionSource: string[],
		variables: Record<string, unknown>
	): void {
		cell.graph.bfs(
			el,
			(el, distance) => {
				if ((el instanceof ActivityBase && distance) || el instanceof Event) {
					const result = el.attr("data/result") as string;
					completionSource.push(result);
					variables[result] = el.attr("data/taskResult");
				}
				if (el instanceof Event) {
					const parent = el.getParentCell();
					if (parent) {
						traverseBfs(parent as dia.Element, cell, completionSource, variables);
					}
				}
				return true;
			},
			{ inbound: true, deep: false }
		);
	};

	export const getEndIndex = (inputString: string, startIndex: number) => {
		if (startIndex < 0 || startIndex >= inputString.length) {
			return -1;
		}

		let endIndex = startIndex;
		while (endIndex < inputString.length && inputString[endIndex] !== " ") {
			endIndex++;
		}

		return endIndex;
	};

	export const removeDuplicates = (array: cmlint.Diagnostic[], key: string) => {
		return array.filter((item, index, self) => {
			return (
				index ===
				self.findIndex((i) => i[key as keyof cmlint.Diagnostic] === item[key as keyof cmlint.Diagnostic])
			);
		});
	};
</script>

<script lang="ts">
	import { onMount } from "svelte";
	import CodeMirror from "svelte-codemirror-editor";
	import * as cmlint from "@codemirror/lint";
	import {
		EditorView,
		highlightActiveLine,
		highlightActiveLineGutter,
		highlightSpecialChars,
		keymap,
	} from "@codemirror/view";
	import {
		autocompletion,
		closeBrackets,
		type CompletionContext,
		completionKeymap,
		type CompletionResult,
	} from "@codemirror/autocomplete";
	import { defaultKeymap } from "@codemirror/commands";
	import { bracketMatching, defaultHighlightStyle, syntaxHighlighting, syntaxTree } from "@codemirror/language";
	import { Label } from "$lib/aoh/wfd/components/ui/label";
	// import { logger } from "@mssfoobar/logger/Logger";
	import { oneDark } from "$lib/aoh/wfd/codemirror/theme";
	import { expression } from "$lib/aoh/wfd/codemirror";
	import { type Flow } from "$lib/aoh/wfd/bpmn/shapes";
	import { superForm } from "sveltekit-superforms";
	import { zodClient } from "sveltekit-superforms/adapters";

	let { inspector, path }: Props = $props();
	const cell = inspector.options.cell as Flow;

	let value: string = $state(cell.prop(path) || cell.prop(path + "Tmp") || "");
	let validateStatus: ValidateResponse | null = $state(cell.prop(path + "Validate") || {});
	let completionSource: string[] = [];
	let variables: Record<string, unknown> = {};
	let codeMirrorContainer: HTMLDivElement;

	const super_validate_condition_form = superForm<ValidateConditionFromSchema>(
		{
			expression: "",
			variables: {},
		},
		{
			dataType: "json",
			validators: zodClient(validate_condition_schema),
			onSubmit: () => {
				$validate_condition_form = {
					expression: value,
					variables: variables,
				};
			},
			onResult: ({ result }) => {
				if (result.type === "success" && result.data) {
					validateStatus = result.data.validate;
					cell.removeProp(path + "Validate");
					cell.prop(path + "Validate", validateStatus);
					cell.prop(path + "Tmp", value);

					if (!validateStatus?.errors) {
						cell.prop(path, value);
					} else {
						cell.removeProp(path);
					}
				}
			},
		}
	);

	const { form: validate_condition_form, enhance: validate_condition_enhance } = super_validate_condition_form;

	onMount(() => {
		if (!cell.isLink()) {
			return;
		}

		const el = cell.getSourceElement();
		if (!el) {
			return;
		}

		if (el) {
			traverseBfs(el, cell, completionSource, variables);
		}

		const codeMirrorInstance = EditorView.findFromDOM(codeMirrorContainer);

		if (codeMirrorInstance) {
			codeMirrorInstance.contentDOM.onblur = () => {
				applyChangeToCell();
				cell.updateConditionalLabel();
			};
			codeMirrorInstance.contentDOM.onfocus = () => {
				validateStatus = {};
			};
		} else {
			// logger.warn("Unable to find code mirror instance to attach validation handlers");
		}
	});

	const codemirrorCompletion = (ctx: CompletionContext): CompletionResult | null => {
		let nodeBefore = syntaxTree(ctx.state).resolveInner(ctx.pos, -1);

		if (nodeBefore.type.name !== "Identifier") {
			return null;
		}

		let textBefore = ctx.state.sliceDoc(nodeBefore.from, ctx.pos);

		let completionList: string[] = completionSource;
		// On explicit call, show all completion
		if (!ctx.explicit) {
			completionList = completionList.filter((source) => source?.startsWith(textBefore));
		}

		return {
			from: nodeBefore.from,
			options: completionList.map((source) => {
				const label = source;
				return {
					label,
					type: "variable",
				};
			}),
		};
	};

	const applyChangeToCell = async () => {
		if (value) {
			super_validate_condition_form.submit(document.getElementById("validate_condition_form"));
		} else {
			validateStatus = {};
			cell.removeProp(path);
			cell.removeProp(path + "Tmp");
			cell.removeProp(path + "Validate");
		}
	};

	const errorLinter = cmlint.linter((view) => {
		let diagnostics: cmlint.Diagnostic[] = [];
		syntaxTree(view.state)
			.cursor()
			.iterate((_node) => {
				if (validateStatus?.data) {
					const fromIndex = validateStatus.data[0].Column;
					const toIndex = getEndIndex(value, fromIndex);
					if (toIndex !== -1) {
						diagnostics.push({
							from: fromIndex,
							to: toIndex,
							severity: "error",
							message: validateStatus.data[0].Message,
						});
					} else {
						diagnostics = [];
					}
				} else {
					diagnostics = [];
				}
			});
		return removeDuplicates(diagnostics, "message");
	});
</script>

<form id="validate_condition_form" method="POST" action="?/validate_condition" use:validate_condition_enhance></form>
<div bind:this={codeMirrorContainer}>
	<Label>Expression</Label>
	<div class="flex">
		<CodeMirror
			class="text-sm normal-case flex-auto"
			bind:value
			basic={false}
			theme={oneDark}
			extensions={[
				expression(),
				highlightSpecialChars(),
				syntaxHighlighting(defaultHighlightStyle, { fallback: true }),
				highlightActiveLine(),
				highlightActiveLineGutter(),
				autocompletion({
					activateOnTyping: true,
					override: [codemirrorCompletion],
				}),
				bracketMatching(),
				closeBrackets(),
				keymap.of([...defaultKeymap, ...completionKeymap]),
				errorLinter,
			]}
		/>
	</div>
	{#if validateStatus?.errors}
		<div class="text-destructive text-xs normal-case whitespace-normal">
			${validateStatus.errors[0]?.message}
		</div>
	{/if}
</div>
