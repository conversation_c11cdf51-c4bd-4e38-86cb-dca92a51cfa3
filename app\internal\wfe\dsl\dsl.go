package dsl

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"maps"
	"reflect"
	"slices"
	"strings"
	"sync"
	"time"

	"github.com/expr-lang/expr"
	aohlog "github.com/mssfoobar/aoh-golib/logger"
	"go.temporal.io/sdk/client"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
)

type Config struct {
	Client client.Client
}

var (
	conf Config
)

// SetConfig sets the global configuration for the workflow
func SetConfig(c Config) {
	conf = c
}

type (
	// Workflow is the representation of workflow definition.
	Workflow struct {
		Variables map[string]interface{}
		Start     string
		States    []Statement
	}

	// Statement is the building block of dsl workflow.
	Statement struct {
		Name         string
		Next         string
		Activity     *ActivityInvocation
		Parallel     *Parallel
		Switch       *Switch
		Form         *Form
		Event        *Event
		End          *End
		CallActivity *CallActivity
	}

	// Parallel has branches that run in parallel.
	Parallel struct {
		Branches []Branch
	}

	// <PERSON> defined the name of the Next state to execute
	Branch struct {
		Next string
	}

	// Switch has conditional Cases and <PERSON><PERSON>ult
	Switch struct {
		Cases   []*Case
		Default *Default
	}

	// <PERSON><PERSON><PERSON> defined the name of the Next state to execute
	Default struct {
		Next string
	}

	// Case defined the Next state to execute by evaluating Conditional
	Case struct {
		Name        string
		Next        string
		Conditional Expression
	}

	// Expression has Basic & Advance mode
	Expression struct {
		Basic   *Basic
		Advance *Advance
	}

	// Basic defined simple lhs & rhs operation
	Basic struct {
		Input    string
		Operator string
		Value    any
	}

	// Advance defined expression string
	Advance struct {
		Expression string
	}

	// ActivityInvocation is used to express invoking an Activity.
	ActivityInvocation struct {
		ActivityBase
		Type    string
		Options *ActivityInvocationOptions
	}

	// ActivityInvocationOptions is an optional field used to set activity options
	ActivityInvocationOptions struct {
		TaskQueue              string
		HeartbeatTimeout       uint32
		ScheduleToCloseTimeout uint32
		ScheduleToStartTimeout uint32
		StartToCloseTimeout    uint32
		WaitForCancellation    bool
	}

	// ActivityBase is the base of all activity invocation.
	// The Arguments defined expected arguments as input to the Activity.
	// The Result specify the name of variable that it will store the result which can then be used as
	// arguments to subsequent workflow states.
	// The BoundaryEvents specify the list of events that can interrupt the execution of the activity.
	ActivityBase struct {
		Arguments      []string
		BoundaryEvents []string
		Result         string
	}

	// Event defined the execution of the activity that waits for events to occur.
	// The Arguments defined the list of the variable names that can be passed into the event.
	// Event can be interrupting or non-interrupting set by Interrupting.
	// When the event is finished, its output is stored inside the Result variable name.
	Event struct {
		Type         string
		Arguments    []string
		Interrupting bool
		Result       string
	}

	// Form defined the execution of the child workflow to wait for submission signal from client.
	// The Arguments expect formJSON variable name. Submitted formJSON value is stored inside the Result variable name.
	Form struct {
		ActivityBase
	}

	// CallActivity defined the execution of the child workflow defined in the Arguments and output will be stored
	// inside the Result variable name.
	CallActivity struct {
		ActivityBase
	}

	// End defined the end of the workflow execution.
	// When Terminate is true, it will terminate the whole workflow.
	End struct {
		Terminate bool
	}

	// Executable is the interface of the workflow statement to be executed
	// It is implemented by ActivityInvocation, Parallel, Switch, Form, Event, CallActivity, End
	executable interface {
		execute(
			ctx workflow.Context,
			bindings map[string]interface{},
			name string,
			next string,
			states []Statement,
		) error
	}
)

// ExecuteWorkflow starts the execution of the workflow defined in `dslWorkflow` parameter.
func ExecuteWorkflow(ctx workflow.Context, dslWorkflow Workflow) (any, error) {
	workflowId := workflow.GetInfo(ctx).WorkflowExecution.ID
	aohlog.Info("[ExecuteWorkflow] workflow execution started", zap.String("workflow_id", workflowId))
	aohlog.Debug(
		"[ExecuteWorkflow] workflow execution details",
		zap.String("workflow_id", workflowId),
		zap.Any("workflow_dsl", dslWorkflow),
	)

	bindings := make(map[string]interface{})
	//workflowcheck:ignore Only iterates for building another map
	for k, v := range dslWorkflow.Variables {
		bindings[k] = v
	}

	var err error
	idx := slices.IndexFunc(
		dslWorkflow.States,
		func(s Statement) bool { return s.Name == dslWorkflow.Start },
	)

	state := dslWorkflow.States[idx]
	if err = state.execute(ctx, bindings, "", state.Next, dslWorkflow.States); err != nil {
		aohlog.Error("workflow execution failed", zap.String("workflow_id", workflowId), zap.Error(err))
		return nil, err
	}
	aohlog.Info("[ExecuteWorkflow] workflow execution completed", zap.String("workflow_id", workflowId))

	return bindings, err
}

// RecoverWorkflow attempts to recover workflow error by waiting for user input via signal.
// User can provide a path to recover from.
// Currently, the only error handling is when the switch condition is not met.
func RecoverWorkflow(ctx workflow.Context, input []string, name string) (string, error) {
	workflowId := workflow.GetInfo(ctx).WorkflowExecution.ID
	parentId := getParentId(ctx)
	aohlog.Debug(
		"[RecoverWorkflow] Started",
		zap.String("workflow_id", workflowId),
		zap.String("parent_workflow_id", parentId),
		zap.Any("input", input),
		zap.String("name", name),
	)

	if len(input) == 0 {
		return "", errors.New("no path to recover")
	}

	var result string
	ok := false

	for !ok {
		var userInput any
		cancel := false
		selector := workflow.NewSelector(ctx)

		// add user input signal channel with blocking call
		selector.AddReceive(workflow.GetSignalChannel(ctx, name), func(c workflow.ReceiveChannel, _ bool) {
			c.Receive(ctx, &userInput)
		})

		// add ctx done channel
		selector.AddReceive(ctx.Done(), func(channel workflow.ReceiveChannel, _ bool) {
			cancel = true
		})

		// only check for channel that got invoked first
		selector.Select(ctx)

		if cancel {
			aohlog.Debug(
				"[RecoverWorkflow] workflow cancelled",
				zap.String("workflow_id", workflowId),
				zap.String("parent_workflow_id", parentId),
			)
			break
		}

		result, ok = userInput.(string)
		if !ok {
			aohlog.Warn(
				"[RecoverWorkflow] invalid input. expected string",
				zap.String("workflow_id", workflowId),
				zap.String("parent_workflow_id", parentId),
			)
		}
	}
	return result, nil
}

// FormWorkflow wait for the user input with a given signal channel name
// First element of the input array must be form-js json and second element
// is an endpoint string for the form to be submitted
func FormWorkflow(ctx workflow.Context, form FormActivity) (interface{}, error) {
	workflowId := workflow.GetInfo(ctx).WorkflowExecution.ID
	parentId := getParentId(ctx)
	aohlog.Debug(
		"[FormWorkflow] Start",
		zap.String("workflow_id", workflowId),
		zap.String("parent_workflow_id", parentId),
		zap.Any("form", form),
	)

	result := make(map[string]any)
	input := make(map[string]any)

	if form.Name == "" || len(form.Schema.Components) == 0 {
		return nil, errors.New("empty form data")
	}

	if form.Input != nil {
		result = form.Input
	}

	ok := false
	for !ok {
		var userInput any
		cancel := false
		selector := workflow.NewSelector(ctx)

		// add user input signal channel with blocking call
		selector.AddReceive(workflow.GetSignalChannel(ctx, form.Name), func(c workflow.ReceiveChannel, _ bool) {
			c.Receive(ctx, &userInput)
		})

		// add ctx done channel
		selector.AddReceive(ctx.Done(), func(channel workflow.ReceiveChannel, _ bool) {
			cancel = true
		})

		// only check for channel that got invoked first
		selector.Select(ctx)

		if cancel {
			aohlog.Debug(
				"[FormWorkflow] workflow cancelled",
				zap.String("workflow_id", workflowId),
				zap.String("parent_workflow_id", parentId),
			)
			break
		}

		input, ok = userInput.(map[string]any)
		if !ok {
			aohlog.Warn(
				"[FormWorkflow] invalid input. expected map[string]interface{}",
				zap.String("workflow_id", workflowId),
				zap.String("parent_workflow_id", parentId),
			)
		}
	}
	maps.Copy(result, input)
	return result, nil
}

// ChildExecuteWorkflow run ExecuteWorkflow as a child workflow
func ChildExecuteWorkflow(ctx workflow.Context, dslWorkflow Workflow) (any, error) {
	return ExecuteWorkflow(ctx, dslWorkflow)
}

func (b Statement) execute(
	ctx workflow.Context,
	bindings map[string]interface{},
	_ string,
	next string,
	states []Statement,
) error {
	if b.Activity != nil {
		return b.Activity.execute(ctx, bindings, b.Name, next, states)
	}

	if b.CallActivity != nil {
		return b.CallActivity.execute(ctx, bindings, b.Name, next, states)
	}

	if b.Form != nil {
		return b.Form.execute(ctx, bindings, b.Name, next, states)
	}

	if b.Event != nil {
		return b.Event.execute(ctx, bindings, b.Name, next, states)
	}

	if b.Parallel != nil {
		return b.Parallel.execute(ctx, bindings, b.Name, next, states)
	}

	if b.Switch != nil {
		return b.Switch.execute(ctx, bindings, b.Name, next, states)
	}

	if b.End != nil {
		return b.End.execute(ctx, bindings, b.Name, next, states)
	}

	return nil
}

func (f Form) execute(
	ctx workflow.Context,
	bindings map[string]interface{},
	name string,
	next string,
	states []Statement,
) error {
	workflowId := workflow.GetInfo(ctx).WorkflowExecution.ID
	aohlog.Debug("[Form] start", zap.String("workflow_id", workflowId), zap.String("name", name))

	var formSchema FormSchema

	inputParam, err := makeInput(f.Arguments, bindings)
	if err != nil {
		aohlog.Warn(
			"[Form] unable to get form from input",
			zap.String("workflow_id", workflowId),
			zap.String("name", name),
			zap.Error(err),
		)
	}

	jsonMap, ok := inputParam[0].(map[string]any)
	if !ok {
		return errors.New("invalid form dsl")
	}

	jsonBody, err := json.Marshal(jsonMap)
	if err != nil {
		return err
	}

	if err := json.Unmarshal(jsonBody, &formSchema); err != nil {
		return err
	}

	// ignore first element which is a formSchema that doesn't need to bind data
	formInput := createFormInput(name, inputParam[1:], f.Arguments[1:])
	formSchema.bindData(formInput)

	childCtx, cancelHandler := workflow.WithCancel(ctx)
	childCtx = workflow.WithChildOptions(childCtx, setChildWorkflowOption(name))
	future := workflow.ExecuteChildWorkflow(childCtx, FormWorkflow, FormActivity{name, formSchema, formInput})

	output, next, err := f.ActivityBase.execute(childCtx, cancelHandler, future, bindings, next, states)
	if err != nil {
		return err
	}

	if f.Result != "" {
		bindings[f.Result] = output
	}

	if err := executeNext(ctx, bindings, next, states); err != nil {
		return err
	}
	return nil
}

func (c CallActivity) execute(
	ctx workflow.Context,
	bindings map[string]interface{},
	name string,
	next string,
	states []Statement,
) error {
	workflowId := workflow.GetInfo(ctx).WorkflowExecution.ID
	aohlog.Debug("[CallActivity] start", zap.String("workflow_id", workflowId), zap.String("name", name))

	var dslWorkflow Workflow
	inputParam, err := makeInput(c.Arguments, bindings)
	if err != nil {
		aohlog.Warn(
			"[CallActivity] unable to get workflow from input",
			zap.String("workflow_id", workflowId),
			zap.String("name", name),
			zap.Error(err),
		)
	}

	jsonMap, ok := inputParam[0].(map[string]any)
	if !ok {
		return errors.New("invalid workflow dsl")
	}

	jsonBody, err := json.Marshal(jsonMap)
	if err != nil {
		return err
	}

	if err := json.Unmarshal(jsonBody, &dslWorkflow); err != nil {
		return err
	}

	childCtx, cancelHandler := workflow.WithCancel(ctx)
	childCtx = workflow.WithChildOptions(childCtx, setChildWorkflowOption(name))
	future := workflow.ExecuteChildWorkflow(childCtx, ChildExecuteWorkflow, dslWorkflow)

	output, next, err := c.ActivityBase.execute(childCtx, cancelHandler, future, bindings, next, states)
	if err != nil {
		return err
	}

	if c.Result != "" {
		bindings[c.Result] = output
	}

	if err := executeNext(ctx, bindings, next, states); err != nil {
		return err
	}

	return nil
}

func (a ActivityInvocation) execute(
	ctx workflow.Context,
	bindings map[string]interface{},
	name string,
	next string,
	states []Statement,
) error {
	workflowId := workflow.GetInfo(ctx).WorkflowExecution.ID
	aohlog.Debug("[Activity] start", zap.String("workflow_id", workflowId), zap.String("name", name))

	inputParam, err := makeInput(a.Arguments, bindings)
	if err != nil {
		aohlog.Warn(
			"[Activity] unable to get activity parameter from input",
			zap.String("workflow_id", workflowId),
			zap.String("name", name),
			zap.Error(err),
		)
	}

	childCtx, cancelHandler := workflow.WithCancel(ctx)
	aCtx := workflow.WithActivityOptions(childCtx, setActivityOption(name, a.Options))
	future := workflow.ExecuteActivity(aCtx, a.Type, inputParam...)

	output, next, err := a.ActivityBase.execute(childCtx, cancelHandler, future, bindings, next, states)
	if err != nil {
		return err
	}

	if a.Result != "" {
		bindings[a.Result] = output
	}

	if err := executeNext(ctx, bindings, next, states); err != nil {
		return err
	}

	return nil
}

func (p Parallel) execute(
	ctx workflow.Context,
	bindings map[string]interface{},
	name string,
	next string,
	states []Statement,
) error {
	workflowId := workflow.GetInfo(ctx).WorkflowExecution.ID
	aohlog.Debug("[Parallel] start", zap.String("workflow_id", workflowId), zap.String("name", name))

	// In the parallel block, we want to execute all of them in parallel and wait for all of them.
	// if one activity fails then we want to cancel all the rest of them as well.
	childCtx, cancelHandler := workflow.WithCancel(ctx)
	selector := workflow.NewSelector(ctx)
	var activityErr error

	for _, b := range p.Branches {
		idx := slices.IndexFunc(
			states,
			func(s Statement) bool { return s.Name == b.Next },
		)

		state := states[idx]

		f := executeAsync(state, childCtx, bindings, state.Name, state.Next, states)
		selector.AddFuture(f, func(f workflow.Future) {
			err := f.Get(ctx, nil)
			if err != nil {
				// cancel all pending activities
				cancelHandler()
				activityErr = err
			}
		})
	}

	for i := 0; i < len(p.Branches); i++ {
		selector.Select(ctx) // this will wait for one branch
		if activityErr != nil {
			return activityErr
		}
	}

	if err := executeNext(ctx, bindings, next, states); err != nil {
		return err
	}

	return nil
}

func (s Switch) execute(
	ctx workflow.Context,
	bindings map[string]interface{},
	name string,
	_ string,
	states []Statement,
) error {
	workflowId := workflow.GetInfo(ctx).WorkflowExecution.ID
	aohlog.Debug("[Switch] start", zap.String("workflow_id", workflowId), zap.String("name", name))

	if ok, err := s.evaluateCases(ctx, bindings, states); ok {
		return err
	}

	ctx = workflow.WithChildOptions(ctx, setChildWorkflowOption(name))
	var result string

	err := workflow.ExecuteChildWorkflow(ctx, RecoverWorkflow, findNextActivities(name, states), name).Get(ctx, &result)
	if err != nil {
		return err
	}

	for _, c := range s.Cases {
		for _, activity := range findNextActivities(c.Next, states) {
			if activity == result {
				if err := executeNext(ctx, bindings, c.Next, states); err != nil {
					return err
				}
			}
		}
	}

	return nil
}

func (s Switch) evaluateCases(
	ctx workflow.Context,
	bindings map[string]interface{},
	states []Statement,
) (bool, error) {
	workflowId := workflow.GetInfo(ctx).WorkflowExecution.ID
	for _, c := range s.Cases {
		if ok, err := evaluateCase(*c, bindings); err != nil {
			aohlog.Warn(
				"[Switch] invalid switch condition",
				zap.String("workflow_id", workflowId),
				zap.String("case_name", c.Name),
				zap.Error(err),
			)
			continue
		} else if ok {
			return true, executeNext(ctx, bindings, c.Next, states)
		}
	}

	if s.Default != nil && len(s.Default.Next) != 0 {
		return true, executeNext(ctx, bindings, s.Default.Next, states)
	}

	return false, errors.New("no case & default matched")
}

func (e Event) execute(
	ctx workflow.Context,
	bindings map[string]interface{},
	name string,
	next string,
	states []Statement,
) error {
	workflowId := workflow.GetInfo(ctx).WorkflowExecution.ID
	aohlog.Debug("[Event] start", zap.String("workflow_id", workflowId), zap.String("name", name))

	var result interface{}

	inputParam, err := makeInput(e.Arguments, bindings)
	if err != nil {
		aohlog.Warn(
			"[Event] invalid event arguments",
			zap.String("workflow_id", workflowId),
			zap.String("name", name),
			zap.Error(err),
		)
	}

	ctx = workflow.WithActivityOptions(ctx, setEventOption(name))

	if err := workflow.ExecuteActivity(ctx, e.Type, inputParam...).Get(ctx, &result); err != nil {
		return err
	}

	if e.Result != "" {
		bindings[e.Result] = result
	}

	if !isBoundaryEvent(name, states) {
		if err := executeNext(ctx, bindings, next, states); err != nil {
			return err
		}
	}

	return nil
}

func (e End) execute(
	ctx workflow.Context,
	_ map[string]interface{},
	name string,
	_ string,
	_ []Statement,
) error {
	workflow.SideEffect(ctx, func(ctx workflow.Context) interface{} {
		return struct {
			SideEffectType string `json:"sideEffectType"`
			SideEffectId   string `json:"sideEffectId"`
		}{SideEffectType: "End", SideEffectId: name}
	})

	if e.Terminate {
		workflowId := workflow.GetInfo(ctx).WorkflowExecution.ID
		runId := workflow.GetInfo(ctx).WorkflowExecution.RunID
		newCtx, _ := workflow.NewDisconnectedContext(ctx)
		workflow.Go(newCtx, func(ctx workflow.Context) {
			//necessary wait before terminating for workflow to create any pending events
			_ = workflow.Sleep(ctx, time.Second*3) //nolint:errcheck
			ctxWTO, cancel := context.WithTimeout(context.Background(), time.Second*10)
			defer cancel()
			err := conf.Client.TerminateWorkflow(ctxWTO, workflowId, runId, TerminateEndEvent)
			if err != nil {
				aohlog.Error("unable to terminate workflow", zap.Error(err))
			}
		})
	}

	return nil
}

func (a ActivityBase) execute(
	ctx workflow.Context,
	cancelFunc workflow.CancelFunc,
	future workflow.Future,
	bindings map[string]any,
	next string,
	states []Statement,
) (any, string, error) {
	var output any
	var cancelByActivity, cancelByEvent bool
	selector := workflow.NewSelector(ctx)
	pendingFutures := []workflow.Future{future}

	selector.AddFuture(future, func(f workflow.Future) {
		cancelFunc()
		cancelByActivity = true
	})

	var once sync.Once
	var eventError error

	for _, event := range a.BoundaryEvents {
		idx := slices.IndexFunc(states, func(s Statement) bool { return s.Name == event })
		state := states[idx]

		f := executeAsync(state, ctx, bindings, state.Name, state.Next, states)
		pendingFutures = append(pendingFutures, f)

		selector.AddFuture(f, func(f workflow.Future) {
			switch {
			case cancelByEvent:
			case cancelByActivity:
			case state.Event.Interrupting:
				// only first interrupted event should set 'next' state
				once.Do(func() {
					next = state.Next
					cancelByEvent = true
				})
				cancelFunc()
			default:
				// non-interrupting event proceed to execute its next state
				if err := executeNext(ctx, bindings, state.Next, states); err != nil {
					eventError = errors.Join(eventError, err)
				}
			}
		})
	}

	for range pendingFutures {
		selector.Select(ctx)
	}

	// catch if there is any error from states executed by boundary event
	if eventError != nil {
		return nil, next, eventError
	}

	// if not cancel by event, wait for activity execution
	if !cancelByEvent {
		if err := future.Get(ctx, &output); err != nil {
			return nil, next, err
		}
	}

	return output, next, nil
}

// containsBoundaryEvent checks if a given eventName exists in the BoundaryEvents
func (a ActivityBase) containsBoundaryEvent(eventName string) bool {
	return slices.Contains(a.BoundaryEvents, eventName)
}

func executeAsync(
	exe executable,
	ctx workflow.Context,
	bindings map[string]interface{},
	name string,
	next string,
	states []Statement,
) workflow.Future {
	future, settable := workflow.NewFuture(ctx)

	workflow.Go(ctx, func(ctx workflow.Context) {
		err := exe.execute(ctx, bindings, name, next, states)
		settable.Set(nil, err)
	})

	return future
}

func makeInput(argNames []string, argsMap map[string]interface{}) ([]interface{}, error) {
	var args []interface{}
	var err error

	for _, arg := range argNames {
		v, ok := argsMap[arg]
		if ok {
			if reflect.TypeOf(v).Kind() == reflect.String {
				resultVar := v.(string)

				if strings.HasPrefix(resultVar, "=") {
					var e error
					s := strings.TrimPrefix(resultVar, "=")

					v, e = expr.Eval(s, argsMap)
					if e != nil {
						_ = errors.Join(err, e) //nolint:errcheck
					}
				}
			}
		}
		args = append(args, v)
	}

	return args, err
}

func compare[T float64 | string](op string, lhs, rhs T) (bool, error) {
	switch op {
	case EQ:
		return lhs == rhs, nil
	case NEQ:
		return lhs != rhs, nil
	case MT:
		return lhs > rhs, nil
	case LT:
		return lhs < rhs, nil
	case MTE:
		return lhs >= rhs, nil
	case LTE:
		return lhs <= rhs, nil
	default:
		return false, errors.New("invalid operator")
	}
}

func compareBool(op string, lhs, rhs bool) (bool, error) {
	switch op {
	case EQ:
		return lhs == rhs, nil
	case NEQ:
		return lhs != rhs, nil
	default:
		return false, errors.New("invalid operator")
	}
}

func executeNext(
	ctx workflow.Context,
	bindings map[string]interface{},
	next string,
	states []Statement,
) error {
	var idx int

	if idx = slices.IndexFunc(states, func(s Statement) bool { return s.Name == next }); idx == -1 {
		return nil
	}
	state := states[idx]

	if err := state.execute(ctx, bindings, state.Name, state.Next, states); err != nil {
		return err
	}

	return nil
}

func evaluateCondition(op string, input any, value any) (bool, error) {
	var ok bool
	var err error

	if reflect.TypeOf(input) != reflect.TypeOf(value) {
		return ok, errors.New("mismatched type")
	}

	dataType := reflect.TypeOf(input).Kind()

	switch dataType {
	case reflect.Float64:
		ok, err = compare[float64](op, input.(float64), value.(float64))
		if err != nil {
			return ok, err
		}
	case reflect.String:
		ok, err = compare[string](op, input.(string), value.(string))
		if err != nil {
			return ok, err
		}
	case reflect.Bool:
		ok, err = compareBool(op, input.(bool), value.(bool))
		if err != nil {
			return ok, err
		}
	default:
		return ok, fmt.Errorf("comparing data type %s is not supported", dataType.String())
	}

	return ok, err
}

func findNextActivities(start string, states []Statement) []string {
	var list []string

	for _, v := range states {
		if start == v.Name {
			switch {
			case v.Parallel != nil:
				list = append(list, findParallelNextActivities(v.Parallel.Branches, states)...)
			case v.Switch != nil:
				list = append(list, findSwitchNextActivities(*v.Switch, states)...)
			case v.Event != nil:
				fallthrough
			case v.Activity != nil:
				fallthrough
			case v.Form != nil:
				list = append(list, v.Name)
			}
		}
	}

	return list
}

func findParallelNextActivities(branches []Branch, states []Statement) []string {
	var list []string
	for _, b := range branches {
		list = append(list, findNextActivities(b.Next, states)...)
	}
	return list
}

func findSwitchNextActivities(sw Switch, states []Statement) []string {
	var list []string
	for _, c := range sw.Cases {
		list = append(list, findNextActivities(c.Next, states)...)
	}
	if sw.Default != nil && len(sw.Default.Next) != 0 {
		list = append(list, sw.Default.Next)
	}
	return list
}

// isBoundaryEvent checks if a given eventName exists in the states BoundaryEvents
func isBoundaryEvent(eventName string, states []Statement) bool {
	for _, v := range states {
		if v.Activity != nil {
			return v.Activity.containsBoundaryEvent(eventName)
		}
		if v.CallActivity != nil {
			return v.CallActivity.containsBoundaryEvent(eventName)
		}
		if v.Form != nil {
			return v.Form.containsBoundaryEvent(eventName)
		}
	}
	return false
}

func setChildWorkflowOption(name string) workflow.ChildWorkflowOptions {
	return workflow.ChildWorkflowOptions{
		Memo: map[string]interface{}{
			MemoName: name, // memo is use in workflow history to identify the child workflow associated name
		},
		WaitForCancellation: true,
	}
}

func setActivityOption(name string, options *ActivityInvocationOptions) workflow.ActivityOptions {
	ao := workflow.ActivityOptions{
		ActivityID:          name,
		StartToCloseTimeout: 5 * time.Minute,
		HeartbeatTimeout:    1 * time.Minute, // this need to set to make activity cancellable
		RetryPolicy: &temporal.RetryPolicy{
			MaximumAttempts: 10,
		},
		WaitForCancellation: true, // Wait for cancellation to complete.
	}

	// Set activity options
	if options != nil {
		if options.TaskQueue != "" {
			ao.TaskQueue = options.TaskQueue
		}

		if options.StartToCloseTimeout != 0 {
			ao.StartToCloseTimeout = time.Duration(
				options.StartToCloseTimeout) * time.Second
		}

		if options.ScheduleToCloseTimeout != 0 {
			ao.ScheduleToCloseTimeout = time.Duration(
				options.ScheduleToCloseTimeout) * time.Second
		}

		if options.ScheduleToStartTimeout != 0 {
			ao.ScheduleToStartTimeout = time.Duration(
				options.ScheduleToStartTimeout) * time.Second
		}

		if options.HeartbeatTimeout != 0 {
			ao.HeartbeatTimeout = time.Duration(options.HeartbeatTimeout) * time.Second
		}

		if options.WaitForCancellation {
			ao.WaitForCancellation = true
		}
	}
	return ao
}

func setEventOption(name string) workflow.ActivityOptions {
	return workflow.ActivityOptions{
		ActivityID:          name,
		StartToCloseTimeout: time.Hour * 24 * 365 * 10, // events are long-running, this value should be large enough to avoid time out
		HeartbeatTimeout:    1 * time.Minute,
		WaitForCancellation: true, // wait for cancellation to complete.
	}
}

func exprValidate(exp string, bindings map[string]any) (bool, error) {
	program, err := expr.Compile(
		exp,
		expr.AsBool(),
		expr.Env(bindings),
	)
	if err != nil {
		return false, err
	}

	val, err := expr.Run(program, bindings)
	if err != nil {
		return false, err
	}

	return val.(bool), nil
}

func evaluateCase(c Case, bindings map[string]interface{}) (bool, error) {
	switch {
	case c.Conditional.Basic != nil:
		input := bindings[c.Conditional.Basic.Input]
		value := c.Conditional.Basic.Value
		return evaluateCondition(c.Conditional.Basic.Operator, input, value)
	case c.Conditional.Advance != nil:
		return exprValidate(c.Conditional.Advance.Expression, bindings)
	default:
		return false, errors.New("missing basic or advance condition")
	}
}

func getParentId(ctx workflow.Context) string {
	var parentId string
	if workflow.GetInfo(ctx).ParentWorkflowExecution != nil {
		parentId = workflow.GetInfo(ctx).ParentWorkflowExecution.ID
	}
	return parentId
}
