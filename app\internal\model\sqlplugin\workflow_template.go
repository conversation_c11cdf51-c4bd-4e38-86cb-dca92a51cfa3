package sqlplugin

import (
	"context"
	"database/sql"
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

type (
	WorkflowTemplateRow struct {
		Id           uuid.UUID       `db:"id" json:"id"`
		Name         string          `db:"name" json:"name"`
		WorkflowJson json.RawMessage `db:"workflow_json" json:"workflow_json"`
		DesignerJson json.RawMessage `db:"designer_json" json:"designer_json"`
		Editable     bool            `db:"editable" json:"editable"`
		// mandatory table columns
		CreatedAt time.Time `db:"created_at" json:"created_at"`
		CreatedBy string    `db:"created_by" json:"created_by"`
		UpdatedAt time.Time `db:"updated_at" json:"updated_at"`
		UpdatedBy string    `db:"updated_by" json:"updated_by"`
		TenantId  string    `db:"tenant_id" json:"tenant_id"`
		OccLock   int       `db:"occ_lock" json:"occ_lock"`
	}

	WorkflowTemplateFilter struct {
		Id       *uuid.UUID
		Name     *string
		TenantId string
	}

	WorkflowTemplatePaginateFilter struct {
		TenantId string
		Limit    int
		Offset   int
		OrderBy  string
	}

	WorkflowTemplateCountFilter struct {
		TenantId string
	}

	WorkflowTemplate interface {
		InsertIntoWorkflowTemplate(ctx context.Context, rows *WorkflowTemplateRow) (sql.Result, error)
		UpdateWorkflowTemplate(ctx context.Context, rows *WorkflowTemplateRow) (sql.Result, error)
		SelectFromWorkflowTemplate(ctx context.Context, filter WorkflowTemplateFilter) (*WorkflowTemplateRow, error)
		ListFromWorkflowTemplate(ctx context.Context, filter WorkflowTemplatePaginateFilter) ([]WorkflowTemplateRow, error)
		DeleteFromWorkflowTemplate(ctx context.Context, filter WorkflowTemplateFilter) (sql.Result, error)
		CountFromWorkflowTemplate(ctx context.Context, filter WorkflowTemplateCountFilter) (int, error)
	}
)
