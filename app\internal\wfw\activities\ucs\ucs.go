package ucs

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/mssfoobar/app/wfe/internal/config"
	"go.temporal.io/sdk/activity"
)

const (
	createRoomEndpoint    = `%s/v1/rooms`
	endRoomEndpoint       = `%s/v1/rooms/{roomid}`
	broadcastRoomEndpoint = `%s/v1/rooms/{roomid}`
	incidentSystemAccount = "incident-system-account"
)

type Activities struct {
	createRoomApi    string
	endRoomApi       string
	broadcastRoomApi string
}

type createRoomRequest struct {
	Requester string
	Name      string
	StartTime string
	EndTime   string
}

type createRoomResponse struct {
	Data []struct {
		Id   string `json:"id"`
		Name string `json:"name"`
	} `json:"data"`
}

type broadcastRoomRequest struct {
	Requestor     string `json:"requestor"`
	Announcements []struct {
		Message               string `json:"message"`
		RelativeFrom          string `json:"relative_from"`
		RelativeTimeInSeconds int    `json:"relative_time_in_seconds"`
	} `json:"announcements"`
}

type endRoomRequest struct {
	Requestor         string `json:"requestor"`
	TimeLeftInSeconds int    `json:"time_left_in_seconds"`
	Reason            string `json:"reason"`
}

func New(opt config.Ucs) *Activities {
	return &Activities{
		createRoomApi:    fmt.Sprintf(createRoomEndpoint, opt.Url),
		endRoomApi:       fmt.Sprintf(endRoomEndpoint, opt.Url),
		broadcastRoomApi: fmt.Sprintf(broadcastRoomEndpoint, opt.Url),
	}
}

// CreateUCSRoom call Activities endpoint to create chat room with incidentId as room name
func (a *Activities) CreateUCSRoom(ctx context.Context, name string) (string, error) {
	room := createRoomRequest{
		Requester: incidentSystemAccount,
		Name:      name,
		StartTime: time.Now().Format(time.RFC3339),
		EndTime:   time.Now().AddDate(1, 0, 0).Format(time.RFC3339),
	}

	b, err := json.Marshal(room)
	if err != nil {
		return "", err
	}

	client := &http.Client{}
	req, err := http.NewRequest(http.MethodPost, a.createRoomApi, bytes.NewReader(b))
	if err != nil {
		return "", err
	}

	// heartbeat report before long-running process
	activity.RecordHeartbeat(ctx)

	res, err := client.Do(req)
	if err != nil {
		return "", err
	}

	activity.RecordHeartbeat(ctx)

	if res.StatusCode != http.StatusOK {
		return "", errors.New("failed to create room")
	}

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return "", err
	}
	func() {
		_ = res.Body.Close()
	}()

	var resp createRoomResponse
	if err := json.Unmarshal(body, &resp); err != nil {
		return "", err
	}

	if len(resp.Data) == 0 {
		return "", errors.New("no response data")
	}

	// catch cancellation from workflow by checking ctx.Done
	select {
	case <-ctx.Done():
		return "", ctx.Err()
	default:
	}

	return resp.Data[0].Id, nil
}

// BroadcastUCSAnnouncement call Activities endpoint to send announcement inside chat room
func (a *Activities) BroadcastUCSAnnouncement(ctx context.Context, roomId string, message string) error {
	endpoint := strings.Replace(a.broadcastRoomApi, "{roomid}", roomId, -1)
	request := broadcastRoomRequest{
		Requestor: incidentSystemAccount,
		Announcements: []struct {
			Message               string `json:"message"`
			RelativeFrom          string `json:"relative_from"`
			RelativeTimeInSeconds int    `json:"relative_time_in_seconds"`
		}{
			{
				Message:               message,
				RelativeFrom:          "start",
				RelativeTimeInSeconds: 0,
			},
		},
	}

	b, err := json.Marshal(request)
	if err != nil {
		return err
	}

	client := &http.Client{}
	req, err := http.NewRequest(http.MethodPatch, endpoint, bytes.NewReader(b))
	if err != nil {
		return err
	}

	// heartbeat report before long-running process
	activity.RecordHeartbeat(ctx)

	var res *http.Response
	res, err = client.Do(req)
	if err != nil {
		return err
	}

	activity.RecordHeartbeat(ctx)

	if res.StatusCode != http.StatusOK {
		return errors.New("failed to send announcement")
	}

	// catch cancellation from workflow by checking ctx.Done
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}

	return nil
}

// EndUCSChatRoom call Activities endpoint to end chat room
func (a *Activities) EndUCSChatRoom(ctx context.Context, roomId string) error {
	endpoint := strings.Replace(a.endRoomApi, "{roomid}", roomId, -1)
	request := endRoomRequest{
		Requestor:         incidentSystemAccount,
		TimeLeftInSeconds: 0,
		Reason:            "session ended",
	}

	b, err := json.Marshal(request)
	if err != nil {
		return err
	}

	client := &http.Client{}
	req, err := http.NewRequest(http.MethodDelete, endpoint, bytes.NewReader(b))
	if err != nil {
		return err
	}

	// heartbeat report before long-running process
	activity.RecordHeartbeat(ctx)

	var res *http.Response
	res, err = client.Do(req)
	if err != nil {
		return err
	}

	activity.RecordHeartbeat(ctx)

	if res.StatusCode != http.StatusOK {
		return errors.New("failed to end chat room")
	}

	// catch cancellation from workflow by checking ctx.Done
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}

	return nil
}
