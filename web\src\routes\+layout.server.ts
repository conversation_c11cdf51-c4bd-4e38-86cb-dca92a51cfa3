/** @type {import('./$types').LayoutLoad} */
import dayjs from "dayjs";
import Duration from "dayjs/plugin/duration";
import { redirect } from "@sveltejs/kit";
import { env } from "$env/dynamic/private";
import { LOGIN_API } from "$lib/aoh/core/provider/auth/auth.js";
import { StatusCodes } from "http-status-codes";

dayjs.extend(Duration);

export async function load({ url }) {
	const LOGIN_PAGE = env.LOGIN_PAGE ? env.LOGIN_PAGE : LOGIN_API;
	if (url.pathname === "/") {
		redirect(StatusCodes.TEMPORARY_REDIRECT, LOGIN_PAGE);
	}
}
